<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hb.postgre.persistence.custtrade.SalesVolumeNewMapper">
	<cache type="org.mybatis.caches.oscache.OSCache"/>

    <select id="listSalesVolumeReport" parameterType="Map" resultType="com.hb.crm.domain.report.fundnavreport.SalesVolume" useCache="false">

        SELECT
        zz1.orgcode as  orgCode,
        zz1.orgname as  orgName,
        zz1.conscode as consCode,
        zz1.consname as consName,
        zz1.conscustno as  conscustNo,
        zz1.custname as conscustName,
        zz2.first_PayDt as firstPayDt,
        zz1.PAYINSUREAMT as payRmb,
        zz1.DISPAYINSUREAMT as payDiscountRmb,
        zz1.collamk as innovativeDiscountRmb
        FROM
        (SELECT
        mmm.orgcode,
        mmm.orgname,
        mmm.conscode,
        mmm.consname,
        mmm.conscustno,
        mmm.custname,
        sum(mmm.PAYINSUREAMT) as PAYINSUREAMT,
        sum(mmm.DISPAYINSUREAMT) as DISPAYINSUREAMT ,
        sum(mmm.collamk) as collamk
        FROM(
        SELECT
        m10.orgcode,
        m10.orgname,
        m9.conscode,
        m9.consname,
        m1.conscustno,
        m7.custname,
        CASE  WHEN A.PAYSTATE = '3' AND A.CURRENCY = '156' THEN coalesce(A.REALPAYAMT, 0) / 10000
        WHEN A.PAYSTATE = '3' AND A.CURRENCY != '156' THEN coalesce(A.REALPAYAMT, 0) / 10000 * coalesce(A.BUYAMT_RMB, 0) / coalesce(A.BUYAMT, 0)
        ELSE 0 END PAYINSUREAMT,
        CASE  WHEN A.PAYSTATE = '3' AND A.CURRENCY = '156' THEN coalesce(A.REALPAYAMT, 0)*M.DISCOUNT_RATE / 10000
        WHEN A.PAYSTATE = '3' AND A.CURRENCY != '156' THEN
        coalesce(A.REALPAYAMT, 0) / 10000 * coalesce(A.BUYAMT_RMB, 0) / coalesce(A.BUYAMT, 0)*M.DISCOUNT_RATE
        ELSE 0 END DISPAYINSUREAMT,
        coalesce(m3.collamk,0) as collamk
        FROM ods.raw_cm_conscust m1
        left join
        (
        SELECT t2.HBTYPE ,t1.PCODE,t1.ID,t1.CURRENCY,t1.BUYAMT,t1.BUYAMT_RMB,t1.PAYSTATE,t1.tradestate,t1.realpayamt,t1.PREBOOKSTATE,t2.PREBOOK_STATE AS jjxx_STATE,t1.EXPECTTRADEDT,t1.CREDT,t1.REALPAYAMTDT,t1.CREATOR,t1.CONSCUSTNO,t1.TRADE_TYPE,t1.RECSTAT
        FROM  ods.raw_cm_prebookproductinfo t1 , dwd.mv_cm_productinfo t2
        WHERE t1.PCODE = t2.PCODE AND coalesce(t2.fccbz,'0') != '1'
        UNION ALL
        SELECT t2.HBTYPE ,t1.PCODE,t1.ID,t1.CURRENCY,t3.totalamt,t3.totalamt,t1.PAYSTATE,t1.tradestate,t3.totalamt,t1.PREBOOKSTATE,t2.PREBOOK_STATE AS jjxx_STATE,t1.EXPECTTRADEDT,t1.CREDT,t1.REALPAYAMTDT,t1.CREATOR,t1.CONSCUSTNO,t1.TRADE_TYPE,t1.RECSTAT
        FROM  ods.raw_cm_prebookproductinfo t1 , dwd.mv_cm_productinfo t2 ,ods.raw_CM_PREBOOK_MANYCALL t3
        WHERE t1.PCODE = t2.PCODE
        AND   t1.ID = t3.firstpreid
        AND   t2.fccbz = '1') a
        on m1.conscustno=a.conscustno
        and a.PREBOOKSTATE='2'
        and a.PAYSTATE='3'

        <if test="tradeBeginDt != null">
            AND A.REALPAYAMTDT &gt;= #{tradeBeginDt}
        </if>
        <if test="tradeEndDt != null">
            AND A.REALPAYAMTDT &lt;= #{tradeEndDt}
        </if>

        left join (SELECT dd.conscustno,sum(dd.collamk)/10000 as collamk FROM dm.dm_trade_ack_insur_gd dd
        where dd.prestate='2'
        and dd.PAYSTATE='1'

        <if test="tradeBeginDt != null">
            AND dd.PAYDT &gt;= #{tradeBeginDt}
        </if>
        <if test="tradeEndDt != null">
            AND dd.PAYDT &lt;= #{tradeEndDt}
        </if>

        group by dd.conscustno
        )m3
        on m1.conscustno=m3.conscustno
        left join ods.raw_cm_conscust m7
        on m1.conscustno=m7.conscustno
        left join ods.raw_cm_custconstant m8
        on a.conscustno=m8.custno
        left join ods.raw_cm_consultant m9
        on m8.conscode=m9.conscode
        left join ods.raw_hb_organization m10
        on m9.outletcode=m10.orgcode
        left join dm.dm_ic_sales_rank m
        on a.id=m.id
        ) mmm
        group by  mmm.orgcode,
        mmm.orgname,
        mmm.conscode,
        mmm.consname,
        mmm.conscustno,
        mmm.custname)zz1
        inner join dm.dm_conscust_first_paydt zz2
        on zz1.conscustno=zz2.conscustno
        left join ods.raw_cm_consultant D
        on zz1.conscode=D.conscode
        where zz1.orgcode is not null

        <if test="conscode != null and conscode !=''">
            AND D.conscode = #{conscode}
        </if>
        <if test="teamcode != null">
            AND D.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND D.OUTLETCODE = #{othertearm} and D.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND D.OUTLETCODE IN (${outletcodes})
        </if>

    </select>
    
    <select id="listSalesVolumeReportByPage" resultType="com.hb.crm.domain.report.fundnavreport.SalesVolume" useCache="false">
        SELECT
        zz1.orgcode as  orgCode,
        zz1.orgname as  orgName,
        zz1.conscode as consCode,
        zz1.consname as consName,
        zz1.conscustno as  conscustNo,
        zz1.custname as conscustName,
        zz2.first_PayDt as firstPayDt,
        zz1.PAYINSUREAMT as payRmb,
        zz1.DISPAYINSUREAMT as payDiscountRmb,
        zz1.collamk as innovativeDiscountRmb
        FROM
        (SELECT
        mmm.orgcode,
        mmm.orgname,
        mmm.conscode,
        mmm.consname,
        mmm.conscustno,
        mmm.custname,
        sum(mmm.PAYINSUREAMT) as PAYINSUREAMT,
        sum(mmm.DISPAYINSUREAMT) as DISPAYINSUREAMT ,
        sum(mmm.collamk) as collamk
        FROM(
        SELECT
        m10.orgcode,
        m10.orgname,
        m9.conscode,
        m9.consname,
        m1.conscustno,
        m7.custname,
        CASE  WHEN A.PAYSTATE = '3' AND A.CURRENCY = '156' THEN coalesce(A.REALPAYAMT, 0) / 10000
        WHEN A.PAYSTATE = '3' AND A.CURRENCY != '156' THEN coalesce(A.REALPAYAMT, 0) / 10000 * coalesce(A.BUYAMT_RMB, 0) / coalesce(A.BUYAMT, 0)
        ELSE 0 END PAYINSUREAMT,
        CASE  WHEN A.PAYSTATE = '3' AND A.CURRENCY = '156' THEN coalesce(A.REALPAYAMT, 0)*M.DISCOUNT_RATE / 10000
        WHEN A.PAYSTATE = '3' AND A.CURRENCY != '156' THEN
        coalesce(A.REALPAYAMT, 0) / 10000 * coalesce(A.BUYAMT_RMB, 0) / coalesce(A.BUYAMT, 0)*M.DISCOUNT_RATE
        ELSE 0 END DISPAYINSUREAMT,
        coalesce(m3.collamk,0) as collamk
        FROM ods.raw_cm_conscust m1
        left join
        (
        SELECT t2.HBTYPE ,t1.PCODE,t1.ID,t1.CURRENCY,t1.BUYAMT,t1.BUYAMT_RMB,t1.PAYSTATE,t1.tradestate,t1.realpayamt,t1.PREBOOKSTATE,t2.PREBOOK_STATE AS jjxx_STATE,t1.EXPECTTRADEDT,t1.CREDT,t1.REALPAYAMTDT,t1.CREATOR,t1.CONSCUSTNO,t1.TRADE_TYPE,t1.RECSTAT
        FROM  ods.raw_cm_prebookproductinfo t1 , dwd.mv_cm_productinfo t2
        WHERE t1.PCODE = t2.PCODE AND coalesce(t2.fccbz,'0') != '1'
        UNION ALL
        SELECT t2.HBTYPE ,t1.PCODE,t1.ID,t1.CURRENCY,t3.totalamt,t3.totalamt,t1.PAYSTATE,t1.tradestate,t3.totalamt,t1.PREBOOKSTATE,t2.PREBOOK_STATE AS jjxx_STATE,t1.EXPECTTRADEDT,t1.CREDT,t1.REALPAYAMTDT,t1.CREATOR,t1.CONSCUSTNO,t1.TRADE_TYPE,t1.RECSTAT
        FROM  ods.raw_cm_prebookproductinfo t1 , dwd.mv_cm_productinfo t2 ,ods.raw_CM_PREBOOK_MANYCALL t3
        WHERE t1.PCODE = t2.PCODE
        AND   t1.ID = t3.firstpreid
        AND   t2.fccbz = '1') a
        on m1.conscustno=a.conscustno
        and a.PREBOOKSTATE='2'
        and a.PAYSTATE='3'

        <if test="param.tradeBeginDt != null">
            AND A.REALPAYAMTDT &gt;= #{param.tradeBeginDt}
        </if>
        <if test="param.tradeEndDt != null">
            AND A.REALPAYAMTDT &lt;= #{param.tradeEndDt}
        </if>

        left join (SELECT dd.conscustno,sum(dd.collamk)/10000 as collamk FROM dm.dm_trade_ack_insur_gd dd
        where dd.prestate='2'
        and dd.PAYSTATE='1'

        <if test="param.tradeBeginDt != null">
            AND dd.PAYDT &gt;= #{param.tradeBeginDt}
        </if>
        <if test="param.tradeEndDt != null">
            AND dd.PAYDT &lt;= #{param.tradeEndDt}
        </if>

        group by dd.conscustno
        )m3
        on m1.conscustno=m3.conscustno
        left join ods.raw_cm_conscust m7
        on m1.conscustno=m7.conscustno
        left join ods.raw_cm_custconstant m8
        on a.conscustno=m8.custno
        left join ods.raw_cm_consultant m9
        on m8.conscode=m9.conscode
        left join ods.raw_hb_organization m10
        on m9.outletcode=m10.orgcode
        left join dm.dm_ic_sales_rank m
        on a.id=m.id
        ) mmm
        group by  mmm.orgcode,
        mmm.orgname,
        mmm.conscode,
        mmm.consname,
        mmm.conscustno,
        mmm.custname)zz1
        inner join dm.dm_conscust_first_paydt zz2
        on zz1.conscustno=zz2.conscustno
        left join ods.raw_cm_consultant D
        on zz1.conscode=D.conscode
        where zz1.orgcode is not null

        <if test="param.conscode != null and param.conscode !=''">
            AND D.conscode = #{param.conscode}
        </if>
        <if test="param.teamcode != null">
            AND D.TEAMCODE = #{param.teamcode}
        </if>
        <if test="param.othertearm != null">
            AND D.OUTLETCODE = #{param.othertearm} and D.TEAMCODE is null
        </if>
        <if test="param.outletcodes != null">
            AND D.OUTLETCODE IN (${param.outletcodes})
        </if>
    </select>

    <select id="listPubFundTransferReport" parameterType="Map" resultType="Map" useCache="false">
        SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)) as u3name,
        t1.conscode as consCode,
        t1.consname as consName,
        count(distinct t1.conscust_no) as custNum,
        sum(t1.ack_vol*t1.nav) as appAmt,
        sum(t1.ack_vol*t1.nav)/count(distinct t1.conscust_no) as avgAmt
        FROM dm.dm_trade_transfer_custody_ls t1
        left join dw.dw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscust_no=t3.conscustno
        where 1=1
        <if test="appBeginDt != null and appBeginDt !=''">
            AND t1.APP_DT &gt;= #{appBeginDt}
        </if>
        <if test="appEndDt != null and appEndDt !=''">
            AND t1.APP_DT &lt;= #{appEndDt}
        </if>
        <if test="conscode != null and conscode !=''">
            AND t1.conscode = #{conscode}
        </if>
        <if test="conscustNo != null and conscustNo !=''">
            AND t1.conscust_no = #{conscustNo}
        </if>
        <if test="custname != null and custname !=''">
            AND t3.custname = #{custname}
        </if>
        <if test="fundglr != null and fundglr !=''">
            AND t1.product_code = #{fundglr}
        </if>
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="orderStatus=='1'.toString()">
            AND t1.ORDER_STATUS = '确认成功'
        </if>
        <if test="orderStatus=='2'.toString()">
            AND t1.ORDER_STATUS = '确认失败'
        </if>
        <if test="ackBeginDt != null and ackBeginDt !=''">
            AND t1.ACK_DT &gt;= #{ackBeginDt}
        </if>
        <if test="ackEndDt != null and ackEndDt !=''">
            AND t1.ACK_DT &lt;= #{ackEndDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="tagentNo != null">
            AND t1.TAGENT_NO = #{tagentNo}
        </if>
        <if test="userId != null">
            AND t1.conscode = #{userId}
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)),
        t1.conscode,
        t1.consname
        order by sum(t1.app_vol*t1.nav) desc,sum(t1.app_vol*t1.nav)/count(distinct t1.conscust_no)
        desc,t1.u1_name desc,
        coalesce(t1.u2_name,t1.u1_name) desc,
        translate(coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)),'一二三四五六七八九','123456789')  desc
    </select>

    <select id="listPubFundTransferReportDtlReportByPage" parameterType="Map" resultType="CustTradeNew" useCache="false">
        SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)) as u3name,
        t1.conscode as consCode,
        t1.consname as consName,
        t1.conscust_no as conscustNo,
        t3.custname as conscustName,
        t1.product_code as fundCode,
        t1.product_name as fundName,
        t1.order_status as orderStatus,
        t1.app_dt as appDt,
        t1.app_vol as appVol,
        t1.nav_dt as navDt,
        t1.ACK_DT as ackDt,
        t1.ack_vol as ackVol,
        t1.nav as nav,
        t1.ack_vol*t1.nav as appAmt,
        t1.fund_man_abbr as managerMan,
        t1.product_class1 as productClass1,
        t1.product_class2 as productClass2,
        t1.org_name as agentName
        FROM dm.dm_trade_transfer_custody_ls t1
        left join dw.dw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscust_no=t3.conscustno
        where 1=1
        <if test="param.appBeginDt != null and param.appBeginDt !=''">
            AND t1.APP_DT &gt;= #{param.appBeginDt}
        </if>
        <if test="param.appEndDt != null and param.appEndDt !=''">
            AND t1.APP_DT &lt;= #{param.appEndDt}
        </if>
        <if test="param.conscode != null and param.conscode !='' and param.conscode !='null'">
            AND t1.conscode = #{param.conscode}
        </if>
        <if test="param.conscustNo != null and param.conscustNo !=''">
            AND t1.conscust_no = #{param.conscustNo}
        </if>
        <if test="param.custname != null and param.custname !=''">
            AND t3.custname = #{param.custname}
        </if>
        <if test="param.fundglr != null and param.fundglr !=''">
            AND t1.product_code = #{param.fundglr}
        </if>
        <if test="param.isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.orderStatus=='1'.toString()">
            AND t1.ORDER_STATUS = '确认成功'
        </if>
        <if test="param.orderStatus=='2'.toString()">
            AND t1.ORDER_STATUS = '确认失败'
        </if>
        <if test="param.ackBeginDt != null and param.ackBeginDt !=''">
            AND t1.ACK_DT &gt;= #{param.ackBeginDt}
        </if>
        <if test="param.ackEndDt != null and param.ackEndDt !=''">
            AND t1.ACK_DT &lt;= #{param.ackEndDt}
        </if>
        <if test="param.teamcode != null">
            AND t2.TEAMCODE = #{param.teamcode}
        </if>
        <if test="param.othertearm != null">
            AND t2.OUTLETCODE = #{param.othertearm} and t2.TEAMCODE is null
        </if>
        <if test="param.outletcodes != null">
            AND t2.OUTLETCODE IN (${param.outletcodes})
        </if>
        <if test="param.tagentNo != null">
            AND t1.TAGENT_NO = #{param.tagentNo}
        </if>
        order by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        translate(coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)),'一二三四五六七八九','123456789')  asc
    </select>

    <select id="listPubFundTransferReportDtlReport" parameterType="Map" resultType="CustTradeNew" useCache="false">
        SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)) as u3name,
        t1.conscode as consCode,
        t1.consname as consName,
        t1.conscust_no as conscustNo,
        t3.custname as conscustName,
        t1.product_code as fundCode,
        t1.product_name as fundName,
        t1.order_status as orderStatus,
        t1.app_dt as appDt,
        t1.app_vol as appVol,
        t1.nav_dt as navDt,
        t1.ACK_DT as ackDt,
        t1.ack_vol as ackVol,
        t1.nav as nav,
        t1.ack_vol*t1.nav as appAmt,
        t1.fund_man_abbr as managerMan,
        t1.product_class1 as productClass1,
        t1.product_class2 as productClass2,
        t1.org_name as agentName
        FROM dm.dm_trade_transfer_custody_ls t1
        left join dw.dw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscust_no=t3.conscustno
        where 1=1
        <if test="appBeginDt != null and appBeginDt !=''">
            AND t1.APP_DT &gt;= #{appBeginDt}
        </if>
        <if test="appEndDt != null and appEndDt !=''">
            AND t1.APP_DT &lt;= #{appEndDt}
        </if>
        <if test="conscode != null and conscode !='' and conscode !='null'">
            AND t1.conscode = #{conscode}
        </if>
        <if test="conscustNo != null and conscustNo !=''">
            AND t1.conscust_no = #{conscustNo}
        </if>
        <if test="custname != null and custname !=''">
            AND t3.custname = #{custname}
        </if>
        <if test="fundglr != null and fundglr !=''">
            AND t1.product_code = #{fundglr}
        </if>
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="orderStatus=='1'.toString()">
            AND t1.ORDER_STATUS = '确认成功'
        </if>
        <if test="orderStatus=='2'.toString()">
            AND t1.ORDER_STATUS = '确认失败'
        </if>
        <if test="ackBeginDt != null and ackBeginDt !=''">
            AND t1.ACK_DT &gt;= #{ackBeginDt}
        </if>
        <if test="ackEndDt != null and ackEndDt !=''">
            AND t1.ACK_DT &lt;= #{ackEndDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="tagentNo != null">
            AND t1.TAGENT_NO = #{tagentNo}
        </if>
        order by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        translate(coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)),'一二三四五六七八九','123456789')  asc
    </select>

    <select id="listPubFundNetAppIncreaseReport" resultType="com.hb.postgre.domain.trade.DmGmNetIncreaseSum" parameterType="Map" useCache="false">
        SELECT
        t1.u1name as u1name,
        coalesce(t1.u2name,t1.u1name) as u2name,
        coalesce(t1.u3name,coalesce(t1.u2name,t1.u1name)) as u3name,
        t1.cons_code as consCode,
        t1.cons_name as consName,
        sum(case when t1.trade_type='购买' then coalesce(t1.ACK_AMT, 0) else 0 end) as increaseAmtRmb,
        sum(case when t1.trade_type='赎回' then coalesce(t1.ACK_AMT, 0) else 0 end) as reduceAmtRmb,
        sum(case when t1.trade_type='购买' then coalesce(t1.ACK_AMT, 0) else 0 end)-
        sum(case when t1.trade_type='赎回' then coalesce(t1.ACK_AMT, 0) else 0 end) as netIncreaseAmtRmb
        FROM dm.dm_gm_net_increase t1
        where 1=1
        <if test="conscode != null and conscode !=''">
            AND t1.cons_code = #{conscode}
        </if>
        <if test="conscustNo != null and conscustNo !=''">
            AND t1.CONSCUST_NO = #{conscustNo}
        </if>
        <if test="custname != null and custname !=''">
            AND t1.CONSCUST_NAME = #{custname}
        </if>
        <if test="isconsstatus=='0'.toString()">
            AND t1.CONS_STATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t1.CONS_STATUS= #{isconsstatus}
        </if>
        <if test="custType=='1'.toString()">
            AND t1.DIS_CODE != 'FOF201710'
        </if>
        <if test="custType=='2'.toString()">
            AND t1.DIS_CODE = 'FOF201710'
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t1.TEAM_CODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t1.OUT_LET_CODE = #{othertearm} and t1.TEAM_CODE is null
        </if>
        <if test="outletcodes != null">
            AND t1.OUT_LET_CODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.bqxlmc IN (${bqxlmc})
        </if>
        <if test="productClass1 != null and productClass1 != '全部'">
            AND t1.product_class1 = #{productClass1}
        </if>
        <if test="productClass2 != null and productClass2 != '全部'">
            AND t1.product_class2 = #{productClass2}
        </if>
        <if test="productClass3 != null and productClass3 != '全部'">
            AND t1.product_class3 = #{productClass3}
        </if>
        group by t1.u1name,
        coalesce(t1.u2name,t1.u1name),
        coalesce(t1.u3name,coalesce(t1.u2name,t1.u1name)),
        t1.cons_code,
        t1.cons_name
    </select>

    <select id="listPubFundNetAppIncreaseDtlReportByPage" resultType="com.hb.postgre.domain.trade.CustTradeNew" parameterType="Map" useCache="false">
        SELECT
        t1.u1name as u1name,
        coalesce(t1.u2name,t1.u1name) as u2name,
        coalesce(t1.u3name,coalesce(t1.u2name,t1.u1name)) as u3name,
        t1.cons_code as consCode,
        t1.cons_name as consName,
        t1.hbone_no as hboneNo,
        t1.CONSCUST_NO as conscustNo,
        t1.CONSCUST_NAME as conscustName,
        t1.MBUSI_CODE as mBusiCode,
        t1.MBUSI_NAME as mBusiName,
        t1.TRADE_TYPE as tradeType,
        t1.FUND_CODE as fundCode,
        t1.MANAGER_MAN as managerMan,
        t1.fund_name as fundName,
        t1.ORDER_STATUS AS orderStatus,
        t1.ack_dt as ackDt,
        t1.ack_amt as ackAmt,
        t1.ack_vol as ackVol,
        t1.product_class1 as productClass1,
        t1.product_class2 as productClass2,
        t1.product_class3 as productClass3,
        t1.bqxlmc as bqxlmc
        FROM dm.dm_gm_net_increase t1
        WHERE 1=1
        <if test="param.conscode != null and param.conscode !='' and param.conscode !='null' ">
            AND t1.cons_code = #{param.conscode}
        </if>
        <if test="param.conscustNo != null and param.conscustNo !=''">
            AND t1.conscust_no = #{param.conscustNo}
        </if>
        <if test="param.custname != null and param.custname !=''">
            AND t1.CONSCUST_NAME = #{param.custname}
        </if>
        <if test="param.fundglr != null and param.fundglr !=''">
            AND t1.FUND_CODE = #{param.fundglr}
        </if>
        <if test="param.isconsstatus=='0'.toString()">
            AND t1.CONS_STATUS= #{param.isconsstatus}
        </if>
        <if test="param.isconsstatus=='1'.toString()">
            AND t1.CONS_STATUS= #{param.isconsstatus}
        </if>
        <if test="param.custType=='1'.toString()">
            AND t1.DIS_CODE != 'FOF201710'
        </if>
        <if test="param.custType=='2'.toString()">
            AND t1.DIS_CODE = 'FOF201710'
        </if>
        <if test="param.beginDt != null and param.beginDt !=''">
            AND t1.ACK_DT &gt;= #{param.beginDt}
        </if>
        <if test="param.endDt != null and param.endDt !=''">
            AND t1.ACK_DT &lt;= #{param.endDt}
        </if>
        <if test="param.teamcode != null">
            AND t1.TEAM_CODE = #{param.teamcode}
        </if>
        <if test="param.othertearm != null">
            AND t1.OUT_LET_CODE = #{param.othertearm} and t1.TEAM_CODE is null
        </if>
        <if test="param.outletcodes != null">
            AND t1.OUT_LET_CODE IN (${param.outletcodes})
        </if>
        <if test="param.bqxlmc != null and param.bqxlmc != ''">
            AND t1.bqxlmc IN (${param.bqxlmc})
        </if>
        <if test="param.productClass1 != null and param.productClass1 != '全部'">
            AND t1.product_class1 = #{param.productClass1}
        </if>
        <if test="param.productClass2 != null and param.productClass2 != '全部'">
            AND t1.product_class2 = #{param.productClass2}
        </if>
        <if test="param.productClass3 != null and param.productClass3 != '全部'">
            AND t1.product_class3 = #{param.productClass3}
        </if>
    </select>

    <select id="listPubFundNetAppIncreaseDtlReport" resultType="com.hb.postgre.domain.trade.CustTradeNew" parameterType="Map" useCache="false">
        SELECT
        t1.u1name as u1name,
        coalesce(t1.u2name,t1.u1name) as u2name,
        coalesce(t1.u3name,coalesce(t1.u2name,t1.u1name)) as u3name,
        t1.cons_code as consCode,
        t1.cons_name as consName,
        t1.hbone_no as hboneNo,
        t1.CONSCUST_NO as conscustNo,
        t1.CONSCUST_NAME as conscustName,
        t1.MBUSI_CODE as mBusiCode,
        t1.MBUSI_NAME as mBusiName,
        t1.TRADE_TYPE as tradeType,
        t1.FUND_CODE as fundCode,
        t1.MANAGER_MAN as managerMan,
        t1.fund_name as fundName,
        t1.ORDER_STATUS AS orderStatus,
        t1.ack_dt as ackDt,
        t1.ack_amt as ackAmt,
        t1.ack_vol as ackVol,
        t1.product_class1 as productClass1,
        t1.product_class2 as productClass2,
        t1.product_class3 as productClass3,
        t1.bqxlmc as bqxlmc
        FROM dm.dm_gm_net_increase t1
        where 1=1
        <if test="conscode != null and conscode !='' and conscode !='null'">
            AND t1.cons_code = #{conscode}
        </if>
        <if test="conscustNo != null and conscustNo !=''">
            AND t1.CONSCUST_NO = #{conscustNo}
        </if>
        <if test="custname != null and custname !=''">
            AND t1.CONSCUST_NAME = #{custname}
        </if>
        <if test="fundglr != null and fundglr !=''">
            AND t1.fund_code = #{fundglr}
        </if>
        <if test="custType=='1'.toString()">
            AND t1.DIS_CODE != 'FOF201710'
        </if>
        <if test="custType=='2'.toString()">
            AND t1.DIS_CODE = 'FOF201710'
        </if>
        <if test="isconsstatus=='0'.toString()">
            AND t1.CONS_STATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t1.CONS_STATUS= #{isconsstatus}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t1.TEAM_CODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t1.OUT_LET_CODE = #{othertearm} and t1.TEAM_CODE is null
        </if>
        <if test="outletcodes != null">
            AND t1.OUT_LET_CODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.bqxlmc IN (${bqxlmc})
        </if>
        <if test="productClass1 != null and productClass1 != '全部'">
            AND t1.product_class1 = #{productClass1}
        </if>
        <if test="productClass2 != null and productClass2 != '全部'">
            AND t1.product_class2 = #{productClass2}
        </if>
        <if test="productClass3 != null and productClass3 != '全部'">
            AND t1.product_class3 = #{productClass3}
        </if>
    </select>

    <select id="countPubFundNetAppIncreaseDtlReport" resultType="long" parameterType="Map" useCache="false">
        SELECT COUNT(1)
        FROM dm.dm_gm_net_increase t1
        where 1=1
        <if test="conscode != null and conscode !='' and conscode !='null'">
            AND t1.cons_code = #{conscode}
        </if>
        <if test="conscustNo != null and conscustNo !=''">
            AND t1.CONSCUST_NO = #{conscustNo}
        </if>
        <if test="custname != null and custname !=''">
            AND t1.CONSCUST_NAME = #{custname}
        </if>
        <if test="fundglr != null and fundglr !=''">
            AND t1.fund_code = #{fundglr}
        </if>
        <if test="custType=='1'.toString()">
            AND t1.DIS_CODE != 'FOF201710'
        </if>
        <if test="custType=='2'.toString()">
            AND t1.DIS_CODE = 'FOF201710'
        </if>
        <if test="isconsstatus=='0'.toString()">
            AND t1.CONS_STATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t1.CONS_STATUS= #{isconsstatus}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t1.TEAM_CODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t1.OUT_LET_CODE = #{othertearm} and t1.TEAM_CODE is null
        </if>
        <if test="outletcodes != null">
            AND t1.OUT_LET_CODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.bqxlmc IN (${bqxlmc})
        </if>
        <if test="productClass1 != null and productClass1 != '全部'">
            AND t1.product_class1 = #{productClass1}
        </if>
        <if test="productClass2 != null and productClass2 != '全部'">
            AND t1.product_class2 = #{productClass2}
        </if>
        <if test="productClass3 != null and productClass3 != '全部'">
            AND t1.product_class3 = #{productClass3}
        </if>
    </select>





    <select id="listNetAppIncreaseReport" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(coalesce(t1.u3_name,t1.u2_name),t1.u1_name) as u3name,
        trunc(sum(t1.buy_amt)/10000,2) as increaseAmtRmb,
        trunc(sum(t1.mid_redem_amt)/10000,2) as reduceAmtRmb,
        trunc((sum(t1.buy_amt)-sum(t1.mid_redem_amt))/10000,2) as netIncreaseAmtRmb
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        where 1=1
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.yjcl IN (${bqxlmc})
        </if>
        <if test="sftzhw != null and sftzhw != '' and sftzhw != '全部' ">
            AND t1.sftzhw = #{sftzhw}
        </if>
        <if test="isFof != null and isFof != ''">
            <if test="isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,t1.u2_name)
        order by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        translate(coalesce(t1.u3_name,t1.u2_name),'一二三四五六七八九','123456789')  asc

    </select>

    <select id="listConscodeNetAppIncreaseReport" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)) as u3name,
        t1.conscode as consCode,
        t2.consname as consName,
        trunc(sum(t1.buy_amt)/10000,2) as increaseAmtRmb,
        trunc(sum(t1.mid_redem_amt)/10000,2) as reduceAmtRmb,
        trunc((sum(t1.buy_amt)-sum(t1.mid_redem_amt))/10000,2) as netIncreaseAmtRmb
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        where 1=1
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="conscode != null and conscode !=''">
            AND t1.conscode = #{conscode}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.yjcl IN (${bqxlmc})
        </if>
        <if test="sftzhw != null and sftzhw != '全部'">
            AND t1.sftzhw = #{sftzhw}
        </if>
        <if test="isFof != null and isFof != ''">
            <if test="isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)),
        t1.conscode,
        t2.consname
        order by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        translate(coalesce(t1.u3_name,coalesce(t1.u2_name,t1.u1_name)),'一二三四五六七八九','123456789'),
        t1.conscode,
        t2.consname  asc

    </select>


    <select id="listCustConscodeNetIncreaseDtlReportByPage" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        select tt.u1name,
        tt.u2name,
        coalesce(tt.u3name,coalesce(tt.u2name,tt.u1name)) as u3name,
        tt.conscustNo,
        tt.conscustName,
        tt.consCode,
        tt.consName,
        tt.custState,
        tt.increaseAmtRmb,
        tt.reduceAmtRmb,
        tt.netIncreaseAmtRmb
        from (SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,t1.u2_name) as u3name,
        t1.conscustno as conscustNo,
        t3.custname as conscustName,
        t1.conscode as consCode,
        t2.consname as consName,
        case when t4.conscustno is not null
        then '新客户'
        else '老客户' end as custState,
        trunc(sum(t1.buy_amt)/10000,2) as increaseAmtRmb,
        trunc(sum(t1.mid_redem_amt)/10000,2) as reduceAmtRmb,
        trunc((sum(t1.buy_amt)-sum(t1.mid_redem_amt))/10000,2) as netIncreaseAmtRmb,
        translate(coalesce(t1.u3_name,t1.u2_name)||'十一','一二三四五六七八九十一','12345678911') as sort
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscustno=t3.conscustno
        left join (SELECT min(m1.ack_dt) tradedt,m1.conscustno FROM dw.dw_trade_mid_ack_gd  m1 group by m1.conscustno ) t4
        on t1.conscustno=t4.conscustno
        and t4.tradedt &gt;= coalesce(#{param.beginDt},to_char(current_timestamp,'yyyy')||'0101')
        where 1=1
        <if test="param.isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.conscode != null and param.conscode !='' and param.conscode !='null'">
            AND t1.conscode = #{param.conscode}
        </if>
        <if test="param.beginDt != null and param.beginDt !=''">
            AND t1.ACK_DT &gt;= #{param.beginDt}
        </if>
        <if test="param.endDt != null and param.endDt !=''">
            AND t1.ACK_DT &lt;= #{param.endDt}
        </if>
        <if test="param.teamcode != null">
            AND t2.TEAMCODE = #{param.teamcode}
        </if>
        <if test="param.othertearm != null">
            AND t2.OUTLETCODE = #{param.othertearm} and t2.TEAMCODE is null
        </if>
        <if test="param.outletcodes != null">
            AND t2.OUTLETCODE IN (${param.outletcodes})
        </if>
        <if test="param.bqxlmc != null and param.bqxlmc != ''">
            AND t1.yjcl IN (${param.bqxlmc})
        </if>
        <if test="param.sftzhw != null and param.sftzhw != '全部'">
            AND t1.sftzhw = #{param.sftzhw}
        </if>
        <if test="param.isFof != null and param.isFof != ''">
            <if test="param.isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="param.isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fundtypeinner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,t1.u2_name),
        t1.conscode,
        t2.consname,
        t1.conscustno,
        t4.conscustno,
        t3.custname) tt
        order by tt.u1name, tt.u2name, tt.u3name, tt.sort, tt.conscode, tt.consname asc nulls last
    </select>

    <select id="listCustConscodeNetIncreaseProductDtlReportByPage" parameterType="Map" resultType="GdTurnoverNew" useCache="false">

        select
        tt.u1name,
        tt.u2name,
        coalesce(tt.u3name, coalesce(tt.u2name, tt.u1name)) as u3name,
        tt.consCode,
        tt.consName,
        tt.fundCode,
        tt.fundName,
        tt.fundtypeInner,
        tt.hbtype,
        tt.managerMan,
        tt.increaseAmtRmb,
        tt.reduceAmtRmb,
        tt.netIncreaseAmtRmb,
        tt.yjcl as bqxlmc,
        tt.sftzhw
        from
        (
        select
        t1.u1_name as u1name,
        coalesce(t1.u2_name, t1.u1_name) as u2name,
        coalesce(t1.u3_name, t1.u2_name) as u3name,
        t1.conscode as consCode,
        t2.consname as consName,
        t1.fund_code as fundCode,
        t1.fund_name as fundName,
        t1.fund_type_inner as fundtypeInner,
        t1.hb_type as hbtype,
        t1.manager_man as managerMan,
        t1.yjcl as yjcl,
        t1.sftzhw as sftzhw,
        trunc(sum(t1.buy_amt)/ 10000, 2) as increaseAmtRmb,
        trunc(sum(t1.mid_redem_amt)/ 10000, 2) as reduceAmtRmb,
        trunc((sum(t1.buy_amt)-sum(t1.mid_redem_amt))/ 10000, 2) as netIncreaseAmtRmb,
        translate(coalesce(t1.u3_name, t1.u2_name)|| '十一', '一二三四五六七八九十一', '12345678911') as sort
        from
        dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on
        t1.conscode = t2.conscode
        left join ods.raw_cm_conscust t3
        on
        t1.conscustno = t3.conscustno
        where
        1 = 1
        <if test="param.isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.conscode != null and param.conscode !='' and param.conscode !='null'">
            AND t1.conscode = #{param.conscode}
        </if>
        <if test="param.beginDt != null and param.beginDt !=''">
            AND t1.ACK_DT &gt;= #{param.beginDt}
        </if>
        <if test="param.endDt != null and param.endDt !=''">
            AND t1.ACK_DT &lt;= #{param.endDt}
        </if>
        <if test="param.teamcode != null">
            AND t2.TEAMCODE = #{param.teamcode}
        </if>
        <if test="param.othertearm != null">
            AND t2.OUTLETCODE = #{param.othertearm} and t2.TEAMCODE is null
        </if>
        <if test="param.outletcodes != null">
            AND t2.OUTLETCODE IN (${param.outletcodes})
        </if>
        <if test="param.bqxlmc != null and param.bqxlmc != ''">
            AND t1.yjcl IN (${param.bqxlmc})
        </if>
        <if test="param.sftzhw != null and param.sftzhw != '全部'">
            AND t1.sftzhw = #{param.sftzhw}
        </if>
        <if test="param.isFof != null and param.isFof != ''">
            <if test="param.isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="param.isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by
        t1.u1_name,
        coalesce(t1.u2_name, t1.u1_name),
        coalesce(t1.u3_name, t1.u2_name),
        t1.conscode,
        t2.consname,
        t1.fund_code,
        t1.fund_name,
        t1.fund_type_inner,
        t1.hb_type,
        t1.yjcl,
        t1.sftzhw,
        t1.manager_man) tt
        order by
        tt.u1name,
        tt.u2name,
        tt.u3name,
        tt.sort,
        tt.conscode,
        tt.consname asc nulls last
    </select>

    <select id="listCustConscodeNetIncreaseTradeDtlReportByPage" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        select  tt.u1name,
        tt.u2name,
        coalesce(tt.u3name,coalesce(tt.u2name,tt.u1name)) as u3name,
        tt.conscustNo,
        tt.conscustName,
        tt.consCode,
        tt.consName,
        tt.custState,
        tt.tradeDt,
        tt.tradeType,
        tt.fundCode,
        tt.fundName,
        tt.fundtypeInner,
        tt.hbtype,
        tt.managerMan,
        tt.tradeAmt,
        tt.bqxlmc,
        tt.sftzhw,
        tt.hwzczbxx,
        tt.hwzczbsx,
        tt.hwzczb,
        tt.bxContractAmt,
        tt.yearNo
        from (SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,t1.u2_name) as u3name,
        t1.conscustno as conscustNo,
        t3.custname as conscustName,
        t1.conscode as consCode,
        t2.consname as consName,
        case when t4.conscustno is not null
        then '新客户'
        else '老客户' end as custState,
        t1.ack_dt as tradeDt,
        decode(t1.trade_Type,'1','购买','2','赎回') as tradeType,
        t1.fund_code as fundCode,
        t1.fund_name as fundName,
        t1.fund_type_inner as fundtypeInner,
        t1.hb_type as hbtype,
        t1.manager_man as managerMan,
        sum(case when t1.trade_Type='1' then coalesce(t1.buy_amt,0)/10000
        else coalesce(t1.mid_redem_amt,0)/10000 end) as tradeAmt,
        t1.yjcl as bqxlmc,
        t1.sftzhw,
        t1.hwzczbxx,
        t1.hwzczbsx,
        t1.hwzczb,
        t1.bx_contract_amt as bxContractAmt,
        t1.yearno as yearNo,
        translate(coalesce(t1.u3_name,t1.u2_name)||'十一','一二三四五六七八九十一','12345678911') as sort
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscustno=t3.conscustno
        left join (SELECT min(m1.ack_dt) tradedt,m1.conscustno FROM dw.dw_trade_mid_ack_gd  m1 group by m1.conscustno ) t4
        on t1.conscustno=t4.conscustno
        and t4.tradedt &gt;= coalesce(#{param.beginDt},to_char(current_timestamp,'yyyy')||'0101')
        where 1=1
        <if test="param.isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{param.isconsstatus}
        </if>
        <if test="param.conscode != null and param.conscode !='' and param.conscode !='null'">
            AND t1.conscode = #{param.conscode}
        </if>
        <if test="param.beginDt != null and param.beginDt !=''">
            AND t1.ACK_DT &gt;= #{param.beginDt}
        </if>
        <if test="param.endDt != null and param.endDt !=''">
            AND t1.ACK_DT &lt;= #{param.endDt}
        </if>
        <if test="param.teamcode != null">
            AND t2.TEAMCODE = #{param.teamcode}
        </if>
        <if test="param.othertearm != null">
            AND t2.OUTLETCODE = #{param.othertearm} and t2.TEAMCODE is null
        </if>
        <if test="param.outletcodes != null">
            AND t2.OUTLETCODE IN (${param.outletcodes})
        </if>
        <if test="param.bqxlmc != null and param.bqxlmc != ''">
            AND t1.yjcl IN (${param.bqxlmc})
        </if>
        <if test="param.sftzhw != null and param.sftzhw != '全部'">
            AND t1.sftzhw = #{param.sftzhw}
        </if>
        <if test="param.isFof != null and param.isFof != ''">
            <if test="param.isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="param.isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,t1.u2_name),
        t1.conscode,
        t2.consname,
        t1.conscustno,
        t4.conscustno,
        t3.custname,
        t1.ack_dt,
        t1.trade_Type,
        t1.fund_code,
        t1.fund_name,
        t1.fund_type_inner,
        t1.hb_type,
        t1.manager_man,
        t1.yjcl,
        t1.sftzhw,
        t1.hwzczbxx,
        t1.hwzczbsx,
        t1.hwzczb,
        t1.bx_contract_amt,
        t1.yearno) tt
        order by tt.u1name, tt.u2name, tt.u3name, tt.sort, tt.conscode, tt.consname asc nulls last
    </select>


    <select id="listCustConscodeNetIncreaseDtlReport" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        select tt.u1name,
        tt.u2name,
        coalesce(tt.u3name,coalesce(tt.u2name,tt.u1name)) as u3name,
        tt.conscustNo,
        tt.conscustName,
        tt.consCode,
        tt.consName,
        tt.custState,
        tt.increaseAmtRmb,
        tt.reduceAmtRmb,
        tt.netIncreaseAmtRmb
        from (SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,t1.u2_name) as u3name,
        t1.conscustno as conscustNo,
        t3.custname as conscustName,
        t1.conscode as consCode,
        t2.consname as consName,
        case when t4.conscustno is not null
        then '新客户'
        else '老客户' end as custState,
        trunc(sum(t1.buy_amt)/10000,2) as increaseAmtRmb,
        trunc(sum(t1.mid_redem_amt)/10000,2) as reduceAmtRmb,
        trunc((sum(t1.buy_amt)-sum(t1.mid_redem_amt))/10000,2) as netIncreaseAmtRmb,
        translate(coalesce(t1.u3_name,t1.u2_name)||'十一','一二三四五六七八九十一','12345678911') as sort
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscustno=t3.conscustno
        left join (SELECT min(m1.ack_dt) tradedt,m1.conscustno FROM dw.dw_trade_mid_ack_gd  m1 group by m1.conscustno ) t4
        on t1.conscustno=t4.conscustno
        and t4.tradedt &gt;= coalesce(#{beginDt},to_char(current_timestamp,'yyyy')||'0101')
        where 1=1
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="conscode != null and conscode !='' and conscode !='null'">
            AND t1.conscode = #{conscode}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.yjcl IN (${bqxlmc})
        </if>
        <if test="sftzhw != null and sftzhw != '全部'">
            AND t1.sftzhw = #{sftzhw}
        </if>
        <if test="isFof != null and isFof != ''">
            <if test="isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,t1.u2_name),
        t1.conscode,
        t2.consname,
        t1.conscustno,
        t4.conscustno,
        t3.custname) tt
        order by tt.u1name, tt.u2name, tt.u3name, tt.sort, tt.conscode, tt.consname asc nulls last
    </select>

    <select id="listCustConscodeNetIncreaseProductDtlReport" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        select tt.u1name,
        tt.u2name,
        coalesce(tt.u3name,coalesce(tt.u2name,tt.u1name)) as u3name,
        tt.consCode,
        tt.consName,
        tt.fundCode,
        tt.fundName,
        tt.fundtypeInner,
        tt.hbtype,
        tt.managerMan,
        tt.increaseAmtRmb,
        tt.reduceAmtRmb,
        tt.netIncreaseAmtRmb,
        tt.bqxlmc,
        tt.sftzhw
        from (select
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,t1.u2_name) as u3name,
        t1.conscode as consCode,
        t2.consname as consName,
        t1.fund_code as fundCode,
        t1.fund_name as fundName,
        t1.fund_type_inner as fundtypeInner,
        t1.hb_type as hbtype,
        t1.yjcl as bqxlmc,
        t1.sftzhw as sftzhw,
        t1.manager_man as managerMan,
        trunc(sum(t1.buy_amt)/10000,2) as increaseAmtRmb,
        trunc(sum(t1.mid_redem_amt)/10000,2) as reduceAmtRmb,
        trunc((sum(t1.buy_amt)-sum(t1.mid_redem_amt))/10000,2) as netIncreaseAmtRmb,
        translate(coalesce(t1.u3_name,t1.u2_name)||'十一','一二三四五六七八九十一','12345678911') as sort
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscustno=t3.conscustno
        where 1=1
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="conscode != null and conscode !='' and conscode !='null'">
            AND t1.conscode = #{conscode}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.yjcl IN (${bqxlmc})
        </if>
        <if test="sftzhw != null and sftzhw != '全部'">
            AND t1.sftzhw = #{sftzhw}
        </if>
        <if test="isFof != null and isFof != ''">
            <if test="isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,t1.u2_name),
        t1.conscode,
        t2.consname,
        t1.fund_code,
        t1.fund_name,
        t1.fund_type_inner,
        t1.hb_type,
        t1.yjcl,
        t1.sftzhw,
        t1.manager_man) tt
        order by tt.u1name, tt.u2name, tt.u3name, tt.sort, tt.conscode, tt.consname asc nulls last
    </select>

    <select id="listCustConscodeNetIncreaseTradeDtlReport" parameterType="Map" resultType="GdTurnoverNew" useCache="false">
        select  tt.u1name,
        tt.u2name,
        coalesce(tt.u3name,coalesce(tt.u2name,tt.u1name)) as u3name,
        tt.conscustNo,
        tt.conscustName,
        tt.consCode,
        tt.consName,
        tt.custState,
        tt.tradeDt,
        tt.tradeType,
        tt.fundCode,
        tt.fundName,
        tt.fundtypeInner,
        tt.hbtype,
        tt.managerMan,
        tt.tradeAmt,
        tt.bqxlmc,
        tt.sftzhw,
        tt.hwzczbxx,
        tt.hwzczbsx,
        tt.hwzczb,
        tt.bxContractAmt,
        tt.yearNo
        from (SELECT
        t1.u1_name as u1name,
        coalesce(t1.u2_name,t1.u1_name) as u2name,
        coalesce(t1.u3_name,t1.u2_name) as u3name,
        t1.conscustno as conscustNo,
        t3.custname as conscustName,
        t1.conscode as consCode,
        t2.consname as consName,
        case when t4.conscustno is not null
        then '新客户'
        else '老客户' end as custState,
        t1.ack_dt as tradeDt,
        decode(t1.trade_type,'1','购买','2','赎回') as tradeType,
        t1.fund_code as fundCode,
        t1.fund_name as fundName,
        t1.fund_type_inner as fundtypeInner,
        t1.hb_type as hbtype,
        t1.manager_man as managerMan,
        sum(case when t1.trade_type='1' then coalesce(t1.buy_amt,0)/10000
        else coalesce(t1.mid_redem_amt,0)/10000 end) as tradeAmt,
        t1.yjcl as bqxlmc,
        t1.sftzhw,
        t1.hwzczbxx,
        t1.hwzczbsx,
        t1.hwzczb,
        t1.bx_contract_amt as bxContractAmt,
        t1.yearno as yearNo,
        translate(coalesce(t1.u3_name,t1.u2_name)||'十一','一二三四五六七八九十一','12345678911') as sort
        FROM dm.dm_gd_net_increase t1
        left join ods.raw_cm_consultant t2
        on t1.conscode=t2.conscode
        left join ods.raw_cm_conscust t3
        on t1.conscustno=t3.conscustno
        left join (SELECT min(m1.ack_dt) tradedt,m1.conscustno FROM dw.dw_trade_mid_ack_gd  m1 group by m1.conscustno ) t4
        on t1.conscustno=t4.conscustno
        and t4.tradedt &gt;= coalesce(#{beginDt},to_char(current_timestamp,'yyyy')||'0101')
        where 1=1
        <if test="isconsstatus=='0'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="isconsstatus=='1'.toString()">
            AND t2.CONSSTATUS= #{isconsstatus}
        </if>
        <if test="conscode != null and  conscode !='' and conscode !='null'">
            AND t1.conscode = #{conscode}
        </if>
        <if test="beginDt != null and beginDt !=''">
            AND t1.ACK_DT &gt;= #{beginDt}
        </if>
        <if test="endDt != null and endDt !=''">
            AND t1.ACK_DT &lt;= #{endDt}
        </if>
        <if test="teamcode != null">
            AND t2.TEAMCODE = #{teamcode}
        </if>
        <if test="othertearm != null">
            AND t2.OUTLETCODE = #{othertearm} and t2.TEAMCODE is null
        </if>
        <if test="outletcodes != null">
            AND t2.OUTLETCODE IN (${outletcodes})
        </if>
        <if test="bqxlmc != null and bqxlmc != ''">
            AND t1.yjcl IN (${bqxlmc})
        </if>
        <if test="sftzhw != null and sftzhw != '全部'">
            AND t1.sftzhw = #{sftzhw}
        </if>
        <if test="isFof != null and isFof != ''">
            <if test="isFof == '1'.toString() ">
                AND t1.fund_type_inner = 'FOF'
            </if>
            <if test="isFof == '0'.toString() ">
                AND (t1.fund_type_inner != 'FOF' or t1.fund_type_inner is null)
            </if>
        </if>
        group by t1.u1_name,
        coalesce(t1.u2_name,t1.u1_name),
        coalesce(t1.u3_name,t1.u2_name),
        t1.conscode,
        t2.consname,
        t1.conscustno,
        t4.conscustno,
        t3.custname,
        t1.ack_dt,
        t1.trade_type,
        t1.fund_code,
        t1.fund_name,
        t1.fund_type_inner,
        t1.hb_type,
        t1.manager_man,
        t1.yjcl,
        t1.sftzhw,
        t1.hwzczbxx,
        t1.hwzczbsx,
        t1.hwzczb,
        t1.bx_contract_amt,
        t1.yearno) tt
        order by tt.u1name, tt.u2name, tt.u3name, tt.sort, tt.conscode, tt.consname asc nulls last
    </select>
</mapper>



