package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: GdTurnover.java
 * <AUTHOR>
 * @version 1.0
 * @created 20200629
 */
public class GdTurnover implements Serializable {

    private static final long serialVersionUID = -8669453365115360941L;
    private String orgCode;
    private String orgName;
    private String u1name;
    private String u2name;//区域
    private String u3name;
    private String consCode;
	private String consName;
    private String beforeOrgCode;//划转前部门code
    private String beforeOrgName;//划转前部门
    private String beforeConsCode;//划转前投顾code
    private String beforeConsName;//划转前投顾
    private String conscustNo;//投顾客户号
    private String conscustName;//投顾姓名
    private String assignDt;//分配日期
    private String assignState;//分配状态
    private String assignMarketCap;//分配高端市值
    private String currentState;//当前状态
    private String isDeal;//分配后是否成交
    private String afterFirstDt; //分配后首次成交时间
    private String fundType; //成交成品类型
    private String fundCode; //产品code
    private String fundName; //成交产品名称
    private String ackAmtRmb; //成交金额
    private String ackAmtSum; //累计成交金额
    private BigDecimal reduceAmtRmb =new BigDecimal(0); //划出市值  或者赎回量
    private BigDecimal increaseAmtRmb=new BigDecimal(0); //划入市值  或者申购量
    private BigDecimal netIncreaseAmtRmb=new BigDecimal(0); //净划入市值 或者 净申购量
    private BigDecimal netIncreaseRate = new BigDecimal(0);//净申购比
    private String tradeDt; //交易日期
	private BigDecimal balanceNum = new BigDecimal(0); // 存量客户分配客户数
	private BigDecimal balanceNumCj = new BigDecimal(0);//存量客户分配成交客户数
	private BigDecimal balanceNumCjRate = new BigDecimal(0);//存量客户分配成交率
    private BigDecimal balance0Num = new BigDecimal(0); // 0存量客户分配客户数
    private BigDecimal balance0NumCj = new BigDecimal(0);//0存量客户分配成交客户数
    private BigDecimal balance0NumCjRate = new BigDecimal(0);//0存量客户分配成交率
    private BigDecimal market0Num = new BigDecimal(0); // 0存量客户分配客户数
    private BigDecimal market0NumCj = new BigDecimal(0);//0存量客户分配成交客户数
    private BigDecimal market0NumCjRate = new BigDecimal(0);//0存量客户分配成交率

    private String custState; //客户状态
    private String managerMan; //管理人
    private String hbtype; //好买产品线
    private String fundtypeInner; //产品分类(对内)
    private String tradeType; //交易类型
    private BigDecimal tradeAmt = new BigDecimal(0);//交易金额

    private String bqxlmc; //一级策略
    private String sftzhw; //是否投资海外
    private BigDecimal hwzczbxx = new BigDecimal(0);//海外资产占比下限（%）
    private BigDecimal hwzczbsx = new BigDecimal(0);//海外资产占比上限（%）
    private BigDecimal hwzczb = new BigDecimal(0);//海外资产占比

    /**
     * @Description: 创新合同金额
     */
    private BigDecimal bxContractAmt = new BigDecimal(0);
    /**
     * @Description: 创新第几年缴费
     */
    private BigDecimal yearNo = new BigDecimal(0);

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public BigDecimal getBalanceNum() {
        return balanceNum;
    }

    public void setBalanceNum(BigDecimal balanceNum) {
        this.balanceNum = balanceNum;
    }

    public BigDecimal getBalanceNumCj() {
        return balanceNumCj;
    }

    public void setBalanceNumCj(BigDecimal balanceNumCj) {
        this.balanceNumCj = balanceNumCj;
    }

    public BigDecimal getBalanceNumCjRate() {
        return balanceNumCjRate;
    }

    public void setBalanceNumCjRate(BigDecimal balanceNumCjRate) {
        this.balanceNumCjRate = balanceNumCjRate;
    }

    public BigDecimal getBalance0Num() {
        return balance0Num;
    }

    public void setBalance0Num(BigDecimal balance0Num) {
        this.balance0Num = balance0Num;
    }

    public BigDecimal getBalance0NumCj() {
        return balance0NumCj;
    }

    public void setBalance0NumCj(BigDecimal balance0NumCj) {
        this.balance0NumCj = balance0NumCj;
    }

    public BigDecimal getBalance0NumCjRate() {
        return balance0NumCjRate;
    }

    public void setBalance0NumCjRate(BigDecimal balance0NumCjRate) {
        this.balance0NumCjRate = balance0NumCjRate;
    }

    public BigDecimal getMarket0Num() {
        return market0Num;
    }

    public void setMarket0Num(BigDecimal market0Num) {
        this.market0Num = market0Num;
    }

    public BigDecimal getMarket0NumCj() {
        return market0NumCj;
    }

    public void setMarket0NumCj(BigDecimal market0NumCj) {
        this.market0NumCj = market0NumCj;
    }

    public BigDecimal getMarket0NumCjRate() {
        return market0NumCjRate;
    }

    public void setMarket0NumCjRate(BigDecimal market0NumCjRate) {
        this.market0NumCjRate = market0NumCjRate;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getConscustName() {
        return conscustName;
    }

    public void setConscustName(String conscustName) {
        this.conscustName = conscustName;
    }

    public String getAssignDt() {
        return assignDt;
    }

    public void setAssignDt(String assignDt) {
        this.assignDt = assignDt;
    }

    public String getAssignState() {
        return assignState;
    }

    public void setAssignState(String assignState) {
        this.assignState = assignState;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getIsDeal() {
        return isDeal;
    }

    public void setIsDeal(String isDeal) {
        this.isDeal = isDeal;
    }

    public String getAfterFirstDt() {
        return afterFirstDt;
    }

    public void setAfterFirstDt(String afterFirstDt) {
        this.afterFirstDt = afterFirstDt;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getAckAmtRmb() {
        return ackAmtRmb;
    }

    public void setAckAmtRmb(String ackAmtRmb) {
        this.ackAmtRmb = ackAmtRmb;
    }

    public String getAckAmtSum() {
        return ackAmtSum;
    }

    public void setAckAmtSum(String ackAmtSum) {
        this.ackAmtSum = ackAmtSum;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public BigDecimal getReduceAmtRmb() {
        return reduceAmtRmb;
    }

    public void setReduceAmtRmb(BigDecimal reduceAmtRmb) {
        this.reduceAmtRmb = reduceAmtRmb;
    }

    public BigDecimal getIncreaseAmtRmb() {
        return increaseAmtRmb;
    }

    public void setIncreaseAmtRmb(BigDecimal increaseAmtRmb) {
        this.increaseAmtRmb = increaseAmtRmb;
    }

    public BigDecimal getNetIncreaseAmtRmb() {
        return netIncreaseAmtRmb;
    }

    public void setNetIncreaseAmtRmb(BigDecimal netIncreaseAmtRmb) {
        this.netIncreaseAmtRmb = netIncreaseAmtRmb;
    }

    public String getBeforeOrgCode() {
        return beforeOrgCode;
    }

    public void setBeforeOrgCode(String beforeOrgCode) {
        this.beforeOrgCode = beforeOrgCode;
    }

    public String getBeforeOrgName() {
        return beforeOrgName;
    }

    public void setBeforeOrgName(String beforeOrgName) {
        this.beforeOrgName = beforeOrgName;
    }

    public String getBeforeConsCode() {
        return beforeConsCode;
    }

    public void setBeforeConsCode(String beforeConsCode) {
        this.beforeConsCode = beforeConsCode;
    }

    public String getBeforeConsName() {
        return beforeConsName;
    }

    public void setBeforeConsName(String beforeConsName) {
        this.beforeConsName = beforeConsName;
    }

    public String getAssignMarketCap() {
        return assignMarketCap;
    }

    public void setAssignMarketCap(String assignMarketCap) {
        this.assignMarketCap = assignMarketCap;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public BigDecimal getNetIncreaseRate() {
        return netIncreaseRate;
    }

    public void setNetIncreaseRate(BigDecimal netIncreaseRate) {
        this.netIncreaseRate = netIncreaseRate;
    }

    public String getCustState() {
        return custState;
    }

    public void setCustState(String custState) {
        this.custState = custState;
    }

    public String getManagerMan() {
        return managerMan;
    }

    public void setManagerMan(String managerMan) {
        this.managerMan = managerMan;
    }

    public String getHbtype() {
        return hbtype;
    }

    public void setHbtype(String hbtype) {
        this.hbtype = hbtype;
    }

    public String getFundtypeInner() {
        return fundtypeInner;
    }

    public void setFundtypeInner(String fundtypeInner) {
        this.fundtypeInner = fundtypeInner;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public String getBqxlmc() {
        return bqxlmc;
    }

    public void setBqxlmc(String bqxlmc) {
        this.bqxlmc = bqxlmc;
    }

    public String getSftzhw() {
        return sftzhw;
    }

    public void setSftzhw(String sftzhw) {
        this.sftzhw = sftzhw;
    }

    public BigDecimal getHwzczbxx() {
        return hwzczbxx;
    }

    public void setHwzczbxx(BigDecimal hwzczbxx) {
        this.hwzczbxx = hwzczbxx;
    }

    public BigDecimal getHwzczbsx() {
        return hwzczbsx;
    }

    public void setHwzczbsx(BigDecimal hwzczbsx) {
        this.hwzczbsx = hwzczbsx;
    }

    public BigDecimal getHwzczb() {
        return hwzczb;
    }

    public void setHwzczb(BigDecimal hwzczb) {
        this.hwzczb = hwzczb;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

    public BigDecimal getBxContractAmt() {
        return bxContractAmt;
    }

    public void setBxContractAmt(BigDecimal bxContractAmt) {
        this.bxContractAmt = bxContractAmt;
    }

    public BigDecimal getYearNo() {
        return yearNo;
    }

    public void setYearNo(BigDecimal yearNo) {
        this.yearNo = yearNo;
    }
}
