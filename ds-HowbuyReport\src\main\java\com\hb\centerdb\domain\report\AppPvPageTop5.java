package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class AppPvPageTop5 implements Serializable {

	private static final long serialVersionUID = 1L;

	private String hboneNo; // 一账通号

	private String consCustNo; // 投顾客户号

	private String consCustName; // 客户姓名

	private String consCode; // 投顾账号

	private String consName; // 投顾姓名

	private String classifyName; // 一级部门

	private String outletName; // 二级部门

	private String top5;//
	
	private String fundCode15; //
	
	private String fundName15;//

	private int count15=0; // 最近15天访问量

    private String fundCode30; //

    private String fundName30;//

    private int count30=0; // 最近30天访问量

    private String fundCode60; //

    private String fundName60;//

    private int count60=0; // 最近60天访问量

	private String searchWords; // 近30天搜索关键词

	private int searchCount=0;// 近30天关键词搜索次数

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getConsCustName() {
        return consCustName;
    }

    public void setConsCustName(String consCustName) {
        this.consCustName = consCustName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getClassifyName() {
        return classifyName;
    }

    public void setClassifyName(String classifyName) {
        this.classifyName = classifyName;
    }

    public String getOutletName() {
        return outletName;
    }

    public void setOutletName(String outletName) {
        this.outletName = outletName;
    }

    public String getTop5() {
        return top5;
    }

    public void setTop5(String top5) {
        this.top5 = top5;
    }

    public String getFundCode15() {
        return fundCode15;
    }

    public void setFundCode15(String fundCode15) {
        this.fundCode15 = fundCode15;
    }

    public String getFundName15() {
        return fundName15;
    }

    public void setFundName15(String fundName15) {
        this.fundName15 = fundName15;
    }

    public int getCount15() {
        return count15;
    }

    public void setCount15(int count15) {
        this.count15 = count15;
    }

    public String getFundCode30() {
        return fundCode30;
    }

    public void setFundCode30(String fundCode30) {
        this.fundCode30 = fundCode30;
    }

    public String getFundName30() {
        return fundName30;
    }

    public void setFundName30(String fundName30) {
        this.fundName30 = fundName30;
    }

    public int getCount30() {
        return count30;
    }

    public void setCount30(int count30) {
        this.count30 = count30;
    }

    public String getFundCode60() {
        return fundCode60;
    }

    public void setFundCode60(String fundCode60) {
        this.fundCode60 = fundCode60;
    }

    public String getFundName60() {
        return fundName60;
    }

    public void setFundName60(String fundName60) {
        this.fundName60 = fundName60;
    }

    public int getCount60() {
        return count60;
    }

    public void setCount60(int count60) {
        this.count60 = count60;
    }

    public String getSearchWords() {
        return searchWords;
    }

    public void setSearchWords(String searchWords) {
        this.searchWords = searchWords;
    }

    public int getSearchCount() {
        return searchCount;
    }

    public void setSearchCount(int searchCount) {
        this.searchCount = searchCount;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
}
