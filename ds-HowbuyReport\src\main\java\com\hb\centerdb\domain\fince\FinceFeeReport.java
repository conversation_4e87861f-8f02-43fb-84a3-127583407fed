package com.hb.centerdb.domain.fince;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class FinceFeeReport implements Serializable {


    private static final long serialVersionUID = 6728054880654933552L;
    /**
     * @Description: 类型： 1-母基金互投，2-母基金投子基金
     * */
    private String tType;
    /**
     * @Description: 基金代码
     */
	private String fundCode;
    /**
     * @Description: 基金名称
     */
    private String fundName;
    /**
     * @Description: 业务代码
     */
	private String busiCode;
    /**
     * @Description: 业务类型
     */
    private String busiName;
    /**
     * @Description: 申请日期
     */
    private String appDt;
    /**
     * @Description: 确认日期
     */
	private String ackDt;
    /**
     * @Description: 所投基金代码
     */
	private String targetFundCode;
    /**
     * @Description: 所投基金名称
     */
	private String targetFundName;
    /**
     * @Description: 申请金额
     */
    private Double appAmt=0d;
    /**
     * @Description: 申请份额
     */
    private Double appVol =0d;
    /**
     * @Description: 确认金额
     */
    private Double ackAmt =0d;
    /**
     * @Description: 确认份额
     */
    private Double ackVol =0d;
    /**
     * @Description: 净值
     */
    private Double nav =0d;
    /**
     * @Description: 审核状态
     */
    private String approveStatus;
    /**
     * @Description: 手续费
     */
    private Double commissionFee=0d;
    /**
     * @Description: 业绩报酬
     */
	private Double performancePay=0d;

    public String gettType() {
        return tType;
    }

    public void settType(String tType) {
        this.tType = tType;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getBusiName() {
        return busiName;
    }

    public void setBusiName(String busiName) {
        this.busiName = busiName;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public String getTargetFundCode() {
        return targetFundCode;
    }

    public void setTargetFundCode(String targetFundCode) {
        this.targetFundCode = targetFundCode;
    }

    public String getTargetFundName() {
        return targetFundName;
    }

    public void setTargetFundName(String targetFundName) {
        this.targetFundName = targetFundName;
    }

    public Double getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(Double appAmt) {
        this.appAmt = appAmt;
    }

    public Double getAppVol() {
        return appVol;
    }

    public void setAppVol(Double appVol) {
        this.appVol = appVol;
    }

    public Double getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(Double ackAmt) {
        this.ackAmt = ackAmt;
    }

    public Double getAckVol() {
        return ackVol;
    }

    public void setAckVol(Double ackVol) {
        this.ackVol = ackVol;
    }

    public Double getNav() {
        return nav;
    }

    public void setNav(Double nav) {
        this.nav = nav;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Double getCommissionFee() {
        return commissionFee;
    }

    public void setCommissionFee(Double commissionFee) {
        this.commissionFee = commissionFee;
    }

    public Double getPerformancePay() {
        return performancePay;
    }

    public void setPerformancePay(Double performancePay) {
        this.performancePay = performancePay;
    }
}
