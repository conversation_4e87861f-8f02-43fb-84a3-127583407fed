package com.hb.crm.domain.pubfund;

import java.io.Serializable;

public class PubAcctAckHis implements Serializable {
	private String fundAcctNo;
	private String fundTxAcctNo;
	private String busiCode;
	private String taCode;
	private String taName ; 
	private String appDt;
	private String appTm; 	
	private String ackDt;
	private String tradeDt;
	private String retCode;
	private String retMsg;
	
	private String tradeChan;
	private String invstType;
	
	
	private String disCode;//分销机构号
	private String protocalNo;//协议号
	private String bankName;
	public String getFundAcctNo() {
		return fundAcctNo;
	}
	public void setFundAcctNo(String fundAcctNo) {
		this.fundAcctNo = fundAcctNo;
	}
	public String getFundTxAcctNo() {
		return fundTxAcctNo;
	}
	public void setFundTxAcctNo(String fundTxAcctNo) {
		this.fundTxAcctNo = fundTxAcctNo;
	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
	public String getTaCode() {
		return taCode;
	}
	public void setTaCode(String taCode) {
		this.taCode = taCode;
	}
	public String getTaName() {
		return taName;
	}
	public void setTaName(String taName) {
		this.taName = taName;
	}
	public String getAppDt() {
		return appDt;
	}
	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}
	public String getAppTm() {
		return appTm;
	}
	public void setAppTm(String appTm) {
		this.appTm = appTm;
	}
	public String getAckDt() {
		return ackDt;
	}
	public void setAckDt(String ackDt) {
		this.ackDt = ackDt;
	}
	public String getTradeDt() {
		return tradeDt;
	}
	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}
	public String getRetCode() {
		return retCode;
	}
	public void setRetCode(String retCode) {
		this.retCode = retCode;
	}
	public String getRetMsg() {
		return retMsg;
	}
	public void setRetMsg(String retMsg) {
		this.retMsg = retMsg;
	}
	public String getTradeChan() {
		return tradeChan;
	}
	public void setTradeChan(String tradeChan) {
		this.tradeChan = tradeChan;
	}
	public String getInvstType() {
		return invstType;
	}
	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}
	public String getDisCode() {
		return disCode;
	}
	public void setDisCode(String disCode) {
		this.disCode = disCode;
	}
	public String getProtocalNo() {
		return protocalNo;
	}
	public void setProtocalNo(String protocalNo) {
		this.protocalNo = protocalNo;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
}
