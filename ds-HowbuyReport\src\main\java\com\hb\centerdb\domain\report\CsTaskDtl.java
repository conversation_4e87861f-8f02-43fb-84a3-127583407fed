package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CsTaskDtl implements Serializable {

	private static final long serialVersionUID = 1L;

	private String bookingserialNo; // cms流水号

	private String custName; // 客户名称

	private String bookingDt; // 证件号码

	private String taskType; // 基金类型

	private String conscustNo; // 基金名称

	private String isCall; // 基金名称

	private String handleDate; // 基金名称

	private String calloutStatus; // 基金名称

	private String handleFlag; // 基金名称

	private String consName; // 基金名称

	private String isVirtual; // 当前投顾是否虚拟投顾

	private String priorCons; // 基金名称

	private String outletName; // 基金名称

	private String startDt; // 基金名称

	private String userIds; // 基金名称

	private String orderDate; // 基金名称

	private String taskId; // 基金名称

	private String sourceType; // 基金名称

	private String lastIsVirtual; // 上一个投顾是否为虚拟投顾

	private String isAssign; // 是否分配

	public String getBookingserialNo() {
		return bookingserialNo;
	}

	public void setBookingserialNo(String bookingserialNo) {
		this.bookingserialNo = bookingserialNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getBookingDt() {
		return bookingDt;
	}

	public void setBookingDt(String bookingDt) {
		this.bookingDt = bookingDt;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getIsCall() {
		return isCall;
	}

	public void setIsCall(String isCall) {
		this.isCall = isCall;
	}

	public String getHandleDate() {
		return handleDate;
	}

	public void setHandleDate(String handleDate) {
		this.handleDate = handleDate;
	}

	public String getCalloutStatus() {
		return calloutStatus;
	}

	public void setCalloutStatus(String calloutStatus) {
		this.calloutStatus = calloutStatus;
	}

	public String getHandleFlag() {
		return handleFlag;
	}

	public void setHandleFlag(String handleFlag) {
		this.handleFlag = handleFlag;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getIsVirtual() {
		return isVirtual;
	}

	public void setIsVirtual(String isVirtual) {
		this.isVirtual = isVirtual;
	}

	public String getPriorCons() {
		return priorCons;
	}

	public void setPriorCons(String priorCons) {
		this.priorCons = priorCons;
	}

	public String getOutletName() {
		return outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}

	public String getStartDt() {
		return startDt;
	}

	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}

	public String getUserIds() {
		return userIds;
	}

	public void setUserIds(String userIds) {
		this.userIds = userIds;
	}

	public String getOrderDate() {
		return orderDate;
	}

	public void setOrderDate(String orderDate) {
		this.orderDate = orderDate;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getLastIsVirtual() {
		return lastIsVirtual;
	}

	public void setLastIsVirtual(String lastIsVirtual) {
		this.lastIsVirtual = lastIsVirtual;
	}

	public String getIsAssign() {
		return isAssign;
	}

	public void setIsAssign(String isAssign) {
		this.isAssign = isAssign;
	}
}
