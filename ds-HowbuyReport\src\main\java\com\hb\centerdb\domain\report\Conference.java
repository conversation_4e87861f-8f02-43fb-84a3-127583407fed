package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class Conference implements Serializable {

	private static final long serialVersionUID = 1L;

	public String conferenceId;//会议ID
	public String conferenceDt;//会议日期
	public String conferenceOrg;
	public String conferenceOrgNa;//举办部门
	public String conferenceType;
	public String conferenceTypeNa;//会议类型
	public String conferenceName;//会议名称
	public String conferenceAddr;//会议地址
	public String isOnline;//是否支持线上报名
	public String conferenceOrgCode;
	public String conferenceOrgName;
	public int conferenceMaxNub;//人数上限
	public int conferenceOrgeNub;//报名人数
	public int actualCont;//实际参会人数

	/*近90天累计打款人数*/
	public int custTotalNum90;
	/*近90天累计打款金额*/
	public Double realpayTotalAmtSum90;
	/*近90天累计打款产品*/
	public String realpayTotalFund90;

	public int custNum30;//30天预约人数
	public Double buyAmtSum30;//30天预约金额

	public int realpayCustNum30;//30天打款人数
	public Double realpayAmtSum30;//30天打款金额
	public String realpayFund30;//30天打款产品

	public int realpayCustNum60;//60天打款人数
	public Double realpayAmtSum60;//60天打款金额
	public String realpayFund60;//60天打款产品

	public int realpayCustNum90;//90天打款人数
	public Double realpayAmtSum90;/*打款金额-90天*/
	public String realpayFund90;/*打款产品-90天*/

	/*预约的客户数*/
	public int custNum60;
    public int custNum90;

	public Double buyAmtSum60;
    /*预约金额-90天*/
    public Double buyAmtSum90;
	
	public String getRealpayFund30() {
		return realpayFund30;
	}

	public void setRealpayFund30(String realpayFund30) {
		this.realpayFund30 = realpayFund30;
	}

	public String getRealpayFund60() {
		return realpayFund60;
	}

	public void setRealpayFund60(String realpayFund60) {
		this.realpayFund60 = realpayFund60;
	}

	public String getConferenceOrg() {
		return conferenceOrg;
	}

	public void setConferenceOrg(String conferenceOrg) {
		this.conferenceOrg = conferenceOrg;
	}

	public String getConferenceOrgNa() {
		return conferenceOrgNa;
	}

	public void setConferenceOrgNa(String conferenceOrgNa) {
		this.conferenceOrgNa = conferenceOrgNa;
	}

	public int getConferenceMaxNub() {
		return conferenceMaxNub;
	}

	public void setConferenceMaxNub(int conferenceMaxNub) {
		this.conferenceMaxNub = conferenceMaxNub;
	}

	public String getConferenceId() {
		return conferenceId;
	}

	public void setConferenceId(String conferenceId) {
		this.conferenceId = conferenceId;
	}

	public String getConferenceDt() {
		return conferenceDt;
	}

	public void setConferenceDt(String conferenceDt) {
		this.conferenceDt = conferenceDt;
	}

	public String getConferenceType() {
		return conferenceType;
	}

	public void setConferenceType(String conferenceType) {
		this.conferenceType = conferenceType;
	}

	public String getConferenceTypeNa() {
		return conferenceTypeNa;
	}

	public void setConferenceTypeNa(String conferenceTypeNa) {
		this.conferenceTypeNa = conferenceTypeNa;
	}

	public String getConferenceName() {
		return conferenceName;
	}

	public void setConferenceName(String conferenceName) {
		this.conferenceName = conferenceName;
	}

	public String getConferenceAddr() {
		return conferenceAddr;
	}

	public void setConferenceAddr(String conferenceAddr) {
		this.conferenceAddr = conferenceAddr;
	}

	public String getConferenceOrgCode() {
		return conferenceOrgCode;
	}

	public void setConferenceOrgCode(String conferenceOrgCode) {
		this.conferenceOrgCode = conferenceOrgCode;
	}

	public String getConferenceOrgName() {
		return conferenceOrgName;
	}

	public void setConferenceOrgName(String conferenceOrgName) {
		this.conferenceOrgName = conferenceOrgName;
	}

	public int getConferenceOrgeNub() {
		return conferenceOrgeNub;
	}

	public void setConferenceOrgeNub(int conferenceOrgeNub) {
		this.conferenceOrgeNub = conferenceOrgeNub;
	}

	public int getActualCont() {
		return actualCont;
	}

	public void setActualCont(int actualCont) {
		this.actualCont = actualCont;
	}

	public int getCustNum30() {
		return custNum30;
	}

	public void setCustNum30(int custNum30) {
		this.custNum30 = custNum30;
	}

	public int getCustNum60() {
		return custNum60;
	}

	public void setCustNum60(int custNum60) {
		this.custNum60 = custNum60;
	}

	public Double getBuyAmtSum30() {
		return buyAmtSum30;
	}

	public void setBuyAmtSum30(Double buyAmtSum30) {
		this.buyAmtSum30 = buyAmtSum30;
	}

	public Double getBuyAmtSum60() {
		return buyAmtSum60;
	}

	public void setBuyAmtSum60(Double buyAmtSum60) {
		this.buyAmtSum60 = buyAmtSum60;
	}

	public Double getRealpayAmtSum30() {
		return realpayAmtSum30;
	}

	public void setRealpayAmtSum30(Double realpayAmtSum30) {
		this.realpayAmtSum30 = realpayAmtSum30;
	}

	public Double getRealpayAmtSum60() {
		return realpayAmtSum60;
	}

	public void setRealpayAmtSum60(Double realpayAmtSum60) {
		this.realpayAmtSum60 = realpayAmtSum60;
	}

    public int getCustNum90() {
        return custNum90;
    }

    public void setCustNum90(int custNum90) {
        this.custNum90 = custNum90;
    }

    public Double getBuyAmtSum90() {
        return buyAmtSum90;
    }

    public void setBuyAmtSum90(Double buyAmtSum90) {
        this.buyAmtSum90 = buyAmtSum90;
    }

    public Double getRealpayAmtSum90() {
        return realpayAmtSum90;
    }

    public void setRealpayAmtSum90(Double realpayAmtSum90) {
        this.realpayAmtSum90 = realpayAmtSum90;
    }

    public String getRealpayFund90() {
        return realpayFund90;
    }

    public void setRealpayFund90(String realpayFund90) {
        this.realpayFund90 = realpayFund90;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getIsOnline() {
		return isOnline;
	}

	public void setIsOnline(String isOnline) {
		this.isOnline = isOnline;
	}

	public int getCustTotalNum90() {
		return custTotalNum90;
	}

	public void setCustTotalNum90(int custTotalNum90) {
		this.custTotalNum90 = custTotalNum90;
	}

	public Double getRealpayTotalAmtSum90() {
		return realpayTotalAmtSum90;
	}

	public void setRealpayTotalAmtSum90(Double realpayTotalAmtSum90) {
		this.realpayTotalAmtSum90 = realpayTotalAmtSum90;
	}

	public String getRealpayTotalFund90() {
		return realpayTotalFund90;
	}

	public void setRealpayTotalFund90(String realpayTotalFund90) {
		this.realpayTotalFund90 = realpayTotalFund90;
	}

	public int getRealpayCustNum30() {
		return realpayCustNum30;
	}

	public void setRealpayCustNum30(int realpayCustNum30) {
		this.realpayCustNum30 = realpayCustNum30;
	}

	public int getRealpayCustNum60() {
		return realpayCustNum60;
	}

	public void setRealpayCustNum60(int realpayCustNum60) {
		this.realpayCustNum60 = realpayCustNum60;
	}

	public int getRealpayCustNum90() {
		return realpayCustNum90;
	}

	public void setRealpayCustNum90(int realpayCustNum90) {
		this.realpayCustNum90 = realpayCustNum90;
	}
}
