package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class OrgBal implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custNo; // 客户号

	private String custName; // 客户名称

	private String idNo; // 证件号码

	private String fundType; // 基金类型
	
	private String fundTypeName; // 基金名称

	private Double balanceVol = 0d; // 总持有份额

	private String tradeDt; // 统计日期

	private Double marketAmt = 0d; // 基金市值

	private Double income = 0d; // 预计未结转收益

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getFundType() {
		return fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}
	
	public String getFundTypeName() {
		return fundTypeName;
	}

	public void setFundTypeName(String fundTypeName) {
		this.fundTypeName = fundTypeName;
	}

	public Double getBalanceVol() {
		return balanceVol;
	}

	public void setBalanceVol(Double balanceVol) {
		this.balanceVol = balanceVol;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public Double getMarketAmt() {
		return marketAmt;
	}

	public void setMarketAmt(Double marketAmt) {
		this.marketAmt = marketAmt;
	}

	public Double getIncome() {
		return income;
	}

	public void setIncome(Double income) {
		this.income = income;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
