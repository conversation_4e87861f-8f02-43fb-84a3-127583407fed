package com.hb.crm.domain.report.fundnavreport;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderDetailReport implements Serializable {
	private static final long serialVersionUID = -4122258231586419967L;
	private String id;
    private String hbtype;
    private String managerMan;// 公司名称(管理人)
    private String saleType;//销售类型
    private String prebookcheckman;//预约审核人
	private String expecttradedt;// 合同日期（预计交易日期）
	private String consCustName;// 客户姓名
	private String conscustno;//客户号
	private String conscode;// 投顾号
	/**
	 * 中心
	 */
	private String centerOrg;
	private String custManager;// 所属投顾
	private String departCode;// 部门
	private BigDecimal orderAmt = new BigDecimal(0);// 预约金额
	private BigDecimal orderAmtRmb = new BigDecimal(0);// 预约金额
	private BigDecimal orderAmtUs = new BigDecimal(0);// 预约金额
	private String expectpayamtdt;// 预计打款日期
	private String realpayamtdt;// 实际打款日期
	private BigDecimal realpayamt = new BigDecimal(0);// 实际打款金额
	private BigDecimal realpayamtRmb = new BigDecimal(0);// 实际打款金额
	private BigDecimal realpayamtUs = new BigDecimal(0);// 实际打款金额
	private String prebookstate;// 产品预约状态
	private String isonline;// 产品预约状态
	//2014.1.28 add 产品名称
	private String pname;
    private String pcode;//产品code
	private String precode;//预约码
	private String calmdatetime;
	private String zczmstate;
	private String zczmnotes;
	private String pretype;
	private String doublereturnstatus;
	// add by shiping.lin 20170816 增加确认打款金额
	private BigDecimal pay3amt = new BigDecimal(0);// 实际打款金额
    private BigDecimal disPay3amt = new BigDecimal(0);// 确认折标销量
    private String disCount;
	private String preName;
	
	// add by shiping.lin 20180307 增加 确认到账手续费（元） 支付方式
	private BigDecimal fee = new BigDecimal(0);// 实际打款金额
	private String paymentType;
	private String redeemDirection;
	
	//private BigDecimal callTotalAmt  = new BigDecimal(0);// 累计缴款金额	
	//private String callNum;// Call款情况
	//private BigDecimal callAmt = new BigDecimal(0);// 分次 call认缴金额
	private String callId;
	private String fccl;
	private String spectradeType;
    //增加双录状态
	private String slStatus;
	
	private String creDtTime;

    private int holdDays = 0;// 持有天数

    private int orderNums;//赎回客户数

    private BigDecimal jjjz = new BigDecimal(0);// 基金净值

    private BigDecimal jjsz = new BigDecimal(0);// 基金市值

    private String jjrq;// 基金净值日期

    private String disType;//折扣类型
    private BigDecimal disCountRate = new BigDecimal(0);// 折扣率
    private String currency;//币种

    private String fundTypeInner;//产品类型(对内)

    private String u1name;//一级部门
    private String u2name;//区域(二级部门)
	/**
	 * 部门
	 */
	private String outletcode;
	/**
	 * 创建人
	 */
	private String creator;

	private String bqxlmc; //一级策略
	private String sftzhw; //是否投资海外
	private BigDecimal hwzczbxx = new BigDecimal(0);//海外资产占比下限（%）
	private BigDecimal hwzczbsx = new BigDecimal(0);//海外资产占比上限（%）
	private BigDecimal hwzczb = new BigDecimal(0);//海外资产占比

	public String getOutletcode() {
		return outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreDtTime() {
		return creDtTime;
	}
	public void setCreDtTime(String creDtTime) {
		this.creDtTime = creDtTime;
	}
	public String getSpectradeType() {
		return spectradeType;
	}
	public void setSpectradeType(String spectradeType) {
		this.spectradeType = spectradeType;
	}
	
	public String getFccl() {
		return fccl;
	}
	public void setFccl(String fccl) {
		this.fccl = fccl;
	}
	
	public String getCallId() {
		return callId;
	}
	public void setCallId(String callId) {
		this.callId = callId;
	}
/*	public BigDecimal getCallAmt() {
		return callAmt;
	}
	public void setCallAmt(BigDecimal callAmt) {
		this.callAmt = callAmt;
	}
	
	public BigDecimal getCallTotalAmt() {
		return callTotalAmt;
	}
	public void setCallTotalAmt(BigDecimal callTotalAmt) {
		this.callTotalAmt = callTotalAmt;
	}
	public String getCallNum() {
		return callNum;
	}
	public void setCallNum(String callNum) {
		this.callNum = callNum;
	}*/
	
	public String getRedeemDirection() {
		return redeemDirection;
	}
	public void setRedeemDirection(String redeemDirection) {
		this.redeemDirection = redeemDirection;
	}
	public BigDecimal getFee() {
		return fee;
	}
	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}
	public String getPaymentType() {
		return paymentType;
	}
	
	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}
	public String getIsonline() {
		return isonline;
	}
	public void setIsonline(String isonline) {
		this.isonline = isonline;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getExpecttradedt() {
		return expecttradedt;
	}
	public void setExpecttradedt(String expecttradedt) {
		this.expecttradedt = expecttradedt;
	}
	public String getConsCustName() {
		return consCustName;
	}
	public void setConsCustName(String consCustName) {
		this.consCustName = consCustName;
	}
	public String getConscustno() {
		return conscustno;
	}
	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	public String getConscode() {
		return conscode;
	}
	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	public String getCustManager() {
		return custManager;
	}
	public void setCustManager(String custManager) {
		this.custManager = custManager;
	}
	public String getDepartCode() {
		return departCode;
	}
	public void setDepartCode(String departCode) {
		this.departCode = departCode;
	}
	public BigDecimal getOrderAmt() {
		return orderAmt;
	}
	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}
	public String getExpectpayamtdt() {
		return expectpayamtdt;
	}
	public void setExpectpayamtdt(String expectpayamtdt) {
		this.expectpayamtdt = expectpayamtdt;
	}
	public String getRealpayamtdt() {
		return realpayamtdt;
	}
	public void setRealpayamtdt(String realpayamtdt) {
		this.realpayamtdt = realpayamtdt;
	}
	public BigDecimal getRealpayamt() {
		return realpayamt;
	}
	public void setRealpayamt(BigDecimal realpayamt) {
		this.realpayamt = realpayamt;
	}
	public String getPrebookstate() {
		return prebookstate;
	}
	public void setPrebookstate(String prebookstate) {
		this.prebookstate = prebookstate;
	}
	public String getPname() {
		return pname;
	}
	public void setPname(String pname) {
		this.pname = pname;
	}
	public String getPrecode() {
		return precode;
	}
	public void setPrecode(String precode) {
		this.precode = precode;
	}
	public BigDecimal getOrderAmtRmb() {
		return orderAmtRmb;
	}
	public void setOrderAmtRmb(BigDecimal orderAmtRmb) {
		this.orderAmtRmb = orderAmtRmb;
	}
	public BigDecimal getOrderAmtUs() {
		return orderAmtUs;
	}
	public void setOrderAmtUs(BigDecimal orderAmtUs) {
		this.orderAmtUs = orderAmtUs;
	}
	public BigDecimal getRealpayamtRmb() {
		return realpayamtRmb;
	}
	public void setRealpayamtRmb(BigDecimal realpayamtRmb) {
		this.realpayamtRmb = realpayamtRmb;
	}
	public BigDecimal getRealpayamtUs() {
		return realpayamtUs;
	}
	public void setRealpayamtUs(BigDecimal realpayamtUs) {
		this.realpayamtUs = realpayamtUs;
	}
	public String getCalmdatetime() {
		return calmdatetime;
	}
	public void setCalmdatetime(String calmdatetime) {
		this.calmdatetime = calmdatetime;
	}
	public String getZczmstate() {
		return zczmstate;
	}
	public void setZczmstate(String zczmstate) {
		this.zczmstate = zczmstate;
	}
	public String getZczmnotes() {
		return zczmnotes;
	}
	public void setZczmnotes(String zczmnotes) {
		this.zczmnotes = zczmnotes;
	}
	public String getPretype() {
		return pretype;
	}
	public void setPretype(String pretype) {
		this.pretype = pretype;
	}
	public String getDoublereturnstatus() {
		return doublereturnstatus;
	}
	public BigDecimal getPay3amt() {
		return pay3amt;
	}
	public void setPay3amt(BigDecimal pay3amt) {
		this.pay3amt = pay3amt;
	}
	public void setDoublereturnstatus(String doublereturnstatus) {
		this.doublereturnstatus = doublereturnstatus;
	}
	public String getPreName() {
		return preName;
	}
	public void setPreName(String preName) {
		this.preName = preName;
	}

    public String getSlStatus() {
        return slStatus;
    }

    public void setSlStatus(String slStatus) {
        this.slStatus = slStatus;
    }

    public int getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(int holdDays) {
        this.holdDays = holdDays;
    }

    public int getOrderNums() {
        return orderNums;
    }

    public void setOrderNums(int orderNums) {
        this.orderNums = orderNums;
    }

    public BigDecimal getDisPay3amt() {
        return disPay3amt;
    }

    public void setDisPay3amt(BigDecimal disPay3amt) {
        this.disPay3amt = disPay3amt;
    }

    public String getDisCount() {
        return disCount;
    }

    public void setDisCount(String disCount) {
        this.disCount = disCount;
    }

    public BigDecimal getJjjz() {
        return jjjz;
    }

    public void setJjjz(BigDecimal jjjz) {
        this.jjjz = jjjz;
    }

    public BigDecimal getJjsz() {
        return jjsz;
    }

    public void setJjsz(BigDecimal jjsz) {
        this.jjsz = jjsz;
    }

    public String getHbtype() {
        return hbtype;
    }

    public void setHbtype(String hbtype) {
        this.hbtype = hbtype;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getPrebookcheckman() {
        return prebookcheckman;
    }

    public void setPrebookcheckman(String prebookcheckman) {
        this.prebookcheckman = prebookcheckman;
    }

    public String getJjrq() {
        return jjrq;
    }

    public void setJjrq(String jjrq) {
        this.jjrq = jjrq;
    }

    public String getDisType() {
        return disType;
    }

    public void setDisType(String disType) {
        this.disType = disType;
    }

    public BigDecimal getDisCountRate() {
        return disCountRate;
    }

    public void setDisCountRate(BigDecimal disCountRate) {
        this.disCountRate = disCountRate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPcode() {
        return pcode;
    }

    public void setPcode(String pcode) {
        this.pcode = pcode;
    }

    public String getFundTypeInner() {
        return fundTypeInner;
    }

    public void setFundTypeInner(String fundTypeInner) {
        this.fundTypeInner = fundTypeInner;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getManagerMan() {
        return managerMan;
    }

    public void setManagerMan(String managerMan) {
        this.managerMan = managerMan;
    }

	public String getBqxlmc() {
		return bqxlmc;
	}

	public void setBqxlmc(String bqxlmc) {
		this.bqxlmc = bqxlmc;
	}

	public String getSftzhw() {
		return sftzhw;
	}

	public void setSftzhw(String sftzhw) {
		this.sftzhw = sftzhw;
	}

	public BigDecimal getHwzczbxx() {
		return hwzczbxx;
	}

	public void setHwzczbxx(BigDecimal hwzczbxx) {
		this.hwzczbxx = hwzczbxx;
	}

	public BigDecimal getHwzczbsx() {
		return hwzczbsx;
	}

	public void setHwzczbsx(BigDecimal hwzczbsx) {
		this.hwzczbsx = hwzczbsx;
	}

	public BigDecimal getHwzczb() {
		return hwzczb;
	}

	public void setHwzczb(BigDecimal hwzczb) {
		this.hwzczb = hwzczb;
	}

	public String getCenterOrg() {
		return centerOrg;
	}

	public void setCenterOrg(String centerOrg) {
		this.centerOrg = centerOrg;
	}
}
