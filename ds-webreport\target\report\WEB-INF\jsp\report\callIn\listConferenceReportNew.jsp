<%@ page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<%@ page import="com.hb.crm.web.tools.ContextManager"%>
<%
    String userId = request.getParameter("userId");
    Date date = new Date();
    request.setAttribute("date", date);
%>
<!Doctype html><html xmlns=http://www.w3.org/1999/xhtml>
<%@ include file='/WEB-INF/jsp/common/include.jsp'%>
<link href="<%=request.getContextPath()%>/StaticResource/multijs/css.css" rel="stylesheet" type="text/css" />
<link href="<%=request.getContextPath()%>/StaticResource/multijs/style.css" rel="stylesheet" type="text/css" />
<link href="<%=request.getContextPath()%>/StaticResource/multijs/bigpage.css" rel="stylesheet" type="text/css" />
<link href="<%=request.getContextPath()%>/StaticResource/multijs/jquery-ui.css" rel="stylesheet" type="text/css" />
<link href="<%=request.getContextPath()%>/StaticResource/multijs/jquery.multiselect.css" rel="stylesheet" type="text/css" />
<link href="<%=request.getContextPath()%>/StaticResource/multijs/jquery.multiselect.filter.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/common.js"></script>

<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/jquery-openwindow.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/dataDic.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/jquery.multiselect.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/jquery.multiselect.filter.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/jquery.multiselect.zh-cn.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/StaticResource/multijs/jquery.multiselect.filter.zh-cn.js"></script>

<head>
    <title>路演参会情况跟踪报表(新)</title>
    <script type="text/javascript">
        $(window).resize(function(){
            $('#dg').datagrid('resize');
        });

        $(function () {
            $('#query_conferenceType').combotree({
                valueField: "id", //Value字段
                textField: "text", //Text字段
                multiple: true,
                data: [{"id": "1", "text": "沙龙"},{"id": "2", "text": "月度"},{"id": "3", "text": "季度"},
                    {"id": "4", "text": "年度"},{"id": "6", "text": "至臻年会"},{"id": "9", "text": "理财九章","children": [{"id": "9a", "text": "理财九章-长宽高"},
                            {"id": "9b", "text": "理财九章-财富健康度"},{"id": "9c", "text": "理财九章-大类-股票"},{"id": "9d", "text": "理财九章-大类-固收"},
                            {"id": "9e", "text": "理财九章-大类-股权"},{"id": "9f", "text": "理财九章-大类-FOF"},{"id": "9g", "text": "理财九章-大类-海外"},
                            {"id": "9h", "text": "理财九章-大类-保险"},{"id": "9i", "text": "理财九章-大类-家办"}]}
                    ],
                //                url: "tree_data2.json", //数据源
                onCheck: function (node, checked) {
                    //让全选不显示
                    $("#query_conferenceType").combotree("setText", $("#query_conferenceType").combobox("getText").toString().replace("All,", ""));
                },
                onClick: function (node, checked) {
                    //让全选不显示
                    $("#query_conferenceType").combotree("setText", $("#query_conferenceType").combobox("getText").toString().replace("All,", ""));
                }
            });
        });
        var selectedValue;
        $(document).ready(function() {
            $("input[name='options']").click(function() {
                $("input[name='options']").not(this).prop("checked", false);
            });
            $("input[name='options']").each(function() {
                if (this.checked) {
                    selectedValue= this.value;
                    // 在这里编写代码，例如打印选中的值
                }
            })
            ;
        });

        $(function() {
            // 初始化Datagrid组建
            userDatagrid = $('#dg').datagrid({
                fit : true,
                fitColumns:false,
                url:"<%=request.getContextPath()%>/report/listConferenceNew_json.do",
                loadMsg: "正在加载...,请稍后！",
                toolbar:'#tb',
                nowrap: true,
                pageSize : 20,
                pageList : [ 20,50,100,500,1000],
                resizable: true,
                striped: true,  //设置为true将交替显示行背景。
                remoteSort: true,
                singleSelect:true,
                pagination:true,
                columns:[[
                    {field:'conferenceId',title:'会议ID',width:60,align:'left'},
                    {field:'conferenceName',title:'会议名称',width:200,align:'left'},
                    {field:'conferenceType',title:'会议类型',width:100,align:'left',hidden:true},
                    {field:'conferenceTypeNa',title:'会议类型',width:100,align:'left'},
                    {field:'conferenceOrgNa',title:'举办部门',width:100,align:'left'},
                    {field:'conferenceDt',title:'会议日期',width:100,align:'left'},
                    {field:'conferenceAddr',title:'会议地址',width:120,align:'left'},
                    {field:'isOnline',title:'是否支持线上报名',width:80,align:'left'},
                    {field:'conferenceMaxNub',title:'人数上限',width:80,align:'left'},
                    {field:'conferenceOrgeNub',title:'报名人数',width : 80,align:'center' },
                    {field:'actualCont',title:'实际参会人数',width:80,align:'left',
                        formatter: function(value,rowData,rowIndex){
                            var url = "<%=request.getContextPath()%>/report/listConferenceDtlNew.do?conferenceType="+rowData.conferenceType+"&conferenceOrgCode="+rowData.conferenceOrgCode+"&conferenceOrgName="+encodeURIComponent(encodeURIComponent(rowData.conferenceOrgName))+"&conferenceName="+encodeURIComponent(encodeURIComponent(rowData.conferenceName))+"&query_orgCode="+$('#query_orgCode').combobox("getValue")+"&query_consCode="+$('#query_consCode').combobox("getValue")+"&query_conferenceIds="+rowData.conferenceId+"&conferenceBegindt="+$('#conferenceBegindt').val()+"&conferenceEndDt="+$('#conferenceEndDt').val()+"&conferenceDt="+rowData.conferenceDt+"&conferenceSelect="+selectedValue;
                            var url1 = "<%=request.getContextPath()%>/report/listConferenceDtlNew.do?conferenceType="+$('#query_conferenceType').combobox("getValues").join()+"&query_orgCode="+$('#query_orgCode').combobox("getValue")+"&query_consCode="+$('#query_consCode').combobox("getValue")+"&query_conferenceIds="+$('#query_conferenceIds').combobox("getValue")+"&conferenceName="+encodeURIComponent(encodeURIComponent($('#query_conferenceIds').combobox("getText")))+"&conferenceOrgName="+encodeURIComponent(encodeURIComponent($('#query_orgCode').combobox("getText")))+"&conferenceBegindt="+$('#conferenceBegindt').val()+"&conferenceEndDt="+$('#conferenceEndDt').val()+"&conferenceSelect="+selectedValue;


                            var updateStr='';
                            if(rowData.conferenceName != null && rowData.actualCont != 0 && rowData.conferenceName != '' ){
                                updateStr='<a style="color:red;" href="'+url+'" target="_blank" >'+value+'</a>&nbsp;';
                            }else if(rowData.conferenceName != null && rowData.actualCont == 0 ){
                                updateStr=rowData.actualCont;
                            }else if(rowData.conferenceName == null && rowData.actualCont != 0 ){
                                updateStr='<a style="color:red;" href="'+url1+'" target="_blank" >'+value+'</a>&nbsp;';
                            }
                            else{
                                updateStr=rowData.actualCont;
                            }
                            return updateStr;
                        }

                    },
                    {field:'custTotalNum90',title:'近90天累计打款人数',width:100,align:'left'},
                    {field:'realpayTotalAmtSum90',title:'近90天累计打款金额',width:120,align:'left',
                        formatter:function(value,rowData,rowIndex){
                            if(value != null){
                                return formatCash(value);
                            }else{
                                return '0';
                            }
                        }
                    },
                    //{field:'realpayTotalFund90',title:'近90天累计打款产品',width:180,align:'left'},
                    {field:'custNum30',title:'30天预约人数',width:100,align:'left'},
                    {field:'buyAmtSum30',title:'30天预约金额',width:120,align:'left',
                        formatter:function(value,rowData,rowIndex){
                            if(value != null){
                                return formatCash(value);
                            }else{
                                return '0';
                            }
                        }
                    },
                    {field:'realpayCustNum30',title:'30天打款人数',width:100,align:'left'},
                    {field:'realpayAmtSum30',title:'30天打款金额',width:120,align:'left',
                        formatter:function(value,rowData,rowIndex){
                            if(value != null){
                                return formatCash(value);
                            }else{
                                return '0';
                            }
                        }
                    },
                    {field:'realpayFund30',title:'30天打款产品',width:180,align:'left'},
                    {field:'realpayCustNum60',title:'60天打款人数',width:100,align:'left'},
                    {field:'realpayAmtSum60',title:'60天打款金额',width:120,align:'left',
                        formatter:function(value,rowData,rowIndex){
                            if(value != null){
                                return formatCash(value);
                            }else{
                                return '0';
                            }
                        }
                    },
                    {field:'realpayFund60',title:'60天打款产品',width:180,align:'left'},
                    {field:'realpayCustNum90',title:'90天打款人数',width:100,align:'left'},
                    {field:'realpayAmtSum90',title:'90天打款金额',width:120,align:'left',
                        formatter:function(value,rowData,rowIndex){
                            if(value != null){
                                return formatCash(value);
                            }else{
                                return '0';
                            }
                        }
                    },
                    {field:'realpayFund90',title:'90天打款产品',width:180,align:'left'}
                ]]
            });
        });

        // 查询操作
        var toQuery=function(){
            var queryParams = $('#dg').datagrid('options').queryParams;

            if($('#query_orgCode').combobox("getValue")){
                queryParams.orgCode = $('#query_orgCode').combobox("getValue");
            }else {
                queryParams.orgCode = null;
            }

            if($("#query_consCode").combobox("getValue")){
                queryParams.consCode = $("#query_consCode").combobox("getValue");
            }else{
                queryParams.consCode = null;
            }

            if($('#query_conferenceType').combobox("getValue")){
                queryParams.conferenceType = $('#query_conferenceType').combobox("getValues").join();
            }else {
                queryParams.conferenceType = null;
            }

            if($("#query_conferenceIds").combobox("getValue")){
                queryParams.conferenceIds = $("#query_conferenceIds").combobox("getValue");
            }else{
                queryParams.conferenceIds=null;
            }

            if($("#query_conferenceIds").combobox("getValue")){
                queryParams.conferenceNames = $("#query_conferenceIds").combobox("getText");
            }else{
                queryParams.conferenceNames=null;
            }

            if($('#conferenceBegindt').val()) {
                queryParams.conferenceBegindt = $('#conferenceBegindt').val();
            }else{
                queryParams.conferenceBegindt = null; //20210901增加路演日期
            }
            if($('#conferenceEndDt').val()) {
                queryParams.conferenceEndDt = $('#conferenceEndDt').val();
            }else{
                queryParams.conferenceEndDt = null;
            }

            $("input[name='options']").each(function() {
                if (this.checked) {
                    selectedValue = this.value;
                    // 在这里编写代码，例如打印选中的值
                    queryParams.conferenceSelect = selectedValue;
                }
            })
            ;

            $("#dg").datagrid('load');
        };

        // 导出操作
        function toExport(){
            var orgCode = null;
            var conferenceType = null;
            var conferenceName = null;
            var orgCodeName = null;
            var conferenceTypeNames = null;
            var conferenceTypeIds = null;
            var consCode = null;
            var consName = null;
            var conferenceBegindt=null;
            var conferenceEndDt=null;
            var conferenceSelect=null;

            var queryParams = $('#dg').datagrid('options').queryParams;

            if($('#query_orgCode').combobox("getValue")){
                queryParams.orgCode = $('#query_orgCode').combobox("getValue");
            }else {
                queryParams.orgCode = null;
            }
            if($("#query_consCode").combobox("getValue")){
                queryParams.consCode = $("#query_consCode").combobox("getValue");
            }else{
                queryParams.consCode = null;
            }

            if($('#query_conferenceType').combobox("getValue")){
                queryParams.conferenceType = $('#query_conferenceType').combobox("getValues").join();
            }else {
                queryParams.conferenceType = null;
            }

            if($("#query_conferenceIds").combobox("getValue")){
                queryParams.conferenceIds = $("#query_conferenceIds").combobox("getValue");
            }else{
                queryParams.conferenceIds=null;
            }
            if($("#query_conferenceIds").combobox("getValue")){
                queryParams.conferenceNames = $("#query_conferenceIds").combobox("getText");
            }else{
                queryParams.conferenceNames=null;
            }
            //query_conferenceId

            if($("#query_orgCode").combobox("getValue")){
                queryParams.orgCodeName = $("#query_orgCode").combobox("getText");
            }else{
                queryParams.orgCodeName = "HowBuy";
            }
            if($("#query_conferenceType").combobox("getValue")){
                queryParams.conferenceTypeName = $("#query_conferenceType").combobox("getText");
            }else {
                queryParams.conferenceTypeName = "全部";
            }
            if($("#query_consCode").combobox("getValue")){
                queryParams.consName = $("#query_consCode").combobox("getText");
            }else{
                queryParams.consName = "全部";
            }
            if($('#conferenceBegindt').val()) {
                queryParams.conferenceBegindt = $('#conferenceBegindt').val();
            }else{
                queryParams.conferenceBegindt = null; //20210901增加路演日期
            }
            if($('#conferenceEndDt').val()) {
                queryParams.conferenceEndDt = $('#conferenceEndDt').val();
            }else{
                queryParams.conferenceEndDt = null;
            }

            $("input[name='options']").each(function() {
                if (this.checked) {
                    var selectedValue = this.value;
                    // 在这里编写代码，例如打印选中的值
                    queryParams.conferenceSelect = selectedValue;
                }
            })
            ;

            $('#ConferenceForm').form('submit',{
                url : "<%=request.getContextPath()%>/report/exportConferenceNew.do",
                onSubmit:function(param){
                    param.orgCode = queryParams.orgCode;
                    param.conferenceType = queryParams.conferenceType;
                    param.conferenceNames = queryParams.conferenceNames;
                    param.conferenceIds = queryParams.conferenceIds;
                    param.orgCodeName = queryParams.orgCodeName;
                    param.conferenceTypeName = queryParams.conferenceTypeName;
                    param.consCode = queryParams.consCode;
                    param.consName = queryParams.consName;
                    param.conferenceBegindt = queryParams.conferenceBegindt;
                    param.conferenceEndDt = queryParams.conferenceEndDt;
                    param.conferenceSelect = queryParams.conferenceSelect;
                },
                success : function(data) {
                    var result = $.parseJSON(data);
                    if (result.msg=="success") {
                        $.messager.alert('操作提示',"导出成功！",'info');
                    } else if (result.msg=="zero"){
                        $.messager.alert('操作提示',"导出数据为空！",'info');
                    }  else{
                        $.messager.alert('操作提示',"数据出现异常，请重新操作！",'error');
                    }
                }
            });
        }

        // 清空操作
        var toClear=function(){
            $("#query_conferenceType").combobox("setValue","");
            $("#query_conferenceIds").combobox("setValue","");

        };

        //
        var conferenceNameLoader = function(param,success,error){
            var q = param.q || '';
            if (q.length < 1){return false;}
            $.ajax({
                type:'POST',
                url: '<%=request.getContextPath()%>/sys/autoLoadConference.do',
                dataType: 'json',
                data: {
                    featureClass: "P",
                    style: "full",
                    maxRows: 20,
                    term: q
                },
                success: function(data){
                    var items = $.map(data.result, function(item){
                        return {
                            id: item.conferenceId,
                            name: item.conferenceName
                        };

                    });
                    success(items);
                },
                error: function(){
                    error.apply(this, arguments);
                }
            });
        };

        function ajax_post(url, param, callback){
            $.ajax({
                type:'POST',
                url:url,
                data:param,
                contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                success:callback,
                error:function(html){
                }
            });
        }

        function formatCash( cash ) {
            var str_cash = cash + "";//转换成字符串
            var ret_cash = "";
            var counter = 0;
            var dot_index = str_cash.indexOf(".");
            for(var i=str_cash.length-1;i>=0;i--){
                ret_cash = str_cash.charAt(i) + ret_cash;
                if(str_cash.charAt(i)==".") {
                    counter = 0;
                    continue;
                }
                counter++;
                if(counter==3){
                    counter = 0;
                    // 小数点后面不加逗号
                    if(i!=0 && (i < dot_index || dot_index < 0))
                        ret_cash = "," + ret_cash;
                }
            }
            return ret_cash;
        }

        var setConsCode = function(){
            var userId = '${userId}';
            $.post("<%=request.getContextPath()%>/system/getConsCodeComboxValue.do",{userId:userId}).done(function(data){
                $("#query_consCode").combobox('select',data);
            })
        };

        setTimeout(setConsCode,200);

    </script>
</head>
<body>

<div id="tb" style="padding:1px;height:auto">
    <form id="ConferenceForm" name="ConferenceForm" method="post"  action="" >
        <table class="table-search">
            <tr>

                <td style="width:8%" align="right" class="table-search-title">参会部门:</td>
                <td style="width:320px;" nowrap="nowrap">
                    <crm:SelectOrganAllCons consName="query_consCode" orgName="query_orgCode" cssStyle="width: 150px;" hasAll="true" modules="070408" notAuthor="true"/>
                </td>

                <td style="width:8%" align="right" class="table-search-title">会议类型:</td>
                <td>
                    <select id="query_conferenceType" class="easyui-combotree" style="width: 205px; height: 24px;">
                    </select>
<%--
                    <crm:SelectConstant thisName="query_conferenceType" code="conferenceType" clazz="easyui-combobox" cssStyle="width: 120px;" hasAll="true" editable="false"/>
--%>
                </td>

                <td class="table-search-title" style="width:6%" align="right">会议名称:</td>
                <td align="left">
                    <input id="query_conferenceIds"
                           name="query_conferenceIds"
                           class="easyui-combobox"
                           style="height:22px; width:250px;"
                           data-options="loader: conferenceNameLoader, mode: 'remote', valueField: 'id', textField: 'name', panelWidth:'250' "/>
                </td>

            </tr>
            <tr>
                <td style="width:8%" align="right" class="table-search-title">会议日期：</td>
                <td style="width:330px;">
                    <input id="conferenceBegindt" name="conferenceBegindt" type="text" class="Wdate" onClick="WdatePicker({dateFmt:'yyyyMMdd'})" value='' style="width:150px; height: 18px;"/>-<input id="conferenceEndDt" name="conferenceEndDt" type="text" class="Wdate" onClick="WdatePicker({dateFmt:'yyyyMMdd'})" value='' style="width:150px; height: 18px;"/>
                </td>
                <td align="center" colspan="8">
                    <a type="button" id="btnSearch" class="easyui-linkbutton" onClick="toQuery()" >查询</a>
                    <a type="button" id="btnExport" class="easyui-linkbutton" onClick="toExport()" >导出</a>
                    <a type="button" id="btnClear" class="easyui-linkbutton" onClick="toClear()" >清空</a>
                </td>
            </tr>
            <tr>
                <td colspan="9">数据维度:
                <label for="option1">
                    <input type="checkbox" id="option1" name="options" value="1" checked="checked">
                    会议维度
                </label>

                <label for="option2">
                    <input type="checkbox" id="option2" name="options" value="2">
                    会议类型维度
                </label>
                *会议维度下，一条数据获将对应多个会议类型；会议类型维度下，一条数据对应一个会议类型
                </td>
            </tr>
        </table>
    </form>
</div>

<table id="dg"></table>

</body>
</html>