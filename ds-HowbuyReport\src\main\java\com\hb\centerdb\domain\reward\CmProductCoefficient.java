package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/9/15 10:46
 */
public class CmProductCoefficient implements Serializable {


    private static final long serialVersionUID = -695574053407612769L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 底表ID
     */
    private Long priId;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 所属部门
     */
    private String orgCode;
    /**
     * 起始日期
     */
    private String startDt;
    /**
     * 结束日期
     */
    private String endDt;
    /**
     * 核算产品类型
     */
    private String accountProductType;
    /**
     * 折标系数
     */
    private BigDecimal foldCoefficient;
    /**
     * 佣金率（%）
     */
    private BigDecimal commissionRate;
    /**
     * 年度配置奖
     */
    private BigDecimal annuallySetAward;
    /**
     * 二级存量折标
     */
    private BigDecimal secondStockCoeff;
    /**
     * 存量服务费A
     */
    private BigDecimal stockFeeA;
    /**
     * 存量服务费B
     */
    private BigDecimal stockFeeB;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 所属部门名
     */
    private String orgName;
    /**
     * 产品名称
     */
    private String fundName;
    /**
     * 核算产品类型名称
     */
    private String accountProductTypeName;

    /**
     * 修改人
     */
    private String modor;
    /**
     * 是否核算存续费1、0
     */
    private String sfhscxf;

    public String getSfhscxf() {
        return sfhscxf;
    }

    public void setSfhscxf(String sfhscxf) {
        this.sfhscxf = sfhscxf;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPriId() {
        return priId;
    }

    public void setPriId(Long priId) {
        this.priId = priId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public String getAccountProductType() {
        return accountProductType;
    }

    public void setAccountProductType(String accountProductType) {
        this.accountProductType = accountProductType;
    }

    public BigDecimal getFoldCoefficient() {
        return foldCoefficient;
    }

    public void setFoldCoefficient(BigDecimal foldCoefficient) {
        this.foldCoefficient = foldCoefficient;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getAnnuallySetAward() {
        return annuallySetAward;
    }

    public void setAnnuallySetAward(BigDecimal annuallySetAward) {
        this.annuallySetAward = annuallySetAward;
    }

    public BigDecimal getSecondStockCoeff() {
        return secondStockCoeff;
    }

    public void setSecondStockCoeff(BigDecimal secondStockCoeff) {
        this.secondStockCoeff = secondStockCoeff;
    }

    public BigDecimal getStockFeeA() {
        return stockFeeA;
    }

    public void setStockFeeA(BigDecimal stockFeeA) {
        this.stockFeeA = stockFeeA;
    }

    public BigDecimal getStockFeeB() {
        return stockFeeB;
    }

    public void setStockFeeB(BigDecimal stockFeeB) {
        this.stockFeeB = stockFeeB;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getAccountProductTypeName() {
        return accountProductTypeName;
    }

    public void setAccountProductTypeName(String accountProductTypeName) {
        this.accountProductTypeName = accountProductTypeName;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }
}
