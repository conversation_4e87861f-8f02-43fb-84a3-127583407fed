/**
 * 
 */
package com.hb.crm.constant.help;

/**
 * <AUTHOR>
 * 交易审核标志.
 */
public enum TXCheckFlag {
	/**
	 * 不需要.
	 */
	NoRequire("0"),
	/**
	 * 准备.
	 */
	Prepare("1"),
	/**
	 * 等待.
	 */
	Waiting("2"),
	/**
	 * 不通过.
	 */
	Refuse("3"),
	/**
	 * 通过.
	 */
	Pass("4"),
	/**
	 * 驳回.
	 */
	Reject("5"),
	/**
	 * 取消
	 */
	Canceled("9");
	private String value;

    public String getValue() {
        return value;
    }

    TXCheckFlag(String value) {
        this.value = value;
    }
}
