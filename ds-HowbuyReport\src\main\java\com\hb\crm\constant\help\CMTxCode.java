/**
 * 2010-09-17 chris.song	增加301048-登记免费产品
 */
package com.hb.crm.constant.help;

/**
 * <AUTHOR>
 * 客户的TxCode.
 */
public enum CMTxCode {
	/**
	 * 客户注册.
	 */
	custReg("301001"),
	/**
	 * 客户注册撤销.
	 */
	custRegCancel("301002"),
	/**
	 * 客户销户.
	 */
	custCancel("301003"),
	
	/**
	 * 注册客户升级交易客户.
	 */
	tradeReg("301010"),
	/**
     * 客户网上首次登录激活.
     */
    CustActivate("301025"),
    /**
     * 交易用户激活.
     */
    TCustActivate("301026"),
    /**
     * 修改密码和level.
     */
    ChangePwdLvlAndStatus("301027"),
    /**
     * 增加安全流水.
     */
    AddSafetest("301028"),
    /**
     * 修改登陆密码.
     */
    ChangeLoginPwd("301029"),
   
    
    /**
     * 客户资料修改.
     */
    custDatumModify("301030"),
	/**
	 * 注册客户信息修改.
	 */
	custRegInfoModify("301031"),
	
	custMobileModify("301032"),
    
    custBaseOtherModify("301033"),
    /**
     * 客户注册扩展信息修改.
     */
    custRegExtModify("301034"),
	
	
	custEmailModify("301035"),
	
	

	
	
	CustPerModify("301037"),
	
	CustFinModify("301038"),
	
	CustTxModify("301039"),
	
	ChangeCustRiskLevel("301040"),
	
	EmailSubscribe("301041"),
    
    SMGSubscribe("301042"),
	
    /**
     * 修改交易密码.
     */
    ChangeTPwd("301043"),
    /**
     * 重置登陆密码.
     */
    ResetLpwd("301044"),
    /**
     * 最后登陆时间.
     */
    ChangeLogonDt("301045"),
    
    ConsEmailSubscribe("301046"),
    
    ConsSMGSubscribe("301047"),
    /**
     * 登记免费产品
     */
    RegFreeProd("301048"),
    
    /**
     * 查询托管信息(被托管用户名)
     * */
     TrustshipByTrustshipName("301049"),
    /**
     * 查询托管信息(托管用户名)
     * */
     TrustshipByName("301050"),
    /**
     * 用户托管信息(保存和更新)
     * */
     TrustshipSaveOrUpdate("301051")
    
	;
	private String value;

    public String getValue() {
        return value;
    }

    CMTxCode(String value) {
        this.value = value;
    }
}
