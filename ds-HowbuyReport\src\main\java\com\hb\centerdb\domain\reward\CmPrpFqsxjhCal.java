package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 非券商小集合核算表
 * @reason:
 * @Date: 2020/9/22 09:58
 */
public class CmPrpFqsxjhCal implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;
	private String accountDt;
	private String tradeDt;
	private String quarter;
	private BigDecimal preid;
	private String conscode;
	private String consName;
	private String orgCode;
	private String orgName;
	/**
	 * 区域
	 */
	private String areaName;
	/**
	 * 中心
	 */
	private String centerName;

	private String fundCode;
	private String fundName;
	private String conscustNo;
	private String custName;
	private String accountProductType;
	private String accountProductTypeVal;
	private BigDecimal ackAmtRmb;
	private BigDecimal subscribeAmtRmb;
	private String subscribeAmtRmbVal;
	private BigDecimal ackFeeRmb;
	private BigDecimal discountFee;
	private BigDecimal adjustDiscountFee;
	private String discountType;
	private String discountTypeVal;
	private String discountReason;
	private BigDecimal tradeNum;
	private String sourceType;
	private String sourceTypeVal;
	private BigDecimal sourceCoeff;
	private BigDecimal oldSourceCoeff;
	private BigDecimal foldCoeff;
	private BigDecimal commissionRate;
	private BigDecimal oldCommissionRate;
	private BigDecimal foldPayamtRmb;
	private BigDecimal quarterTotalAmtRmb;
	private BigDecimal unqualifiedCoeff;
	private BigDecimal achieveCoeff;
	private BigDecimal extraCoeff;
	private BigDecimal totalAchieve;
	private String accountDtFlag;
	private String foldCoeffFlag;
	private String commissionRateFlag;
	private String sourceCoeffFlag;
	private String unqualifiedCoeffFlag;
	private String extraCoeffFlag;
	private String totalAchieveFlag;
	private String lastModor;
	
	private String creator;

	private Date createTime;

	private String modor;
	
	private Date updateTime;
	
	private String manycallFlag;
	
	private BigDecimal manycallSort;

	/** 管理系数 */
	private BigDecimal manageCoeff;
	/** 客户折标系数 */
	private BigDecimal zbCoeff;
	/**
	 * 管理系数折标销量
	 */
	private BigDecimal manageCoeffZbPayamt;
	/**
	 * 客户折标系数销量
	 */
	private BigDecimal zbCoeffPayamt;
	/**
	 * 管理系数佣金（不含倒追）
	 */
	private BigDecimal manageCoeffCommission;

	public BigDecimal getManageCoeff() {
		return manageCoeff;
	}

	public void setManageCoeff(BigDecimal manageCoeff) {
		this.manageCoeff = manageCoeff;
	}

	public BigDecimal getZbCoeff() {
		return zbCoeff;
	}

	public void setZbCoeff(BigDecimal zbCoeff) {
		this.zbCoeff = zbCoeff;
	}

	public BigDecimal getManageCoeffZbPayamt() {
		return manageCoeffZbPayamt;
	}

	public void setManageCoeffZbPayamt(BigDecimal manageCoeffZbPayamt) {
		this.manageCoeffZbPayamt = manageCoeffZbPayamt;
	}

	public BigDecimal getZbCoeffPayamt() {
		return zbCoeffPayamt;
	}

	public void setZbCoeffPayamt(BigDecimal zbCoeffPayamt) {
		this.zbCoeffPayamt = zbCoeffPayamt;
	}

	public BigDecimal getManageCoeffCommission() {
		return manageCoeffCommission;
	}

	public void setManageCoeffCommission(BigDecimal manageCoeffCommission) {
		this.manageCoeffCommission = manageCoeffCommission;
	}

	public String getTotalAchieveFlag() {
		return totalAchieveFlag;
	}

	public void setTotalAchieveFlag(String totalAchieveFlag) {
		this.totalAchieveFlag = totalAchieveFlag;
	}

	public BigDecimal getId() {
		return id;
	}

	public void setId(BigDecimal id) {
		this.id = id;
	}

	public String getAccountDt() {
		return accountDt;
	}

	public void setAccountDt(String accountDt) {
		this.accountDt = accountDt;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getQuarter() {
		return quarter;
	}

	public void setQuarter(String quarter) {
		this.quarter = quarter;
	}

	public BigDecimal getPreid() {
		return preid;
	}

	public void setPreid(BigDecimal preid) {
		this.preid = preid;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getAccountProductType() {
		return accountProductType;
	}

	public void setAccountProductType(String accountProductType) {
		this.accountProductType = accountProductType;
	}

	public String getAccountProductTypeVal() {
		return accountProductTypeVal;
	}

	public void setAccountProductTypeVal(String accountProductTypeVal) {
		this.accountProductTypeVal = accountProductTypeVal;
	}

	public BigDecimal getAckAmtRmb() {
		return ackAmtRmb;
	}

	public void setAckAmtRmb(BigDecimal ackAmtRmb) {
		this.ackAmtRmb = ackAmtRmb;
	}

	public BigDecimal getSubscribeAmtRmb() {
		return subscribeAmtRmb;
	}

	public void setSubscribeAmtRmb(BigDecimal subscribeAmtRmb) {
		this.subscribeAmtRmb = subscribeAmtRmb;
	}

	public BigDecimal getAckFeeRmb() {
		return ackFeeRmb;
	}

	public void setAckFeeRmb(BigDecimal ackFeeRmb) {
		this.ackFeeRmb = ackFeeRmb;
	}

	public BigDecimal getDiscountFee() {
		return discountFee;
	}

	public void setDiscountFee(BigDecimal discountFee) {
		this.discountFee = discountFee;
	}

	public BigDecimal getAdjustDiscountFee() {
		return adjustDiscountFee;
	}

	public void setAdjustDiscountFee(BigDecimal adjustDiscountFee) {
		this.adjustDiscountFee = adjustDiscountFee;
	}

	public String getDiscountType() {
		return discountType;
	}

	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}

	public String getDiscountTypeVal() {
		return discountTypeVal;
	}

	public void setDiscountTypeVal(String discountTypeVal) {
		this.discountTypeVal = discountTypeVal;
	}

	public String getDiscountReason() {
		return discountReason;
	}

	public void setDiscountReason(String discountReason) {
		this.discountReason = discountReason;
	}

	public BigDecimal getTradeNum() {
		return tradeNum;
	}

	public void setTradeNum(BigDecimal tradeNum) {
		this.tradeNum = tradeNum;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	public String getSourceTypeVal() {
		return sourceTypeVal;
	}

	public void setSourceTypeVal(String sourceTypeVal) {
		this.sourceTypeVal = sourceTypeVal;
	}

	public BigDecimal getSourceCoeff() {
		return sourceCoeff;
	}

	public void setSourceCoeff(BigDecimal sourceCoeff) {
		this.sourceCoeff = sourceCoeff;
	}

	public BigDecimal getFoldCoeff() {
		return foldCoeff;
	}

	public void setFoldCoeff(BigDecimal foldCoeff) {
		this.foldCoeff = foldCoeff;
	}

	public BigDecimal getCommissionRate() {
		return commissionRate;
	}

	public void setCommissionRate(BigDecimal commissionRate) {
		this.commissionRate = commissionRate;
	}

	public BigDecimal getFoldPayamtRmb() {
		return foldPayamtRmb;
	}

	public void setFoldPayamtRmb(BigDecimal foldPayamtRmb) {
		this.foldPayamtRmb = foldPayamtRmb;
	}

	public BigDecimal getQuarterTotalAmtRmb() {
		return quarterTotalAmtRmb;
	}

	public void setQuarterTotalAmtRmb(BigDecimal quarterTotalAmtRmb) {
		this.quarterTotalAmtRmb = quarterTotalAmtRmb;
	}

	public BigDecimal getUnqualifiedCoeff() {
		return unqualifiedCoeff;
	}

	public void setUnqualifiedCoeff(BigDecimal unqualifiedCoeff) {
		this.unqualifiedCoeff = unqualifiedCoeff;
	}

	public BigDecimal getAchieveCoeff() {
		return achieveCoeff;
	}

	public void setAchieveCoeff(BigDecimal achieveCoeff) {
		this.achieveCoeff = achieveCoeff;
	}

	public BigDecimal getExtraCoeff() {
		return extraCoeff;
	}

	public void setExtraCoeff(BigDecimal extraCoeff) {
		this.extraCoeff = extraCoeff;
	}

	public BigDecimal getTotalAchieve() {
		return totalAchieve;
	}

	public void setTotalAchieve(BigDecimal totalAchieve) {
		this.totalAchieve = totalAchieve;
	}

	public String getAccountDtFlag() {
		return accountDtFlag;
	}

	public void setAccountDtFlag(String accountDtFlag) {
		this.accountDtFlag = accountDtFlag;
	}

	public String getFoldCoeffFlag() {
		return foldCoeffFlag;
	}

	public void setFoldCoeffFlag(String foldCoeffFlag) {
		this.foldCoeffFlag = foldCoeffFlag;
	}

	public String getCommissionRateFlag() {
		return commissionRateFlag;
	}

	public void setCommissionRateFlag(String commissionRateFlag) {
		this.commissionRateFlag = commissionRateFlag;
	}

	public String getSourceCoeffFlag() {
		return sourceCoeffFlag;
	}

	public void setSourceCoeffFlag(String sourceCoeffFlag) {
		this.sourceCoeffFlag = sourceCoeffFlag;
	}

	public String getUnqualifiedCoeffFlag() {
		return unqualifiedCoeffFlag;
	}

	public void setUnqualifiedCoeffFlag(String unqualifiedCoeffFlag) {
		this.unqualifiedCoeffFlag = unqualifiedCoeffFlag;
	}

	public String getExtraCoeffFlag() {
		return extraCoeffFlag;
	}

	public void setExtraCoeffFlag(String extraCoeffFlag) {
		this.extraCoeffFlag = extraCoeffFlag;
	}

	public String getLastModor() {
		return lastModor;
	}

	public void setLastModor(String lastModor) {
		this.lastModor = lastModor;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getModor() {
		return modor;
	}

	public void setModor(String modor) {
		this.modor = modor;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public BigDecimal getOldSourceCoeff() {
		return oldSourceCoeff;
	}

	public void setOldSourceCoeff(BigDecimal oldSourceCoeff) {
		this.oldSourceCoeff = oldSourceCoeff;
	}

	public BigDecimal getOldCommissionRate() {
		return oldCommissionRate;
	}

	public void setOldCommissionRate(BigDecimal oldCommissionRate) {
		this.oldCommissionRate = oldCommissionRate;
	}

	public String getManycallFlag() {
		return manycallFlag;
	}

	public void setManycallFlag(String manycallFlag) {
		this.manycallFlag = manycallFlag;
	}

	public BigDecimal getManycallSort() {
		return manycallSort;
	}

	public void setManycallSort(BigDecimal manycallSort) {
		this.manycallSort = manycallSort;
	}

	public String getSubscribeAmtRmbVal() {
		return subscribeAmtRmbVal;
	}

	public void setSubscribeAmtRmbVal(String subscribeAmtRmbVal) {
		this.subscribeAmtRmbVal = subscribeAmtRmbVal;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getCenterName() {
		return centerName;
	}

	public void setCenterName(String centerName) {
		this.centerName = centerName;
	}
}
