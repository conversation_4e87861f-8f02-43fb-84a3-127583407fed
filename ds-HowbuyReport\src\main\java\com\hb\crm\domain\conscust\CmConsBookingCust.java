package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类CmConsBookingCust.java
 * @version 1.0
 */
public class CmConsBookingCust implements Serializable {

	private static final long serialVersionUID = 1L;

	private String consBookingId;

	private String consCustNo;

	private String bookingStatus;

	private String bookingCons;

	private String content;

	private String bookingDt;
	
	private String bookingStartTime;
	
	private String bookingEndTime;

	private String creator;

	private String creDt;

	private String modDt;
	
	private String custName;
	
	private String mobile;
	
	private String email;

	private String visitType;
	
	private String visitClassify;

	private String stimeStamp;
	
	private String consCode;
	
	private String consName;
	

	public String getConsBookingId() {
		return this.consBookingId;
	}

	public void setConsBookingId(String consBookingId) {
		this.consBookingId = consBookingId;
	}

	public String getConsCustNo() {
		return this.consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getBookingStatus() {
		return this.bookingStatus;
	}

	public void setBookingStatus(String bookingStatus) {
		this.bookingStatus = bookingStatus;
	}

	public String getBookingCons() {
		return this.bookingCons;
	}

	public void setBookingCons(String bookingCons) {
		this.bookingCons = bookingCons;
	}

	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getBookingDt() {
		return this.bookingDt;
	}

	public void setBookingDt(String bookingDt) {
		this.bookingDt = bookingDt;
	}

	public String getBookingStartTime() {
		return bookingStartTime;
	}

	public void setBookingStartTime(String bookingStartTime) {
		this.bookingStartTime = bookingStartTime;
	}

	public String getBookingEndTime() {
		return bookingEndTime;
	}

	public void setBookingEndTime(String bookingEndTime) {
		this.bookingEndTime = bookingEndTime;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}
	
	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getVisitType() {
		return visitType;
	}

	public void setVisitType(String visitType) {
		this.visitType = visitType;
	}

	public String getVisitClassify() {
		return visitClassify;
	}

	public void setVisitClassify(String visitClassify) {
		this.visitClassify = visitClassify;
	}

	public String getStimeStamp() {
		return this.stimeStamp;
	}

	public void setStimeStamp(String stimeStamp) {
		this.stimeStamp = stimeStamp;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
