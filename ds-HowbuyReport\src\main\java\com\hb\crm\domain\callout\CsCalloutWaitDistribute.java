package com.hb.crm.domain.callout;

import java.io.Serializable;

public class CsCalloutWaitDistribute implements Serializable{

	private static final long serialVersionUID = 122299934030L;
	
	private long waitId;     //等待ID
	private long taskId;  //来源ID
	private int taskType;  //来源类型
	private String waitDate;  //开始等待分配时间
	private int handleState; //处理标识符
	private int distributeFlag;  //分配标识符 
	private int disposeNum;  //再分配次数
	private String disposeDate;  //在分配处理时间 
	private int subTaskType;  //子来源类型
	private int orderFlag;  //预约标识符
	private String custName;  //客户姓名
	private String remark;  //备注
	private String orderUserId;  //预约座席ID
	private String mobile;
	private String email;
	private String sourceDt;
	private String consCustNo;
	private String deptFlag;
	private String custSmsFlag;
	private String bookingContent;
	private String taskTypeDetail;
	private String cmsNo;
	private String consultDt;
	private String consultNo;	// 咨询类型
	private int isAdd;	// 手动添加客户标识
	
	public long getWaitId() {
		return waitId;
	}
	public void setWaitId(long waitId) {
		this.waitId = waitId;
	}
	public long getTaskId() {
		return taskId;
	}
	public void setTaskId(long taskId) {
		this.taskId = taskId;
	}

	public String getWaitDate() {
		return waitDate;
	}
	public void setWaitDate(String waitDate) {
		this.waitDate = waitDate;
	}
	
	public int getDisposeNum() {
		return disposeNum;
	}
	public void setDisposeNum(int disposeNum) {
		this.disposeNum = disposeNum;
	}
	public String getDisposeDate() {
		return disposeDate;
	}
	public void setDisposeDate(String disposeDate) {
		this.disposeDate = disposeDate;
	}
	public int getTaskType() {
		return taskType;
	}
	public void setTaskType(int taskType) {
		this.taskType = taskType;
	}
	public int getHandleState() {
		return handleState;
	}
	public void setHandleState(int handleState) {
		this.handleState = handleState;
	}
	public int getDistributeFlag() {
		return distributeFlag;
	}
	public void setDistributeFlag(int distributeFlag) {
		this.distributeFlag = distributeFlag;
	}
	public int getSubTaskType() {
		return subTaskType;
	}
	public void setSubTaskType(int subTaskType) {
		this.subTaskType = subTaskType;
	}
	public int getOrderFlag() {
		return orderFlag;
	}
	public void setOrderFlag(int orderFlag) {
		this.orderFlag = orderFlag;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getOrderUserId() {
		return orderUserId;
	}
	public void setOrderUserId(String orderUserId) {
		this.orderUserId = orderUserId;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getSourceDt() {
		return sourceDt;
	}
	public void setSourceDt(String sourceDt) {
		this.sourceDt = sourceDt;
	}
	public String getConsCustNo() {
		return consCustNo;
	}
	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}
	public String getDeptFlag() {
		return deptFlag;
	}
	public void setDeptFlag(String deptFlag) {
		this.deptFlag = deptFlag;
	}
	public String getCustSmsFlag() {
		return custSmsFlag;
	}
	public void setCustSmsFlag(String custSmsFlag) {
		this.custSmsFlag = custSmsFlag;
	}
	public String getBookingContent() {
		return bookingContent;
	}
	public void setBookingContent(String bookingContent) {
		this.bookingContent = bookingContent;
	}
	public String getTaskTypeDetail() {
		return taskTypeDetail;
	}
	public void setTaskTypeDetail(String taskTypeDetail) {
		this.taskTypeDetail = taskTypeDetail;
	}
	public String getCmsNo() {
		return cmsNo;
	}
	public void setCmsNo(String cmsNo) {
		this.cmsNo = cmsNo;
	}
	public String getConsultDt() {
		return consultDt;
	}
	public void setConsultDt(String consultDt) {
		this.consultDt = consultDt;
	}
	public String getConsultNo() {
		return consultNo;
	}
	public void setConsultNo(String consultNo) {
		this.consultNo = consultNo;
	}
	public int getIsAdd() {
		return isAdd;
	}
	public void setIsAdd(int isAdd) {
		this.isAdd = isAdd;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
}
