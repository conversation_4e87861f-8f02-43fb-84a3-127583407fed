package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 实体类Custconstant.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class Custconstant implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custno;

	private String conscode;

	private String seniormgrcode;

	private String startdt;

	private String enddt;

	private String memo;

	private String recstat;

	private String checkflag;

	private String creator;

	private String modifier;

	private String checker;

	private String credt;

	private String moddt;

	private String conscustno;
	
	private Date binddate;

	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}

	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getSeniormgrcode() {
		return seniormgrcode;
	}

	public void setSeniormgrcode(String seniormgrcode) {
		this.seniormgrcode = seniormgrcode;
	}

	public String getStartdt() {
		return this.startdt;
	}

	public void setStartdt(String startdt) {
		this.startdt = startdt;
	}

	public String getEnddt() {
		return this.enddt;
	}

	public void setEnddt(String enddt) {
		this.enddt = enddt;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}

	public String getCheckflag() {
		return this.checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public Date getBinddate() {
		return binddate;
	}

	public void setBinddate(Date binddate) {
		this.binddate = binddate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
