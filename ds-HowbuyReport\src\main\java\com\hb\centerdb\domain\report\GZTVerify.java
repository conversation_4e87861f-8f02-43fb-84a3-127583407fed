package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类GZTVerify.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class GZTVerify implements Serializable {

private static final long serialVersionUID = 1L;

	private String regCode; //开户机构代码
	
	private String regName; //开户机构名称
	
	private Integer verifyNum; //开户验证数量
	
	private Integer verifyCount; //验证次数
	
	private Double verifyCost; //验证成本
	
    public String getRegCode() {
		return regCode;
	}

	public void setRegCode(String regCode) {
		this.regCode = regCode;
	}



	public String getRegName() {
		return regName;
	}



	public void setRegName(String regName) {
		this.regName = regName;
	}



	public Integer getVerifyNum() {
		return verifyNum;
	}



	public void setVerifyNum(Integer verifyNum) {
		this.verifyNum = verifyNum;
	}



	public Integer getVerifyCount() {
		return verifyCount;
	}



	public void setVerifyCount(Integer verifyCount) {
		this.verifyCount = verifyCount;
	}



	public Double getVerifyCost() {
		return verifyCost;
	}



	public void setVerifyCost(Double verifyCost) {
		this.verifyCost = verifyCost;
	}



public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
