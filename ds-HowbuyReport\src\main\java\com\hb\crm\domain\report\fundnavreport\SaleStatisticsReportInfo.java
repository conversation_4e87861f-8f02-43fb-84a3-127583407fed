package com.hb.crm.domain.report.fundnavreport;

import java.io.Serializable;
import java.math.BigDecimal;

public class SaleStatisticsReportInfo implements Serializable {
	private static final long serialVersionUID = -4441111412554894146L;
	private String beginDt;
	private String endDt;
	private String currency;
	private String departCode;
	private String departName;
	// 股票类5
	private BigDecimal gpcj = new BigDecimal(0);// 成交
	private BigDecimal gpyy = new BigDecimal(0);// 预约
	private BigDecimal gpsh = new BigDecimal(0);// 赎回
	private BigDecimal gphj = new BigDecimal(0);// 合计（成交+预约）
	// 固定收益产品3
	private BigDecimal gdcj = new BigDecimal(0);
	private BigDecimal gdyy = new BigDecimal(0);
	private BigDecimal gdsh = new BigDecimal(0);
	private BigDecimal gdhj = new BigDecimal(0);
	// 对冲策略产品4
	private BigDecimal dccj = new BigDecimal(0);
	private BigDecimal dcyy = new BigDecimal(0);
	private BigDecimal dcsh = new BigDecimal(0);
	private BigDecimal dchj = new BigDecimal(0);
	// 债券产品2
	private BigDecimal zqcj = new BigDecimal(0);
	private BigDecimal zqyy = new BigDecimal(0);
	private BigDecimal zqsh = new BigDecimal(0);
	private BigDecimal zqhj = new BigDecimal(0);
	// 其他8
	private BigDecimal qtcj = new BigDecimal(0);
	private BigDecimal qtyy = new BigDecimal(0);
	private BigDecimal qtsh = new BigDecimal(0);
	private BigDecimal qthj = new BigDecimal(0);
	// 现金管理工具1
	private BigDecimal xjcj = new BigDecimal(0);
	private BigDecimal xjyy = new BigDecimal(0);
	private BigDecimal xjsh = new BigDecimal(0);
	private BigDecimal xjhj = new BigDecimal(0);
	// 股权类6
	private BigDecimal gqcj = new BigDecimal(0);
	private BigDecimal gqyy = new BigDecimal(0);
	private BigDecimal gqsh = new BigDecimal(0);
	private BigDecimal gqhj = new BigDecimal(0);
	// 产品总汇
	private BigDecimal zhcj = new BigDecimal(0);
	private BigDecimal zhyy = new BigDecimal(0);
	private BigDecimal zhsh = new BigDecimal(0);
	private BigDecimal zhhj = new BigDecimal(0);
	public String getBeginDt() {
		return beginDt;
	}
	public void setBeginDt(String beginDt) {
		this.beginDt = beginDt;
	}
	public String getEndDt() {
		return endDt;
	}
	public void setEndDt(String endDt) {
		this.endDt = endDt;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getDepartCode() {
		return departCode;
	}
	public void setDepartCode(String departCode) {
		this.departCode = departCode;
	}
	public String getDepartName() {
		return departName;
	}
	public void setDepartName(String departName) {
		this.departName = departName;
	}
	public BigDecimal getGpcj() {
		return gpcj;
	}
	public void setGpcj(BigDecimal gpcj) {
		this.gpcj = gpcj;
	}
	public BigDecimal getGpyy() {
		return gpyy;
	}
	public void setGpyy(BigDecimal gpyy) {
		this.gpyy = gpyy;
	}
	public BigDecimal getGpsh() {
		return gpsh;
	}
	public void setGpsh(BigDecimal gpsh) {
		this.gpsh = gpsh;
	}
	public BigDecimal getGphj() {
		return gphj;
	}
	public void setGphj(BigDecimal gphj) {
		this.gphj = gphj;
	}
	public BigDecimal getGdcj() {
		return gdcj;
	}
	public void setGdcj(BigDecimal gdcj) {
		this.gdcj = gdcj;
	}
	public BigDecimal getGdyy() {
		return gdyy;
	}
	public void setGdyy(BigDecimal gdyy) {
		this.gdyy = gdyy;
	}
	public BigDecimal getGdsh() {
		return gdsh;
	}
	public void setGdsh(BigDecimal gdsh) {
		this.gdsh = gdsh;
	}
	public BigDecimal getGdhj() {
		return gdhj;
	}
	public void setGdhj(BigDecimal gdhj) {
		this.gdhj = gdhj;
	}
	public BigDecimal getDccj() {
		return dccj;
	}
	public void setDccj(BigDecimal dccj) {
		this.dccj = dccj;
	}
	public BigDecimal getDcyy() {
		return dcyy;
	}
	public void setDcyy(BigDecimal dcyy) {
		this.dcyy = dcyy;
	}
	public BigDecimal getDcsh() {
		return dcsh;
	}
	public void setDcsh(BigDecimal dcsh) {
		this.dcsh = dcsh;
	}
	public BigDecimal getDchj() {
		return dchj;
	}
	public void setDchj(BigDecimal dchj) {
		this.dchj = dchj;
	}
	public BigDecimal getZqcj() {
		return zqcj;
	}
	public void setZqcj(BigDecimal zqcj) {
		this.zqcj = zqcj;
	}
	public BigDecimal getZqyy() {
		return zqyy;
	}
	public void setZqyy(BigDecimal zqyy) {
		this.zqyy = zqyy;
	}
	public BigDecimal getZqsh() {
		return zqsh;
	}
	public void setZqsh(BigDecimal zqsh) {
		this.zqsh = zqsh;
	}
	public BigDecimal getZqhj() {
		return zqhj;
	}
	public void setZqhj(BigDecimal zqhj) {
		this.zqhj = zqhj;
	}
	public BigDecimal getQtcj() {
		return qtcj;
	}
	public void setQtcj(BigDecimal qtcj) {
		this.qtcj = qtcj;
	}
	public BigDecimal getQtyy() {
		return qtyy;
	}
	public void setQtyy(BigDecimal qtyy) {
		this.qtyy = qtyy;
	}
	public BigDecimal getQtsh() {
		return qtsh;
	}
	public void setQtsh(BigDecimal qtsh) {
		this.qtsh = qtsh;
	}
	public BigDecimal getQthj() {
		return qthj;
	}
	public void setQthj(BigDecimal qthj) {
		this.qthj = qthj;
	}
	public BigDecimal getXjcj() {
		return xjcj;
	}
	public void setXjcj(BigDecimal xjcj) {
		this.xjcj = xjcj;
	}
	public BigDecimal getXjyy() {
		return xjyy;
	}
	public void setXjyy(BigDecimal xjyy) {
		this.xjyy = xjyy;
	}
	public BigDecimal getXjsh() {
		return xjsh;
	}
	public void setXjsh(BigDecimal xjsh) {
		this.xjsh = xjsh;
	}
	public BigDecimal getXjhj() {
		return xjhj;
	}
	public void setXjhj(BigDecimal xjhj) {
		this.xjhj = xjhj;
	}
	public BigDecimal getGqcj() {
		return gqcj;
	}
	public void setGqcj(BigDecimal gqcj) {
		this.gqcj = gqcj;
	}
	public BigDecimal getGqyy() {
		return gqyy;
	}
	public void setGqyy(BigDecimal gqyy) {
		this.gqyy = gqyy;
	}
	public BigDecimal getGqsh() {
		return gqsh;
	}
	public void setGqsh(BigDecimal gqsh) {
		this.gqsh = gqsh;
	}
	public BigDecimal getGqhj() {
		return gqhj;
	}
	public void setGqhj(BigDecimal gqhj) {
		this.gqhj = gqhj;
	}
	public BigDecimal getZhcj() {
		return zhcj;
	}
	public void setZhcj(BigDecimal zhcj) {
		this.zhcj = zhcj;
	}
	public BigDecimal getZhyy() {
		return zhyy;
	}
	public void setZhyy(BigDecimal zhyy) {
		this.zhyy = zhyy;
	}
	public BigDecimal getZhsh() {
		return zhsh;
	}
	public void setZhsh(BigDecimal zhsh) {
		this.zhsh = zhsh;
	}
	public BigDecimal getZhhj() {
		return zhhj;
	}
	public void setZhhj(BigDecimal zhhj) {
		this.zhhj = zhhj;
	}
}
