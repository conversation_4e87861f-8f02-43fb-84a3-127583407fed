package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类PlatFormLogin.java
 * <AUTHOR>
 * @version 1.0
 * @created 20170726
 */
public class PlatFormLoginRate implements Serializable {

	private static final long serialVersionUID = 1L;

	private String consName; // 投顾姓名
	private String outletName;// 所属部门
	private Double custNum;// 客户数
	private Double loginAPPNum;// 登陆掌基人数
	private String loginAPPRate;// 掌基登陆率

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getOutletName() {
		return outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}
	
	public Double getCustNum() {
		return custNum;
	}

	public void setCustNum(Double custNum) {
		this.custNum = custNum;
	}

	public Double getLoginAPPNum() {
		return loginAPPNum;
	}

	public void setLoginAPPNum(Double loginAPPNum) {
		this.loginAPPNum = loginAPPNum;
	}

	public String getLoginAPPRate() {
		return loginAPPRate;
	}

	public void setLoginAPPRate(String loginAPPRate) {
		this.loginAPPRate = loginAPPRate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
