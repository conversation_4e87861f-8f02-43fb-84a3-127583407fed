package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类FundgroupDetail.java
 * @version 1.0
 */
public class FundgroupDetail implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;
	private String groupid;
	private String fundcode;
	private String fundname;
	private String creator;
	private String modifier;
	private String credt;
	private String moddt;
	
	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getGroupid() {
		return this.groupid;
	}

	public void setGroupid(String groupid) {
		this.groupid = groupid;
	}

	public String getFundcode() {
		return this.fundcode;
	}

	public void setFundcode(String fundcode) {
		this.fundcode = fundcode;
	}

	public String getFundname() {
		return fundname;
	}

	public void setFundname(String fundname) {
		this.fundname = fundname;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
