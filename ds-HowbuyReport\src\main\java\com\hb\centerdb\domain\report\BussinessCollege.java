package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class BussinessCollege implements Serializable {


    private static final long serialVersionUID = 5657553384310546538L;

    private String wechat; // 微信号
    private String mobile; // 手机号
    private String hbonNo; // 一账通号
    private String regDate; // 注册时间
    private String regCode; // 注册渠道
    private String custNo; // 客户号
    private String openDate; // 开户时间
    private String openCode; // 开户渠道
    private String custType; // 用户类型
    private String firstTradeDt; // 首交时间
    private String lsMarket; // 当前零售存量
    private String njbMarket; // 牛基宝当前存量
    private String gdMarket; // 高端存量
    private String firstGdDt; // 首次高端交易时间
    private String mobileMask;//手机掩码

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getHbonNo() {
        return hbonNo;
    }

    public void setHbonNo(String hbonNo) {
        this.hbonNo = hbonNo;
    }

    public String getRegDate() {
        return regDate;
    }

    public void setRegDate(String regDate) {
        this.regDate = regDate;
    }

    public String getRegCode() {
        return regCode;
    }

    public void setRegCode(String regCode) {
        this.regCode = regCode;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getOpenDate() {
        return openDate;
    }

    public void setOpenDate(String openDate) {
        this.openDate = openDate;
    }

    public String getOpenCode() {
        return openCode;
    }

    public void setOpenCode(String openCode) {
        this.openCode = openCode;
    }

    public String getCustType() {
        return custType;
    }

    public void setCustType(String custType) {
        this.custType = custType;
    }

    public String getFirstTradeDt() {
        return firstTradeDt;
    }

    public void setFirstTradeDt(String firstTradeDt) {
        this.firstTradeDt = firstTradeDt;
    }

    public String getLsMarket() {
        return lsMarket;
    }

    public void setLsMarket(String lsMarket) {
        this.lsMarket = lsMarket;
    }

    public String getNjbMarket() {
        return njbMarket;
    }

    public void setNjbMarket(String njbMarket) {
        this.njbMarket = njbMarket;
    }

    public String getGdMarket() {
        return gdMarket;
    }

    public void setGdMarket(String gdMarket) {
        this.gdMarket = gdMarket;
    }

    public String getFirstGdDt() {
        return firstGdDt;
    }

    public void setFirstGdDt(String firstGdDt) {
        this.firstGdDt = firstGdDt;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }
}
