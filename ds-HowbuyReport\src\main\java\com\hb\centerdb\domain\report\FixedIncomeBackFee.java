package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class FixedIncomeBackFee implements Serializable {


    private static final long serialVersionUID = 9010258065909572135L;

    private String hboneNo;//一账通号

    private String conscustNo;//投顾客户号

	private String custName;//客户姓名
	
	private String fundCode;//基金代码

    private String fundAttr;//产品名称
	
	private String fundType;//产品分类

    private String manager;//管理人

    private String consname;//投顾
	
	private String u1Name;//中心
	
	private String u2Name;//部门1
	
	private String u3Name;//部门2

    private String firstAckDt;//首次购买日期

    private Double ackVolSum =0d;//累计购买份额

    private Double ackAmtSum =0d;//累计购买金额

    private int holdDays;//持仓天数  或者计提天数 复用下

    private Double averageMarket =0d;//平均持仓市值

    private String feeType;//费用类型

    private String calcType;//计算类型

    private String clrq;//成立日期

    //新加的
    private String ageSubject;//协议主体
    private String finSdt;//结算起始日
    private String finEdt;//结算结束日

    private int yDays;//年化天数
    private int cDays;//产品天数
    private Double feeRate=0d;//年化费率

    private String buyDt;//交易日期
    private Double buyAmt=0d;//交易金额
    private Double buyVol=0d;//交易份额   或者持仓份额 复用下
    private Double buyNav=0d;//交易净值
    private Double subsidyFee=0d;//补贴金额
    private Double feeTax=0d;//结算金额
    private Double fee=0d;//结算金额不含税


    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getU3Name() {
        return u3Name;
    }

    public void setU3Name(String u3Name) {
        this.u3Name = u3Name;
    }

    public String getFirstAckDt() {
        return firstAckDt;
    }

    public void setFirstAckDt(String firstAckDt) {
        this.firstAckDt = firstAckDt;
    }

    public Double getAckVolSum() {
        return ackVolSum;
    }

    public void setAckVolSum(Double ackVolSum) {
        this.ackVolSum = ackVolSum;
    }


    public Double getAckAmtSum() {
        return ackAmtSum;
    }

    public void setAckAmtSum(Double ackAmtSum) {
        this.ackAmtSum = ackAmtSum;
    }

    public int getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(int holdDays) {
        this.holdDays = holdDays;
    }

    public Double getAverageMarket() {
        return averageMarket;
    }

    public void setAverageMarket(Double averageMarket) {
        this.averageMarket = averageMarket;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public Double getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(Double feeRate) {
        this.feeRate = feeRate;
    }

    public String getClrq() {
        return clrq;
    }

    public void setClrq(String clrq) {
        this.clrq = clrq;
    }

    public String getAgeSubject() {
        return ageSubject;
    }

    public void setAgeSubject(String ageSubject) {
        this.ageSubject = ageSubject;
    }

    public String getFinSdt() {
        return finSdt;
    }

    public void setFinSdt(String finSdt) {
        this.finSdt = finSdt;
    }

    public String getFinEdt() {
        return finEdt;
    }

    public void setFinEdt(String finEdt) {
        this.finEdt = finEdt;
    }

    public int getyDays() {
        return yDays;
    }

    public void setyDays(int yDays) {
        this.yDays = yDays;
    }

    public int getcDays() {
        return cDays;
    }

    public void setcDays(int cDays) {
        this.cDays = cDays;
    }

    public String getBuyDt() {
        return buyDt;
    }

    public void setBuyDt(String buyDt) {
        this.buyDt = buyDt;
    }

    public Double getBuyAmt() {
        return buyAmt;
    }

    public void setBuyAmt(Double buyAmt) {
        this.buyAmt = buyAmt;
    }

    public Double getBuyVol() {
        return buyVol;
    }

    public void setBuyVol(Double buyVol) {
        this.buyVol = buyVol;
    }

    public Double getBuyNav() {
        return buyNav;
    }

    public void setBuyNav(Double buyNav) {
        this.buyNav = buyNav;
    }

    public Double getSubsidyFee() {
        return subsidyFee;
    }

    public void setSubsidyFee(Double subsidyFee) {
        this.subsidyFee = subsidyFee;
    }

    public Double getFeeTax() {
        return feeTax;
    }

    public void setFeeTax(Double feeTax) {
        this.feeTax = feeTax;
    }
}
