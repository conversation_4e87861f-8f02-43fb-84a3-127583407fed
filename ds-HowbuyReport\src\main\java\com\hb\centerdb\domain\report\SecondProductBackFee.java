package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class SecondProductBackFee implements Serializable {


    private static final long serialVersionUID = 8552282210636927557L;

    /**
     * @Description: 一账通号
     * */
    private String hboneNo;
    /**
     * @Description: 投顾客户号
     */
    private String conscustNo;
    /**
     * @Description: 客户姓名
     */
	private String custName;
    /**
     * @Description: 基金代码
     */
	private String fundCode;
    /**
     * @Description: 产品名称
     */
    private String fundAttr;
    /**
     * @Description: 产品分类
     */
	private String fundType;
    /**
     * @Description: 管理人
     */
    private String manager;
    /**
     * @Description: 投顾
     */
    private String consname;
    /**
     * @Description: 中心
     */
	private String u1Name;
    /**
     * @Description: 部门1
     */
	private String u2Name;
    /**
     * @Description: 部门2
     */
	private String u3Name;
    /**
     * @Description: 赎回日期
     */
    private String ackDtRedem;
    /**
     * @Description: 赎回份额
     */
    private Double ackVolRedem =0d;
    /**
     * @Description: 赎回金额
     */
    private Double ackAmtRedem =0d;
    /**
     * @Description: 持仓市值
     */
    private Double holdAmt =0d;
    /**
     * @Description: 份额
     */
    private Double balanceVol =0d;
    /**
     * @Description: 费用类型
     */
    private String feeType;
    /**
     * @Description: 计算类型
     */
    private String calcType;
    /**
     * @Description: 费用
     */
	private Double fee=0d;
    /**
     * @Description: 计提基准净值
     */
    private Double accNav =0d;
    /**
     * @Description: 计提日
     */
    private String fixedDt;
    /**
     * @Description: 交易类型
     */
    private String tradeType;
    /**
     * @Description: 费率
     */
    private Double feeRate =0d;
    /**
     * @Description: 去税费用
     */
    private Double feeTax=0d;

    /**
     * @Description: 代理费
     */
    private Double agencyFee=0d;
    /**
     * @Description: 转换费
     */
    private Double transferFee=0d;
    /**
     * @Description: 手续费
     */
    private Double achievPay=0d;
    /**
     * @Description: 其他费用1
     */
    private Double otherFee1=0d;
    /**
     * @Description: 交易确认费用合计
     */
    private Double totalTransFee=0d;
    /**
     * @Description: 份额强制调整标志
     */
    private String adjustFlag;
    /**
     * @Description: 分红日/发放日
     */
    private String divDt;
    /**
     * @Description: 转入/转出标识
     */
    private String transDirect;
    /**
     * @Description: 过户原因
     */
    private String transferReason;

    /**
     * @Description: 申请日期
     */
    private String appDt;
    /**
     * @Description: 确认日期
     */
    private String ackDt;
    /**
     * @Description: 确认份额
     */
    private Double ackVol =0d;
    /**
     * @Description: 确认金额
     */
    private Double ackAmt =0d;
    /**
     * @Description: 净值
     */
    private Double nav =0d;
    /**
     * @Description: 中台业务名称
     */
    private String busiCode;
    /**
     * @Description: 中台详细业务名称
     */
    private String busiDtlCode;

    /**
     * @Description: 天数
     */
    private int countDays;
    /**
     * @Description: 结算开始日期
     */
    private String beginDate;
    /**
     * @Description: 结算结束日期
     */
    private String endDate;
    /**
     * @Description: 协议主体
     */
    private String ageSubject;
    /**
     * @Description: 备注
     */
    private String remark;

    /**
     * @Description: 币种
     */
    private String currency;

    /**
     * @Description: 预计交易日期
     */
    private String expecttradedt;
    /**
     * @Description: 好买产品线
     */
    private String hbtype;
    /**
     * @Description: 管理人
     */
    private String managerMan;
    /**
     * @Description: 税后折扣金额
     */
    private Double afterdiscountamt;
    /**
     * @Description: 税前折扣金额
     */
    private Double beforediscountamt;
    /**
     * @Description: 认购费率
     */
    private Double subRate;
    /**
     * @Description: 金蝶k3编码
     */
    private String kingCode;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getU3Name() {
        return u3Name;
    }

    public void setU3Name(String u3Name) {
        this.u3Name = u3Name;
    }

    public String getAckDtRedem() {
        return ackDtRedem;
    }

    public void setAckDtRedem(String ackDtRedem) {
        this.ackDtRedem = ackDtRedem;
    }

    public Double getAckVolRedem() {
        return ackVolRedem;
    }

    public void setAckVolRedem(Double ackVolRedem) {
        this.ackVolRedem = ackVolRedem;
    }

    public Double getAckAmtRedem() {
        return ackAmtRedem;
    }

    public void setAckAmtRedem(Double ackAmtRedem) {
        this.ackAmtRedem = ackAmtRedem;
    }

    public Double getHoldAmt() {
        return holdAmt;
    }

    public void setHoldAmt(Double holdAmt) {
        this.holdAmt = holdAmt;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public Double getAckVol() {
        return ackVol;
    }

    public void setAckVol(Double ackVol) {
        this.ackVol = ackVol;
    }

    public Double getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(Double ackAmt) {
        this.ackAmt = ackAmt;
    }

    public Double getNav() {
        return nav;
    }

    public void setNav(Double nav) {
        this.nav = nav;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getBusiDtlCode() {
        return busiDtlCode;
    }

    public void setBusiDtlCode(String busiDtlCode) {
        this.busiDtlCode = busiDtlCode;
    }

    public int getCountDays() {
        return countDays;
    }

    public void setCountDays(int countDays) {
        this.countDays = countDays;
    }

    public Double getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(Double balanceVol) {
        this.balanceVol = balanceVol;
    }

    public Double getAccNav() {
        return accNav;
    }

    public void setAccNav(Double accNav) {
        this.accNav = accNav;
    }

    public String getFixedDt() {
        return fixedDt;
    }

    public void setFixedDt(String fixedDt) {
        this.fixedDt = fixedDt;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public Double getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(Double feeRate) {
        this.feeRate = feeRate;
    }

    public Double getFeeTax() {
        return feeTax;
    }

    public void setFeeTax(Double feeTax) {
        this.feeTax = feeTax;
    }

    public String getAgeSubject() {
        return ageSubject;
    }

    public void setAgeSubject(String ageSubject) {
        this.ageSubject = ageSubject;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExpecttradedt() {
        return expecttradedt;
    }

    public void setExpecttradedt(String expecttradedt) {
        this.expecttradedt = expecttradedt;
    }

    public String getHbtype() {
        return hbtype;
    }

    public void setHbtype(String hbtype) {
        this.hbtype = hbtype;
    }

    public String getManagerMan() {
        return managerMan;
    }

    public void setManagerMan(String managerMan) {
        this.managerMan = managerMan;
    }

    public Double getAfterdiscountamt() {
        return afterdiscountamt;
    }

    public void setAfterdiscountamt(Double afterdiscountamt) {
        this.afterdiscountamt = afterdiscountamt;
    }

    public Double getBeforediscountamt() {
        return beforediscountamt;
    }

    public void setBeforediscountamt(Double beforediscountamt) {
        this.beforediscountamt = beforediscountamt;
    }

    public Double getSubRate() {
        return subRate;
    }

    public void setSubRate(Double subRate) {
        this.subRate = subRate;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getAgencyFee() {
        return agencyFee;
    }

    public void setAgencyFee(Double agencyFee) {
        this.agencyFee = agencyFee;
    }

    public Double getTransferFee() {
        return transferFee;
    }

    public void setTransferFee(Double transferFee) {
        this.transferFee = transferFee;
    }

    public Double getAchievPay() {
        return achievPay;
    }

    public void setAchievPay(Double achievPay) {
        this.achievPay = achievPay;
    }

    public Double getOtherFee1() {
        return otherFee1;
    }

    public void setOtherFee1(Double otherFee1) {
        this.otherFee1 = otherFee1;
    }

    public Double getTotalTransFee() {
        return totalTransFee;
    }

    public void setTotalTransFee(Double totalTransFee) {
        this.totalTransFee = totalTransFee;
    }

    public String getAdjustFlag() {
        return adjustFlag;
    }

    public void setAdjustFlag(String adjustFlag) {
        this.adjustFlag = adjustFlag;
    }

    public String getDivDt() {
        return divDt;
    }

    public void setDivDt(String divDt) {
        this.divDt = divDt;
    }

    public String getTransDirect() {
        return transDirect;
    }

    public void setTransDirect(String transDirect) {
        this.transDirect = transDirect;
    }

    public String getTransferReason() {
        return transferReason;
    }

    public void setTransferReason(String transferReason) {
        this.transferReason = transferReason;
    }

    public String getKingCode() {
        return kingCode;
    }

    public void setKingCode(String kingCode) {
        this.kingCode = kingCode;
    }
}
