package com.hb.crm.domain.agent;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类CsSign.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CsSign implements Serializable {

private static final long serialVersionUID = 1L;

	private Integer signid;
	
	private String conscode;
	
	private Date signindate;
	
	private Integer signoutflag;
	
	private Date signoutdate;
	
	private String remark;
	
	public Integer getSignid() {
		return this.signid;
	}

	public void setSignid(Integer signid) {
		this.signid = signid;
	}
	
	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	
	public Date getSignindate() {
		return this.signindate;
	}

	public void setSignindate(Date signindate) {
		this.signindate = signindate;
	}
	
	public Integer getSignoutflag() {
		return this.signoutflag;
	}

	public void setSignoutflag(Integer signoutflag) {
		this.signoutflag = signoutflag;
	}
	
	public Date getSignoutdate() {
		return this.signoutdate;
	}

	public void setSignoutdate(Date signoutdate) {
		this.signoutdate = signoutdate;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
