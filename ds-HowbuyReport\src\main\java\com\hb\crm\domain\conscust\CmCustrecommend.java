package com.hb.crm.domain.conscust;

import java.io.Serializable;

public class CmCustrecommend  implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5871079276434433611L;

	private String id;
	private String conscustno;
	private String pcode;
	private String buydt;
	private Double buyamt;
	private String creator;
	private String modifier;
	private String intruducetype;
	private String reducepratio;
	private String credt;
	private String moddt;
	private String deldt;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getConscustno() {
		return conscustno;
	}
	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	public String getPcode() {
		return pcode;
	}
	public void setPcode(String pcode) {
		this.pcode = pcode;
	}
	public String getBuydt() {
		return buydt;
	}
	public void setBuydt(String buydt) {
		this.buydt = buydt;
	}
	public Double getBuyamt() {
		return buyamt;
	}
	public void setBuyamt(Double buyamt) {
		this.buyamt = buyamt;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public String getCredt() {
		return credt;
	}
	public void setCredt(String credt) {
		this.credt = credt;
	}
	public String getModdt() {
		return moddt;
	}
	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	public String getDeldt() {
		return deldt;
	}
	public void setDeldt(String deldt) {
		this.deldt = deldt;
	}

	public String getIntruducetype() {
		return intruducetype;
	}

	public void setIntruducetype(String intruducetype) {
		this.intruducetype = intruducetype;
	}

	public String getReducepratio() {
		return reducepratio;
	}

	public void setReducepratio(String reducepratio) {
		this.reducepratio = reducepratio;
	}
}
