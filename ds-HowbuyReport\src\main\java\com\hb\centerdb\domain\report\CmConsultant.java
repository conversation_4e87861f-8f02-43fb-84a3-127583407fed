package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类CmConsultant.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmConsultant implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscode;
	
	private String consname;
	
	private String conslevel;
	
	private String teamcode;
	
	private String teamname;
	
	private String character;
	
	private String consstatus;
	
	private String recstat;
	
	private String checkflag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String credt;
	
	private String moddt;
	
	private String telno;
	
	private String mobile;
	
	private String deptcode;
	
	private String outletcode;
	
	private String outletname;

	private String pictureurl;
	
	private String email;
	
	private String resume;
	
	private String startdt;
	
	private String enddt;
	
	private String empcardno;
	
	private String loginflag;
	
	private String isseniormgr;
	
	private String workplace;
	
    private String roleId;
	
	private String roleName;
	
	private String managerrole;
	
	private String dialchannel;
	
	private String ip;
	
	private String isvirtual;
	
	private String isinside;
	
	private String picaddr;
	
	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	
	public String getConsname() {
		return this.consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}
	
	public String getConslevel() {
		return this.conslevel;
	}

	public void setConslevel(String conslevel) {
		this.conslevel = conslevel;
	}
	
	public String getTeamcode() {
		return this.teamcode;
	}

	public void setTeamcode(String teamcode) {
		this.teamcode = teamcode;
	}
	
	public String getCharacter() {
		return this.character;
	}

	public void setCharacter(String character) {
		this.character = character;
	}
	
	public String getConsstatus() {
		return this.consstatus;
	}

	public void setConsstatus(String consstatus) {
		this.consstatus = consstatus;
	}
	
	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}
	
	public String getCheckflag() {
		return this.checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	
	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}
	
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	public String getDeptcode() {
		return this.deptcode;
	}

	public void setDeptcode(String deptcode) {
		this.deptcode = deptcode;
	}
	
	public String getOutletcode() {
		return this.outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}
	
	public String getPictureurl() {
		return this.pictureurl;
	}

	public void setPictureurl(String pictureurl) {
		this.pictureurl = pictureurl;
	}
	
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getResume() {
		return this.resume;
	}

	public void setResume(String resume) {
		this.resume = resume;
	}
	
	public String getStartdt() {
		return this.startdt;
	}

	public void setStartdt(String startdt) {
		this.startdt = startdt;
	}
	
	public String getEnddt() {
		return this.enddt;
	}

	public void setEnddt(String enddt) {
		this.enddt = enddt;
	}
	
	public String getEmpcardno() {
		return this.empcardno;
	}

	public void setEmpcardno(String empcardno) {
		this.empcardno = empcardno;
	}
	
	public String getLoginflag() {
		return this.loginflag;
	}

	public void setLoginflag(String loginflag) {
		this.loginflag = loginflag;
	}
	
	public String getIsseniormgr() {
		return this.isseniormgr;
	}

	public void setIsseniormgr(String isseniormgr) {
		this.isseniormgr = isseniormgr;
	}
	
	public String getWorkplace() {
		return this.workplace;
	}

	public void setWorkplace(String workplace) {
		this.workplace = workplace;
	}
	
	public String getTeamname() {
		return teamname;
	}

	public void setTeamname(String teamname) {
		this.teamname = teamname;
	}

	public String getOutletname() {
		return outletname;
	}

	public void setOutletname(String outletname) {
		this.outletname = outletname;
	}
	
    public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
		
    public String getManagerrole() {
		return managerrole;
	}

	public void setManagerrole(String managerrole) {
		this.managerrole = managerrole;
	}	

	public String getDialchannel() {
		return dialchannel;
	}

	public void setDialchannel(String dialchannel) {
		this.dialchannel = dialchannel;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public String toString() {
		return "CmConsultant [conscode=" + conscode + ", consname=" + consname
				+ ", conslevel=" + conslevel + ", teamcode=" + teamcode
				+ ", teamname=" + teamname + ", character=" + character
				+ ", consstatus=" + consstatus + ", recstat=" + recstat
				+ ", checkflag=" + checkflag + ", creator=" + creator
				+ ", modifier=" + modifier + ", checker=" + checker + ", credt="
				+ credt + ", moddt=" + moddt + ", telno=" + telno + ", mobile="
				+ mobile + ", deptcode=" + deptcode + ", outletcode=" + outletcode
				+ ", outletname=" + outletname + ", pictureurl=" + pictureurl
				+ ", email=" + email + ", resume=" + resume + ", startdt="
				+ startdt + ", enddt=" + enddt + ", empcardno=" + empcardno
				+ ", loginflag=" + loginflag + ", isseniormgr=" + isseniormgr
				+ ", workplace=" + workplace + ", roleId=" + roleId + ", roleName="
				+ roleName + ", managerrole=" + managerrole + ", dialchannel=" + dialchannel + ", ip=" + ip + "]";
	}

	public String getIsvirtual() {
		return isvirtual;
	}

	public void setIsvirtual(String isvirtual) {
		this.isvirtual = isvirtual;
	}

	public String getIsinside() {
		return isinside;
	}

	public void setIsinside(String isinside) {
		this.isinside = isinside;
	}

	public String getPicaddr() {
		return picaddr;
	}

	public void setPicaddr(String picaddr) {
		this.picaddr = picaddr;
	}


}
