package com.hb.crm.web.controller.report;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hb.crm.tools.DateTimeUtil;
import com.hb.postgre.domain.op.ConferenceDtlNew;
import com.hb.postgre.domain.op.ConferenceNew;
import com.hb.postgre.service.op.ConferenceNewService;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hb.centerdb.domain.report.ConferenceDtl;
import com.hb.crm.constant.StaticVar;
import com.hb.crm.tools.NumberUtil;
import com.hb.crm.tools.PageData;
import com.hb.crm.tools.StringUtil;
import com.hb.crm.web.cache.ConsOrgCache;
import com.hb.crm.web.util.ParamUtil;
import com.hb.crm.web.util.Util;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Controller
@RequestMapping("/report")
public class ConferenceController {
	
	private static Logger logger = LoggerFactory.getLogger(ConferenceController.class);

	@Autowired
	private ConferenceNewService conferenceNewService;
		
	@RequestMapping("/listConference.do")
	public ModelAndView listConference() {
		return new ModelAndView("report/callIn/listConferenceReport");
	}

	@RequestMapping("/listConferenceNew.do")
	public ModelAndView listConferenceNew() {
		return new ModelAndView("report/callIn/listConferenceReportNew");
	}


	/**
	 *路演参会报表（新）
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listConferenceNew_json.do")
	public Map<String, Object> listConferenceNew_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

		String conferenceNames = request.getParameter("conferenceNames");
		if(conferenceNames != null ){
			conferenceNames = URLDecoder.decode(conferenceNames, "UTF-8");
		};

		String conferenceIds = request.getParameter("conferenceIds");
		String conferenceType = request.getParameter("conferenceType");
		String orgCode = request.getParameter("orgCode");
		String curPage = request.getParameter("page");
		String consCode = request.getParameter("consCode");
		String conferenceBegindt = request.getParameter("conferenceBegindt");
		String conferenceEndDt = request.getParameter("conferenceEndDt");
		String conferenceSelect = request.getParameter("conferenceSelect");

		String selectType = null;
		if(orgCode!=null && orgCode.equals("0")){
			selectType="0" ;
		} else{
			selectType="1" ;
		}

		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>();
		// 获取分页参数
		param = new ParamUtil(request).getParamObjMap();

		param.put("conferenceNames", conferenceNames);
		param.put("conferenceIds", conferenceIds);

		String conferenceTypeArray[]=new String[20];
		String []arr= {"1","2","3","4","6","9"};
		if(null!=conferenceType&&!"".equals(conferenceType.trim())) {
			conferenceTypeArray= conferenceType.split(",");
		}else{
			conferenceTypeArray=arr;
		}
		param.put("conferenceType", conferenceTypeArray);
		//param.put("conferenceType", getInStr(conferenceType,"t.conferencetype"));
		param.put("selectType", selectType);
		param.put("conferenceBegindt", conferenceBegindt);
		param.put("conferenceEndDt", conferenceEndDt);
		param.put("conferenceSelect", conferenceSelect);

		Map<String, Object> resultMap = new HashMap<String, Object>();

		if(conferenceIds == null){
			return resultMap;
		}

		if(StringUtil.isNotNullStr(consCode)){
			param.put("conscode", consCode);
		}else{
			//选择了未分配组
			if(orgCode.startsWith("other")){
				param.put("othertearm", orgCode.replaceFirst("other", ""));
			}else{
				String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
				//选择了团队
				if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
					param.put("teamcode", orgCode);
				}else{
					if(!"0".equals(orgCode)){
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}else{
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}
				}
			}
		}

		PageData<ConferenceNew> conferenceList = conferenceNewService.listConferenceNewByPage(param);
		//添加合计字段
		if(conferenceList.getListData().size()>0){
			ConferenceNew conferenceHJ = new ConferenceNew();
			conferenceHJ.setConferenceDt("小计:");
			for(ConferenceNew conference : conferenceList.getListData()){
				conferenceHJ.setConferenceMaxNub(Util.ObjectToInteger(conference.getConferenceMaxNub())+Util.ObjectToInteger(conferenceHJ.getConferenceMaxNub()));
				conferenceHJ.setConferenceOrgeNub(Util.ObjectToInteger(conference.getConferenceOrgeNub())+Util.ObjectToInteger(conferenceHJ.getConferenceOrgeNub()));
				conferenceHJ.setActualCont(Util.ObjectToInteger(conference.getActualCont())+Util.ObjectToInteger(conferenceHJ.getActualCont()));
				conferenceHJ.setCustTotalNum90(Util.ObjectToInteger(conference.getCustTotalNum90())+Util.ObjectToInteger(conferenceHJ.getCustTotalNum90()));
				conferenceHJ.setRealpayTotalAmtSum90(StringUtil.getRound(Util.ObjectToDouble(conference.getRealpayTotalAmtSum90())+Util.ObjectToDouble(conferenceHJ.getRealpayTotalAmtSum90()),2));
				conferenceHJ.setCustNum30(Util.ObjectToInteger(conference.getCustNum30())+Util.ObjectToInteger(conferenceHJ.getCustNum30()));
				conferenceHJ.setBuyAmtSum30(StringUtil.getRound(Util.ObjectToDouble(conference.getBuyAmtSum30())+Util.ObjectToDouble(conferenceHJ.getBuyAmtSum30()),2));
				conferenceHJ.setRealpayCustNum30(Util.ObjectToInteger(conference.getRealpayCustNum30())+Util.ObjectToInteger(conferenceHJ.getRealpayCustNum30()));
				conferenceHJ.setRealpayAmtSum30(StringUtil.getRound(Util.ObjectToDouble(conference.getRealpayAmtSum30())+Util.ObjectToDouble(conferenceHJ.getRealpayAmtSum30()),2));
				conferenceHJ.setRealpayCustNum60(Util.ObjectToInteger(conference.getRealpayCustNum60())+Util.ObjectToInteger(conferenceHJ.getRealpayCustNum60()));
				conferenceHJ.setRealpayAmtSum60(StringUtil.getRound(Util.ObjectToDouble(conference.getRealpayAmtSum60())+Util.ObjectToDouble(conferenceHJ.getRealpayAmtSum60()),2));
				conferenceHJ.setRealpayCustNum90(Util.ObjectToInteger(conference.getRealpayCustNum90())+Util.ObjectToInteger(conferenceHJ.getRealpayCustNum90()));
				conferenceHJ.setRealpayAmtSum90(StringUtil.getRound(Util.ObjectToDouble(conference.getRealpayAmtSum90())+Util.ObjectToDouble(conferenceHJ.getRealpayAmtSum90()),2));

				String hj30= "";
				String rf30= "";
				String hj60= "";
				String rf60= "";
				String hj90= "";
				String rf90= "";
				String hjtotal90= "";
				String rftotal90= "";
				if(conferenceHJ.getRealpayFund30() == null){
					hj30="";
				}else{
					hj30= conferenceHJ.getRealpayFund30();
				}
				if(conference.getRealpayFund30() == null){
					rf30 ="";
				}else{
					rf30 =conference.getRealpayFund30();
				}
				if(conferenceHJ.getRealpayFund60() == null){
					hj60="";
				}else{
					hj60= conferenceHJ.getRealpayFund60();
				}
				if(conference.getRealpayFund60() == null){
					rf60 ="";
				}else{
					rf60 =conference.getRealpayFund60();
				}
				if(conferenceHJ.getRealpayFund90() == null){
					hj90="";
				}else{
					hj90= conferenceHJ.getRealpayFund90();
				}
				if(conference.getRealpayFund90() == null){
					rf90 ="";
				}else{
					rf90 =conference.getRealpayFund90();
				}
				if(conferenceHJ.getRealpayTotalFund90() == null){
					hjtotal90="";
				}else{
					hjtotal90= conferenceHJ.getRealpayTotalFund90();
				}
				if(conference.getRealpayTotalFund90() == null){
					rftotal90 ="";
				}else{
					rftotal90 =conference.getRealpayTotalFund90();
				}
				conferenceHJ.setRealpayFund30(hj30+rf30);
				conferenceHJ.setRealpayFund60(hj60+rf60);
				conferenceHJ.setRealpayFund90(hj90+rf90);
				//conferenceHJ.setRealpayTotalFund90(hjtotal90+rftotal90);
			}
			conferenceList.getListData().add(conferenceHJ);
		}
		resultMap.put("total", conferenceList.getPageBean().getTotalNum());
		resultMap.put("page", curPage);
		resultMap.put("rows", conferenceList.getListData());
		return resultMap;
	}

	private String getListStringValue(List<String> list){
		if(list==null){
			return null;
		}
		String listStr = list.toString();
		String listStr1=listStr.replaceAll("\\[|\\]", "").replaceAll(" ", "");
		String[] str2=listStr1.split(",");
		String ret="";
		for(String i : str2){
			ret+="'"+i+"',";
		}
		return ret.substring(0, ret.length()-1);
	}



	/**
	 * 路演参会 新 页面数据导出方法
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportConferenceNew.do")
	public Map<String, Object> exportConferenceNew(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		String orgCode = request.getParameter("orgCode");
		String conferenceType = request.getParameter("conferenceType");
		String conferenceNames = request.getParameter("conferenceNames");
		String conferenceIds = request.getParameter("conferenceIds");

		String orgCodeName = request.getParameter("orgCodeName");
		String conferenceTypeName = request.getParameter("conferenceTypeName");
		String consCode = request.getParameter("consCode");
		String consName = request.getParameter("consName");
		String conferenceBegindt = request.getParameter("conferenceBegindt");
		String conferenceEndDt = request.getParameter("conferenceEndDt");
		String conferenceSelect = request.getParameter("conferenceSelect");

		String selectType = null;
		if(orgCode!=null && orgCode.equals("0")){
			selectType="0" ;
		} else{
			selectType="1" ;
		}
		if(orgCodeName != null ){
			orgCodeName = URLDecoder.decode(orgCodeName, "UTF-8");
		};
		if(conferenceTypeName != null ){
			conferenceTypeName = URLDecoder.decode(conferenceTypeName, "UTF-8");
		};
		if(conferenceNames != null ){
			conferenceNames = URLDecoder.decode(conferenceNames, "UTF-8");
		};
		if(consName != null ){
			consName = URLDecoder.decode(consName, "UTF-8");
		};
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>();

		//param.put("conferenceType", conferenceType);
		String conferenceTypeArray[]=new String[20];
		String []arr= {"1","2","3","4","6","9"};
		if(null!=conferenceType&&!"".equals(conferenceType.trim())) {
			conferenceTypeArray= conferenceType.split(",");
		}else{
			conferenceTypeArray=arr;
		}
		param.put("conferenceType", conferenceTypeArray);
		//param.put("conferenceType", getInStr(conferenceType,"t.conferencetype"));
		param.put("conferenceNames", conferenceNames);
		param.put("conferenceIds", conferenceIds);

		param.put("orgCodeName", orgCodeName);
		param.put("conferenceTypeName", conferenceTypeName);
		param.put("consName", consName);
		param.put("selectType", selectType);

		param.put("conferenceBegindt", conferenceBegindt);
		param.put("conferenceEndDt", conferenceEndDt);
		param.put("conferenceSelect", conferenceSelect);

		if(StringUtil.isNotNullStr(consCode)){
			param.put("conscode", consCode);
		}else{
			//选择了未分配组
			if(orgCode.startsWith("other")){
				param.put("othertearm", orgCode.replaceFirst("other", ""));
			}else{
				String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
				//选择了团队
				if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
					param.put("teamcode", orgCode);
				}else{
					if(!"0".equals(orgCode)){
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}else{
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}
				}
			}
		}

		try{
			List<ConferenceNew> ConferenceList = conferenceNewService.listConferenceNew(param);
			if(null!=ConferenceList&&ConferenceList.size()>0){
				boolean falg = exportConferenceDataNew(response, ConferenceList,param);
				if(falg){
					resultMap.put("msg", "success");
				}else{
					resultMap.put("msg", "error");
				}
			}else{
				resultMap.put("msg", "zero");
			}

		} catch (Exception e) {
			resultMap.put("msg", "error");
			logger.error("报表导出异常！", e);
		}
		return resultMap;
	}
	
	/**
	 * 路演跟踪报表明细查询
	 */
	@RequestMapping("/listConferenceDtl.do")
	public String  listConferenceDtl(HttpServletRequest request) throws UnsupportedEncodingException{
		
		String conferenceOrgCode = request.getParameter("conferenceOrgCode");
		String conferenceType = request.getParameter("conferenceType");
		String conferenceName  = request.getParameter("conferenceName");
		String queryorgCode  = request.getParameter("query_orgCode");
		String queryconsCode  = request.getParameter("query_consCode");
		String conferenceOrgName  = request.getParameter("conferenceOrgName");
		String conferenceIds = request.getParameter("query_conferenceIds");

        String conferenceBegindt = request.getParameter("conferenceBegindt");
        String conferenceEndDt = request.getParameter("conferenceEndDt");

        String conferenceDt = request.getParameter("conferenceDt");

		if(conferenceName != null ){
			conferenceName = URLDecoder.decode(conferenceName, "UTF-8");
		};	
		if(conferenceOrgName != null ){
			conferenceOrgName = URLDecoder.decode(conferenceOrgName, "UTF-8");
		}else{
			conferenceOrgName ="";
		};
		
		request.setAttribute("conferenceOrgCode", conferenceOrgCode);
		request.setAttribute("conferenceOrgName", conferenceOrgName);
		request.setAttribute("conferenceType", conferenceType);
        request.setAttribute("conferenceIds", conferenceIds);
		request.setAttribute("conferenceName", conferenceName);
		request.setAttribute("queryorgCode", queryorgCode);
		request.setAttribute("queryconsCode", queryconsCode);
		request.setAttribute("conferenceBegindt", conferenceBegindt);
        request.setAttribute("conferenceEndDt", conferenceEndDt);
        request.setAttribute("conferenceDt", conferenceDt);//会议日期
		
		
		return "report/callIn/listConferenceDtlReport";
		
	}

	/**
	 * 路演跟踪报表明细 （新）查询
	 */
	@RequestMapping("/listConferenceDtlNew.do")
	public String  listConferenceDtlNew(HttpServletRequest request) throws UnsupportedEncodingException{

		String conferenceOrgCode = request.getParameter("conferenceOrgCode");
		String conferenceType = request.getParameter("conferenceType");
		String conferenceName  = request.getParameter("conferenceName");
		String queryorgCode  = request.getParameter("query_orgCode");
		String queryconsCode  = request.getParameter("query_consCode");
		String conferenceOrgName  = request.getParameter("conferenceOrgName");
		String conferenceIds = request.getParameter("query_conferenceIds");

		String conferenceBegindt = request.getParameter("conferenceBegindt");
		String conferenceEndDt = request.getParameter("conferenceEndDt");

		String conferenceDt = request.getParameter("conferenceDt");
		String conferenceSelect = request.getParameter("conferenceSelect");

		String queryorgName=ConsOrgCache.getInstance().getAllOrgMap().get(queryorgCode);

		if(conferenceName != null ){
			conferenceName = URLDecoder.decode(conferenceName, "UTF-8");
		};
		if(conferenceOrgName != null ){
			conferenceOrgName = URLDecoder.decode(conferenceOrgName, "UTF-8");
		}else{
			conferenceOrgName ="";
		};

		request.setAttribute("conferenceOrgCode", conferenceOrgCode);
		request.setAttribute("conferenceOrgName", conferenceOrgName);
		request.setAttribute("conferenceType", conferenceType);
		request.setAttribute("conferenceIds", conferenceIds);
		request.setAttribute("conferenceName", conferenceName);
		request.setAttribute("queryorgCode", queryorgCode);
		request.setAttribute("queryconsCode", queryconsCode);
		request.setAttribute("conferenceBegindt", conferenceBegindt);
		request.setAttribute("conferenceEndDt", conferenceEndDt);
		request.setAttribute("conferenceDt", conferenceDt);//会议日期
		request.setAttribute("conferenceSelect", conferenceSelect);
		request.setAttribute("queryorgName", queryorgName);


		return "report/callIn/listConferenceDtlReportNew";

	}


	/**
	 * 路演参会明细  新 页面数据导出方法
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportConferenceDtlNew.do")
	public Map<String, Object> exportConferenceDtlNew(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		String conferenceName = request.getParameter("conferenceName");
		String orgCode = request.getParameter("orgCode");
		String consCode = request.getParameter("consCode");

		String conferenceOrgCode = request.getParameter("conferenceOrgCode");
		String conferenceOrgName = request.getParameter("conferenceOrgName");
		String conferenceIds = request.getParameter("conferenceIds");
		String conferenceType = request.getParameter("conferenceType");

		String conferenceBegindt = request.getParameter("conferenceBegindt");
		String conferenceEndDt = request.getParameter("conferenceEndDt");

		String conferenceDt = request.getParameter("conferenceDt");
		String conferenceSelect = request.getParameter("conferenceSelect");

		if(conferenceName != null ){
			conferenceName = URLDecoder.decode(conferenceName, "UTF-8");
		};

		if(conferenceOrgName != null ){
			conferenceOrgName = URLDecoder.decode(conferenceOrgName, "UTF-8");
		};

		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>();

		if(StringUtil.isNotNullStr(consCode)){
			param.put("conscode", consCode);
		}else{
			//选择了未分配组
			if(orgCode.startsWith("other")){
				param.put("othertearm", orgCode.replaceFirst("other", ""));
			}else{
				String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
				//选择了团队
				if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
					param.put("teamcode", orgCode);
				}else{
					if(!"0".equals(orgCode)){
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}
				}
			}
		}

		String conferenceTypeArray[]=new String[20];
		String []arr= {"1","2","3","4","6","9"};
		if(null!=conferenceType&&!"".equals(conferenceType.trim())) {
			conferenceTypeArray= conferenceType.split(",");
		}else{
			conferenceTypeArray=arr;
		}
		param.put("conferenceType", conferenceTypeArray);

		//param.put("conferenceType", getInStr(conferenceType,"t.conferencetype"));
		param.put("conferenceIds", conferenceIds);
		param.put("conferenceName", conferenceName);
		param.put("conferenceOrgCode", conferenceOrgCode);
		param.put("conferenceOrgName", conferenceOrgName);
		param.put("conferenceBegindt", conferenceBegindt);
		param.put("conferenceEndDt", conferenceEndDt);
		param.put("conferenceDt", conferenceDt);
		param.put("conferenceSelect", conferenceSelect);
		param.put("orgCode", orgCode);

		try{
			List<ConferenceDtlNew> conferenceDtlList = conferenceNewService.listConferenceDtlNew(param);

			if(null!=conferenceDtlList&&conferenceDtlList.size()>0){
				for(ConferenceDtlNew conferenceDtl:conferenceDtlList){
					String upOrgCode=ConsOrgCache.getInstance().getUpOrgMapCache().get(conferenceDtl.getConferenceOrgCode());
					if("0".equals(upOrgCode)){
						conferenceDtl.setConferenceUpperOrgName(ConsOrgCache.getInstance().getOrgMap().get(conferenceDtl.getConferenceOrgCode()));
					}else{
						conferenceDtl.setConferenceUpperOrgName(ConsOrgCache.getInstance().getAllOrgMap().get(upOrgCode));
					}

				}

				boolean falg = exportConferenceDtlDataNew(response, conferenceDtlList,param);
				if(falg){
					resultMap.put("msg", "success");
				}else{
					resultMap.put("msg", "error");
				}
			}else{
				resultMap.put("msg", "zero");
			}

		} catch (Exception e) {
			resultMap.put("msg", "error");
			logger.error("报表导出异常！", e);
		}
		return resultMap;
	}


	/**
	 *路演参会明细 (新)
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listConferenceDtlNew_json.do")
	public Map<String, Object> listConferenceDtlNew_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

		String conferenceType = request.getParameter("conferenceType");
		String conferenceIds = request.getParameter("conferenceIds");

		String conferenceOrgCode = request.getParameter("conferenceOrgCode");
		String orgCode = request.getParameter("queryorgCode");
		String consCode = request.getParameter("queryconsCode");

		String conferenceName = request.getParameter("conferenceName");

		String conferenceBegindt = request.getParameter("conferenceBegindt");
		String conferenceEndDt = request.getParameter("conferenceEndDt");

		String conferenceDt = request.getParameter("conferenceDt");

		String conferenceSelect = request.getParameter("conferenceSelect");
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>();
		// 获取分页参数
		param = new ParamUtil(request).getParamObjMap();

		Map<String, Object> resultMap = new HashMap<String, Object>();

		String curPage = request.getParameter("page");

		if(StringUtil.isNotNullStr(consCode)){
			param.put("conscode", consCode);
		}else{
			//选择了未分配组
			if(orgCode.startsWith("other")){
				param.put("othertearm", orgCode.replaceFirst("other", ""));
			}else{
				String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
				//选择了团队
				if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
					param.put("teamcode", orgCode);
				}else{
					if(!"0".equals(orgCode)){
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}
				}
			}
		}

		param.put("conferenceOrgCode", conferenceOrgCode);
		String conferenceTypeArray[]=new String[20];
		String []arr= {"1","2","3","4","6","9"};
		if(null!=conferenceType&&!"".equals(conferenceType.trim())) {
			conferenceTypeArray= conferenceType.split(",");
		}else{
			conferenceTypeArray=arr;
		}
		param.put("conferenceType", conferenceTypeArray);
		//param.put("conferenceType", getInStr(conferenceType,"t.conferencetype"));
		param.put("conferenceIds", conferenceIds);
		param.put("conferenceName", conferenceName);

		param.put("conferenceBegindt", conferenceBegindt);
		param.put("conferenceEndDt", conferenceEndDt);

		param.put("conferenceDt", conferenceDt);
		param.put("conferenceSelect", conferenceSelect);

		//
		PageData<ConferenceDtlNew> ConferenceDtlList = conferenceNewService.listConferenceDtlNewByPage(param);
		if(ConferenceDtlList.getListData().size()>0){
			for(ConferenceDtlNew conferenceDtl:ConferenceDtlList.getListData()){
				String upOrgCode=ConsOrgCache.getInstance().getUpOrgMapCache().get(conferenceDtl.getConferenceOrgCode());
				if("0".equals(upOrgCode)){
					conferenceDtl.setConferenceUpperOrgName(ConsOrgCache.getInstance().getOrgMap().get(conferenceDtl.getConferenceOrgCode()));
				}else{
					conferenceDtl.setConferenceUpperOrgName(ConsOrgCache.getInstance().getAllOrgMap().get(upOrgCode));
				}

			}
		}
		resultMap.put("total", ConferenceDtlList.getPageBean().getTotalNum());
		resultMap.put("page", curPage);
		resultMap.put("rows", ConferenceDtlList.getListData());
		return resultMap;
	}
	
	


	/**
	 * 路演参会报表 新 数据导出
	 * @param response
	 * @throws Exception
	 */
	public boolean exportConferenceDataNew(HttpServletResponse response, List<ConferenceNew> ConferenceList,Map<String,Object> ConferenceMap) throws Exception {
		NumberFormat format = NumberFormat.getNumberInstance();
		format.setMinimumFractionDigits(2);
		format.setMaximumFractionDigits(2);
		String dtTitle=DateTimeUtil.convertDateToString("yyyyMMdd", new Date());
		// 设置导出参数和标题
		String excelName = "".equals("ConferenceReportNew.xls") ? URLEncoder.encode("路演参会情况跟踪(新)"+dtTitle, "utf-8") : URLEncoder.encode("路演参会情况跟踪(新)"+dtTitle+".xls", "utf-8");
		// 清空输出流
		response.reset();
		response.setHeader("Content-Disposition", "attachment;filename="+ new String((excelName).getBytes(), "iso8859-1"));
		ServletOutputStream os = response.getOutputStream();
		// 默认每个sheet只存储60000条数据，如果记录条数超过60000，则分成多个sheet进行导出
		int x = 0; // sheet也数据范围标识
		int curSheet = 1; // 当前页数
		int sheetSize = 60000; // 每页sheet显示记录数
		int totalSheet = 0;	// 总sheet数
		if(ConferenceList!=null && ConferenceList.size()>0){
			totalSheet = (ConferenceList.size()+sheetSize-1)/sheetSize;
		}
		// 建立excel文件
		WritableWorkbook wbook = Workbook.createWorkbook(os);
		try {
			int sheetNum = 1;//当前sheet索引
			for (int j = 0; j < totalSheet; j++) {
				// sheet标题
				String sheetName = "第"+curSheet+"页数据";

				// sheet名称
				WritableSheet wsheet = wbook.createSheet(sheetName, sheetNum);
				sheetNum++;
				WritableFont fontCss = new WritableFont(WritableFont.ARIAL, 12,WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,Colour.BLACK);
				WritableCellFormat formatTitle = new WritableCellFormat(fontCss);
				//wsheet.mergeCells(0, 0, 11, 0);
				formatTitle.setBackground(Colour.WHITE);
				formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
				formatTitle.setAlignment(Alignment.CENTRE);
				fontCss = new jxl.write.WritableFont(WritableFont.ARIAL, 9, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,Colour.BLACK);
				formatTitle = new WritableCellFormat(fontCss);
				formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
				wsheet.setColumnView(0, 12);
				wsheet.setColumnView(1, 40);
				wsheet.setColumnView(2, 30);
				wsheet.setColumnView(3, 10);
				wsheet.setColumnView(4, 20);
				wsheet.setColumnView(5, 12);
				wsheet.setColumnView(6, 20);
				wsheet.setColumnView(7, 20);
				wsheet.setColumnView(8, 20);
				wsheet.setColumnView(9, 20);
				wsheet.setColumnView(10, 20);
				wsheet.setColumnView(11, 12);
				wsheet.setColumnView(12, 20);
				wsheet.setColumnView(13, 20);
				wsheet.setColumnView(14, 20);
				wsheet.setColumnView(15, 20);
				wsheet.setColumnView(16, 20);
				wsheet.setColumnView(17, 20);
				wsheet.setColumnView(18, 20);
				wsheet.setColumnView(19, 20);
				wsheet.setColumnView(20, 20);
				wsheet.setColumnView(21, 20);
				wsheet.setColumnView(22, 20);

				wsheet.addCell(new Label(0, 0, "举办部门："));
				wsheet.addCell(new Label(1, 0, StringUtil.null2String(ConferenceMap.get("orgCodeName"))));
				wsheet.addCell(new Label(3, 0, "会议类型："));
				wsheet.addCell(new Label(4, 0, StringUtil.null2String(ConferenceMap.get("conferenceTypeName"))));
				wsheet.addCell(new Label(5, 0, "会议名称："));
				wsheet.addCell(new Label(6, 0, StringUtil.null2String(ConferenceMap.get("conferenceNames"))));

				// 设置标题：列（从0开始），行（从1开始），显示内容
				wsheet.addCell(new Label(0, 1, "会议ID", formatTitle));
				wsheet.addCell(new Label(1, 1, "会议名称", formatTitle));
				wsheet.addCell(new Label(2, 1, "会议类型", formatTitle));
				wsheet.addCell(new Label(3, 1, "举办部门", formatTitle));
				wsheet.addCell(new Label(4, 1, "会议日期", formatTitle));
				wsheet.addCell(new Label(5, 1, "会议地址", formatTitle));
				wsheet.addCell(new Label(6, 1, "是否支持线上报名", formatTitle));
				wsheet.addCell(new Label(7, 1, "人数上限", formatTitle));
				wsheet.addCell(new Label(8, 1, "报名人数", formatTitle));
				wsheet.addCell(new Label(9, 1, "实际参会人数", formatTitle));
				wsheet.addCell(new Label(10, 1, "近90天累计打款人数", formatTitle));
				wsheet.addCell(new Label(11, 1, "近90天累计打款金额 ", formatTitle));
				//wsheet.addCell(new Label(12, 1, "近90天累计打款产品 ", formatTitle));
				wsheet.addCell(new Label(12, 1, "30天预约人数", formatTitle));
				wsheet.addCell(new Label(13, 1, "30天预约金额", formatTitle));
				wsheet.addCell(new Label(14, 1, "30天打款人数", formatTitle));
				wsheet.addCell(new Label(15, 1, "30天打款金额", formatTitle));
				wsheet.addCell(new Label(16, 1, "30天打款产品", formatTitle));
				wsheet.addCell(new Label(17, 1, "60天打款人数", formatTitle));
				wsheet.addCell(new Label(18, 1, "60天打款金额", formatTitle));
				wsheet.addCell(new Label(19, 1, "60天打款产品", formatTitle));
				wsheet.addCell(new Label(20, 1, "90天打款人数", formatTitle));
				wsheet.addCell(new Label(21, 1, "90天打款金额", formatTitle));
				wsheet.addCell(new Label(22, 1, "90天打款产品", formatTitle));

				// 控制行数
				int rows = 2;
				// 对合并后的单元格进行样式设置，包括垂直居中显示
				WritableFont contentFont = new WritableFont(WritableFont.ARIAL,9, WritableFont.NO_BOLD, false,UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
				WritableCellFormat formatContent = new WritableCellFormat(contentFont);
				formatContent.setBackground(Colour.WHITE);
				formatContent.setBorder(Border.ALL, BorderLineStyle.THIN);
				formatContent.setVerticalAlignment(VerticalAlignment.CENTRE);

				int start = curSheet * sheetSize - sheetSize; // 设置导出sheet的起点行索引位置
				int end = curSheet * sheetSize; // 设置导出sheet的结束行索引位置
				if(end > ConferenceList.size()){
					end = ConferenceList.size();
				}
				for (int i = 0; i < ConferenceList.size(); i++) {
					if (x >= start && x < end) {
						// 循环写入内容，同时进行合并单元格操作
						ConferenceNew conference = ConferenceList.get(x);
						wsheet.addCell(new Label(0, rows, (conference.getConferenceId() == null ? "" : conference.getConferenceId()), formatContent));
						wsheet.addCell(new Label(1, rows, (conference.getConferenceName() == null ? "" : conference.getConferenceName()), formatContent));
						wsheet.addCell(new Label(2, rows, (conference.getConferenceTypeNa() == null ? "" : conference.getConferenceTypeNa()), formatContent));
						wsheet.addCell(new Label(3, rows, (conference.getConferenceOrgNa() == null ? "" : conference.getConferenceOrgNa()), formatContent));
						wsheet.addCell(new Label(4, rows, (conference.getConferenceDt() == null ? "" : conference.getConferenceDt()), formatContent));
						wsheet.addCell(new Label(5, rows, (conference.getConferenceAddr() == null ? "" : conference.getConferenceAddr()), formatContent));
						wsheet.addCell(new Label(6, rows, (conference.getIsOnline() == null ? "" : conference.getIsOnline()), formatContent));
						wsheet.addCell(new Label(7, rows, (String.valueOf(conference.getConferenceMaxNub()).toString() == null ? "" : String.valueOf(conference.getConferenceMaxNub()).toString()), formatContent));
						wsheet.addCell(new Label(8, rows, (String.valueOf(conference.getConferenceOrgeNub()).toString() == null ? "" : String.valueOf(conference.getConferenceOrgeNub()).toString()), formatContent));
						wsheet.addCell(new Label(9, rows, (String.valueOf(conference.getActualCont()).toString() == null ? "" : String.valueOf(conference.getActualCont()).toString()), formatContent));
						wsheet.addCell(new Label(10, rows, (String.valueOf(conference.getCustTotalNum90()).toString() == null ? "" : String.valueOf(conference.getCustTotalNum90()).toString()), formatContent));
						wsheet.addCell(new Label(11, rows, (conference.getRealpayTotalAmtSum90() == null ? "" : format.format(NumberUtil.replaceNullToZero(conference.getRealpayTotalAmtSum90()))), formatContent));
						String rf30="";
						String rf60="";
						String rf90="";
						String rftotal90="";
						if(conference.getRealpayFund30() == null){
							rf30="";
						}else{
							rf30= conference.getRealpayFund30().replace("</br>", "\n\r");
						}
						if(conference.getRealpayFund60() == null){
							rf60="";
						}else{
							rf60= conference.getRealpayFund60().replace("</br>", "\n\r");
						}
						if(conference.getRealpayFund90() == null){
							rf90="";
						}else{
							rf90= conference.getRealpayFund90().replace("</br>", "\n\r");
						}
						if(conference.getRealpayTotalFund90() == null){
							rftotal90="";
						}else{
							rftotal90= conference.getRealpayTotalFund90().replace("</br>", "\n\r");
						}
						//wsheet.addCell(new Label(12, rows, (rftotal90 == null ? "" : rftotal90), formatContent));

						wsheet.addCell(new Label(12, rows, (String.valueOf(conference.getCustNum30()).toString() == null ? "" : String.valueOf(conference.getCustNum30()).toString()), formatContent));
						wsheet.addCell(new Label(13, rows, (conference.getBuyAmtSum30() == null ? "" : format.format(NumberUtil.replaceNullToZero(conference.getBuyAmtSum30()))), formatContent));

						wsheet.addCell(new Label(14, rows, (String.valueOf(conference.getRealpayCustNum30()).toString() == null ? "" : String.valueOf(conference.getRealpayCustNum30()).toString()), formatContent));
						wsheet.addCell(new Label(15, rows, (conference.getRealpayAmtSum30() == null ? "" : format.format(NumberUtil.replaceNullToZero(conference.getRealpayAmtSum30()))), formatContent));
						wsheet.addCell(new Label(16, rows, (rf30 == null ? "" : rf30), formatContent));

						wsheet.addCell(new Label(17, rows, (String.valueOf(conference.getRealpayCustNum60()).toString() == null ? "" : String.valueOf(conference.getRealpayCustNum60()).toString()), formatContent));
						wsheet.addCell(new Label(18, rows, (conference.getRealpayAmtSum60() == null ? "" : format.format(NumberUtil.replaceNullToZero(conference.getRealpayAmtSum60()))), formatContent));
						wsheet.addCell(new Label(19, rows, (rf60 == null ? "" : rf60), formatContent));

						wsheet.addCell(new Label(20, rows, (String.valueOf(conference.getRealpayCustNum90()).toString() == null ? "" : String.valueOf(conference.getRealpayCustNum90()).toString()), formatContent));
						wsheet.addCell(new Label(21, rows, (conference.getRealpayAmtSum90() == null ? "" : format.format(NumberUtil.replaceNullToZero(conference.getRealpayAmtSum90()))), formatContent));
						wsheet.addCell(new Label(22, rows, (rf90 == null ? "" : rf90), formatContent));
						rows++;
					} else {
						break;
					}
					x++;
				}
				curSheet++;
			}
			// 主体内容生成结束
			wbook.write(); // 写入文件
			logger.info("报表导出成功！");
			return true;
		} catch (Exception ex) {
			logger.error("报表导出异常！", ex);
			return false;
		}finally{
			if(wbook!=null){
				wbook.close();
			}
			os.close(); // 关闭流
		}
	}


	/**
	 * 路演参会报表明细 新  数据导出
	 * @param response
	 * @throws Exception
	 */
	public boolean exportConferenceDtlDataNew(HttpServletResponse response, List<ConferenceDtlNew> ConferenceDtlList, Map<String,Object> ConferenceDtlMap) throws Exception {
		NumberFormat format = NumberFormat.getNumberInstance();
		format.setMinimumFractionDigits(2);
		format.setMaximumFractionDigits(2);
		String dtTitle=DateTimeUtil.convertDateToString("yyyyMMdd", new Date());
		// 设置导出参数和标题
		String excelName = "".equals("ConferenceDtlReportNew.xls") ? URLEncoder.encode("路演参会情况跟踪明细报表(新)"+dtTitle, "utf-8") : URLEncoder.encode("路演参会情况跟踪明细报表(新)"+dtTitle+".xls", "utf-8");
		// 清空输出流
		response.reset();
		response.setHeader("Content-Disposition", "attachment;filename="+ new String((excelName).getBytes(), "iso8859-1"));
		ServletOutputStream os = response.getOutputStream();
		// 默认每个sheet只存储60000条数据，如果记录条数超过60000，则分成多个sheet进行导出
		int x = 0; // sheet也数据范围标识
		int curSheet = 1; // 当前页数
		int sheetSize = 60000; // 每页sheet显示记录数
		int totalSheet = 0;	// 总sheet数
		if(ConferenceDtlList!=null && ConferenceDtlList.size()>0){
			totalSheet = (ConferenceDtlList.size()+sheetSize-1)/sheetSize;
		}
		// 建立excel文件
		WritableWorkbook wbook = Workbook.createWorkbook(os);
		try {
			int sheetNum = 1;//当前sheet索引
			for (int j = 0; j < totalSheet; j++) {
				// sheet标题
				String sheetName = "第"+curSheet+"页数据";

				// sheet名称
				WritableSheet wsheet = wbook.createSheet(sheetName, sheetNum);
				sheetNum++;
				WritableFont fontCss = new WritableFont(WritableFont.ARIAL, 12,WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,Colour.BLACK);
				WritableCellFormat formatTitle = new WritableCellFormat(fontCss);
				//wsheet.mergeCells(0, 0, 11, 0);
				formatTitle.setBackground(Colour.WHITE);
				formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
				formatTitle.setAlignment(Alignment.CENTRE);
				//wsheet.addCell(new Label(0, 0, "机构存量明细报表", formatTitle));
				fontCss = new jxl.write.WritableFont(WritableFont.ARIAL, 9, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,Colour.BLACK);
				formatTitle = new WritableCellFormat(fontCss);
				formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);

				wsheet.setColumnView(0, 20);
				wsheet.setColumnView(1, 20);
				wsheet.setColumnView(2, 20);
				wsheet.setColumnView(3, 20);
				wsheet.setColumnView(4, 20);
				wsheet.setColumnView(5, 20);
				wsheet.setColumnView(6, 20);
				wsheet.setColumnView(7, 20);
				wsheet.setColumnView(8, 20);
				wsheet.setColumnView(9, 20);
				wsheet.setColumnView(10, 20);
				wsheet.setColumnView(11, 20);
				wsheet.setColumnView(12, 20);
				wsheet.setColumnView(13, 20);
				wsheet.setColumnView(14, 20);
				wsheet.setColumnView(15, 20);

				String orgName=StringUtil.null2String(ConsOrgCache.getInstance().getAllOrgMap().get(ConferenceDtlMap.get("orgCode")));
				wsheet.addCell(new Label(0, 0, "参会部门："));
				wsheet.addCell(new Label(1, 0, orgName));
				wsheet.addCell(new Label(2, 0, "会议名称："));
				wsheet.addCell(new Label(3, 0, StringUtil.null2String(ConferenceDtlMap.get("conferenceName"))));


				// 设置标题：列（从0开始），行（从1开始），显示内容
				wsheet.addCell(new Label(0, 1, "会议ID", formatTitle));
				wsheet.addCell(new Label(1, 1, "会议名称", formatTitle));
				wsheet.addCell(new Label(2, 1, "会议类型", formatTitle));
				wsheet.addCell(new Label(3, 1, "会议日期", formatTitle));
				wsheet.addCell(new Label(4, 1, "举办部门", formatTitle));
				wsheet.addCell(new Label(5, 1, "所属投顾", formatTitle));
				wsheet.addCell(new Label(6, 1, "所属区域", formatTitle));
				wsheet.addCell(new Label(7, 1, "所属部门", formatTitle));
				wsheet.addCell(new Label(8, 1, "报名人姓名", formatTitle));
				wsheet.addCell(new Label(9, 1, "投顾客户号 ", formatTitle));
				wsheet.addCell(new Label(10, 1, "报名人是否投顾", formatTitle));
				wsheet.addCell(new Label(11, 1, "报名数据来源", formatTitle));
				wsheet.addCell(new Label(12, 1, "报名人数 ", formatTitle));
				wsheet.addCell(new Label(13, 1, "实际参会人数", formatTitle));
				wsheet.addCell(new Label(14, 1, "报名时客户类型", formatTitle));
				wsheet.addCell(new Label(15, 1, "高端首单成交时间 ", formatTitle));
				wsheet.addCell(new Label(16, 1, "近90天累计打款产品数", formatTitle));
				wsheet.addCell(new Label(17, 1, "近90天累计打款金额", formatTitle));
				wsheet.addCell(new Label(18, 1, "近90天累计打款产品 ", formatTitle));
				wsheet.addCell(new Label(19, 1, "打款时间区间", formatTitle));
				// 控制行数
				int rows = 2;
				// 对合并后的单元格进行样式设置，包括垂直居中显示
				WritableFont contentFont = new WritableFont(WritableFont.ARIAL,9, WritableFont.NO_BOLD, false,UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
				WritableCellFormat formatContent = new WritableCellFormat(contentFont);
				formatContent.setBackground(Colour.WHITE);
				formatContent.setBorder(Border.ALL, BorderLineStyle.THIN);
				formatContent.setVerticalAlignment(VerticalAlignment.CENTRE);

				int start = curSheet * sheetSize - sheetSize; // 设置导出sheet的起点行索引位置
				int end = curSheet * sheetSize; // 设置导出sheet的结束行索引位置
				if(end > ConferenceDtlList.size()){
					end = ConferenceDtlList.size();
				}
				for (int i = 0; i < ConferenceDtlList.size(); i++) {
					if (x >= start && x < end) {
						// 循环写入内容，同时进行合并单元格操作
						ConferenceDtlNew ConferenceDtl = ConferenceDtlList.get(x);
						wsheet.addCell(new Label(0, rows, (ConferenceDtl.getConferenceId() == null ? "" : ConferenceDtl.getConferenceId()), formatContent));
						wsheet.addCell(new Label(1, rows, (ConferenceDtl.getConferenceName() == null ? "" : ConferenceDtl.getConferenceName()), formatContent));
						wsheet.addCell(new Label(2, rows, (ConferenceDtl.getConferenceTypeNa() == null ? "" : ConferenceDtl.getConferenceTypeNa()), formatContent));
						wsheet.addCell(new Label(3, rows, (ConferenceDtl.getConferenceDt() == null ? "" : ConferenceDtl.getConferenceDt()), formatContent));
						wsheet.addCell(new Label(4, rows, (ConferenceDtl.getConferenceOrgName() == null ? "" : ConferenceDtl.getConferenceOrgName()), formatContent));
						wsheet.addCell(new Label(5, rows, (ConferenceDtl.getConsName() == null ? "" : ConferenceDtl.getConsName()), formatContent));
						wsheet.addCell(new Label(6, rows, (ConferenceDtl.getU2name() == null ? "" : ConferenceDtl.getU2name()), formatContent));
						wsheet.addCell(new Label(7, rows, (ConferenceDtl.getOutletname() == null ? "" : ConferenceDtl.getOutletname()), formatContent));
						wsheet.addCell(new Label(8, rows, (ConferenceDtl.getCustName() == null ? "" : ConferenceDtl.getCustName()), formatContent));
						wsheet.addCell(new Label(9, rows, (ConferenceDtl.getConscustNo() == null ? "" : ConferenceDtl.getConscustNo()), formatContent));
						wsheet.addCell(new Label(10, rows, (ConferenceDtl.getIsCons() == null ? "" : ConferenceDtl.getIsCons()), formatContent));
						wsheet.addCell(new Label(11, rows, (ConferenceDtl.getRegistDataSource() == null ? "" : ConferenceDtl.getRegistDataSource()), formatContent));
						wsheet.addCell(new Label(12, rows, (String.valueOf(ConferenceDtl.getAppointmentsNub()).toString() == null ? "" : String.valueOf(ConferenceDtl.getAppointmentsNub()).toString()), formatContent));
						wsheet.addCell(new Label(13, rows, (String.valueOf(ConferenceDtl.getActualNub()).toString() == null ? "" : String.valueOf(ConferenceDtl.getActualNub()).toString()), formatContent));
						wsheet.addCell(new Label(14, rows, (ConferenceDtl.getCustType() == null ? "" : ConferenceDtl.getCustType()), formatContent));
						wsheet.addCell(new Label(15, rows, (ConferenceDtl.getFirstTradeDt() == null ? "" : ConferenceDtl.getFirstTradeDt()), formatContent));

						wsheet.addCell(new Label(16, rows, (String.valueOf(ConferenceDtl.getFundTotalNum90()).toString() == null ? "" : String.valueOf(ConferenceDtl.getFundTotalNum90()).toString()), formatContent));
						wsheet.addCell(new Label(17, rows, (ConferenceDtl.getRealpayTotalAmtSum90() == null ? "" : format.format(NumberUtil.replaceNullToZero(ConferenceDtl.getRealpayTotalAmtSum90()))), formatContent));
						String rftotal90="";
						if(ConferenceDtl.getRealpayTotalFund90() == null){
							rftotal90="";
						}else{
							rftotal90= ConferenceDtl.getRealpayTotalFund90().replace("</br>", "\n\r");
						}
						wsheet.addCell(new Label(18, rows, (rftotal90 == null ? "" : rftotal90), formatContent));
						wsheet.addCell(new Label(19, rows, (ConferenceDtl.getRealpayTimes() == null ? "" : ConferenceDtl.getRealpayTimes()), formatContent));
						rows++;
					} else {
						break;
					}
					x++;
				}
				curSheet++;
			}
			// 主体内容生成结束
			wbook.write(); // 写入文件
			logger.info("报表导出成功！");
			return true;
		} catch (Exception ex) {
			logger.error("报表导出异常！", ex);
			return false;
		}finally{
			if(wbook!=null){
				wbook.close();
			}
			os.close(); // 关闭流
		}
	}


    /**
     * 转换查询条件
     * @param str
     * @return
     */
    public String getInStr(String str,String columnName){
        if(null!=str&&!"".equals(str.trim())){
            String arr[]= str.split(",");
            StringBuffer sb = new StringBuffer();
            for(int i=0;i<arr.length;i++){
                if ((i % 1000) == 0 && i > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                    sb.append(") OR " + columnName + " IN ( '"
                            + arr[i] + "',");
                } else {
                    sb.append("'" + arr[i] + "',");
                }
            }
            return sb.deleteCharAt(sb.length() - 1).toString();
        }else{
            return null;
        }

    }
}