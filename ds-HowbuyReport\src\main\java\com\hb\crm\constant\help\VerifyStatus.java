/**
 * 
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 激活状态.
 */
public enum VerifyStatus {
	/**
	 * 已生成.
	 */
	Created("1"),
	/**
	 * 已发送.
	 */
	Sent("2"),
	/**
	 * 已激活.
	 */
	Activated("3"),
	
	;
	private String value;

    public String getValue() {
        return value;
    }

    VerifyStatus(String value) {
        this.value = value;
    }
 public final static Map<VerifyStatus, String> VerifyStatusEnumMAP;
    
    static{

    	VerifyStatusEnumMAP = new EnumMap<VerifyStatus, String>(VerifyStatus.class);
    	VerifyStatusEnumMAP.put(VerifyStatus.Created, "已生成");
    	VerifyStatusEnumMAP.put(VerifyStatus.Sent, "已发送");
    	VerifyStatusEnumMAP.put(VerifyStatus.Activated, "已激活");
    
    }
    /**
     * 跟据value返回枚举对应的key
     * 
     * @param value
     * @return PCustLevel
     */
    public static VerifyStatus getEnumMAPKey(String value) {
    	VerifyStatus tmpKey = null;
        for (VerifyStatus tmpEnum : VerifyStatus.values()) {
            if (tmpEnum.value.equals(value)) {
                tmpKey = tmpEnum;
                break;
            }
        }
        return tmpKey;
    }
    /**
     * 返回VerifyStatus对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final String value) {
        return VerifyStatus.VerifyStatusEnumMAP.get(
        		VerifyStatus.getEnumMAPKey(value));
    }
    public static String getEnumDesc(final VerifyStatus value) {
        return VerifyStatus.VerifyStatusEnumMAP.get(
        		value);
    }
}
