package com.hb.postgre.persistence.custtrade;

import com.hb.crm.domain.report.fundnavreport.SalesVolume;
import com.hb.crm.tools.CommPageBean;
import com.hb.postgre.domain.trade.CustTradeNew;
import com.hb.postgre.domain.trade.DmGmNetIncreaseSum;
import com.hb.postgre.domain.trade.GdTurnoverNew;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SalesVolumeNewMapper {


	/**
	 * 营销季报表
	 * @param param
	 * @return
	 */
	public List<SalesVolume> listSalesVolumeReport(Map<String, String> param);

    /**
     * 营销季报表分页
     * @param param
     * @return
     */
    List<SalesVolume> listSalesVolumeReportByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);


    /**
     * 净增量 报表
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listNetAppIncreaseReport(Map<String, String> param);

    /**
     * 公募净增量 报表
     * @param param
     * @return
     */
    public List<DmGmNetIncreaseSum> listPubFundNetAppIncreaseReport(Map<String, String> param);
    /**
     * @description 公募净增量明细
     * @param param
     * @param pageBean
     * @return java.util.List<DmGmNetIncreaseDtl>
     * @author: jianjian.yang
     * @date: 2024/3/12 10:23
     * @since JDK 1.8
     */
    public List<CustTradeNew> listPubFundNetAppIncreaseDtlReportByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
    public List<CustTradeNew> listPubFundNetAppIncreaseDtlReport(Map<String, String> param);

    /**
     * 公募转托管 报表
     * @param param
     * @return
     */
    public List<Map<String,Object>> listPubFundTransferReport(Map<String, String> param);
    public List<CustTradeNew> listPubFundTransferReportDtlReportByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
    public List<CustTradeNew> listPubFundTransferReportDtlReport(Map<String, String> param);

    /**
     * 净增量 投顾 报表
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listConscodeNetAppIncreaseReport(Map<String, String> param);


    /**
     * 净增量 投顾 报表 明细分页
     * @param param
     * @return
     */
    List<GdTurnoverNew> listCustConscodeNetIncreaseDtlReportByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);


    /**
     * 净增量 报表 明细
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listCustConscodeNetIncreaseDtlReport(Map<String, String> param);


    /**
     * 净增量 投顾 报表 产品明细分页
     * @param param
     * @return
     */
    List<GdTurnoverNew> listCustConscodeNetIncreaseProductDtlReportByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);


    /**
     * 净增量 报表 产品明细
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listCustConscodeNetIncreaseProductDtlReport(Map<String, String> param);

    /**
     * 净增量 投顾 报表 交易明细分页
     * @param param
     * @return
     */
    List<GdTurnoverNew> listCustConscodeNetIncreaseTradeDtlReportByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);


    /**
     * 净增量 报表 交易明细
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listCustConscodeNetIncreaseTradeDtlReport(Map<String, String> param);
}
