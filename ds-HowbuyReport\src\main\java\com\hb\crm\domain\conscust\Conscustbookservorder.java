package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Conscustbookservorder.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Conscustbookservorder implements Serializable {

private static final long serialVersionUID = 1L;

	private Integer id;
	
	private Integer bookserviceid;
	
	private String conscustno;
	
	private String content;
	
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getBookserviceid() {
		return this.bookserviceid;
	}

	public void setBookserviceid(Integer bookserviceid) {
		this.bookserviceid = bookserviceid;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
