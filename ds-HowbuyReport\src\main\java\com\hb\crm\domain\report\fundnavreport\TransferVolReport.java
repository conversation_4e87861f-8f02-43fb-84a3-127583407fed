package com.hb.crm.domain.report.fundnavreport;

import java.io.Serializable;
import java.math.BigDecimal;

public class TransferVolReport implements Serializable {
    private static final long serialVersionUID = 4517624336111183643L;
    private String expecttradedt;// 预计交易日期
	private String transferor;// 转让人投顾客户号
	private String transferorName;// 转让人姓名
	private String transferorCons;// 转让人所属投顾
	private String transferorOutlet;// 转让人所属部门
	private String fundcode;// 转让产品代码
    private String fundName;//转让产品名称
	private String assignee;// 受让人投顾客户号
	private BigDecimal transfervol=new BigDecimal(0);// 转让产品份额
	private String assigneeName;// 受让人姓名
	private String assigneeCons;// 受让人所属投顾
	private String assigneeOutlet;// 受让人所属部门

    public String getExpecttradedt() {
        return expecttradedt;
    }

    public void setExpecttradedt(String expecttradedt) {
        this.expecttradedt = expecttradedt;
    }

    public String getTransferor() {
        return transferor;
    }

    public void setTransferor(String transferor) {
        this.transferor = transferor;
    }

    public String getTransferorName() {
        return transferorName;
    }

    public void setTransferorName(String transferorName) {
        this.transferorName = transferorName;
    }

    public String getTransferorCons() {
        return transferorCons;
    }

    public void setTransferorCons(String transferorCons) {
        this.transferorCons = transferorCons;
    }

    public String getTransferorOutlet() {
        return transferorOutlet;
    }

    public void setTransferorOutlet(String transferorOutlet) {
        this.transferorOutlet = transferorOutlet;
    }

    public String getFundcode() {
        return fundcode;
    }

    public void setFundcode(String fundcode) {
        this.fundcode = fundcode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public BigDecimal getTransfervol() {
        return transfervol;
    }

    public void setTransfervol(BigDecimal transfervol) {
        this.transfervol = transfervol;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getAssigneeCons() {
        return assigneeCons;
    }

    public void setAssigneeCons(String assigneeCons) {
        this.assigneeCons = assigneeCons;
    }

    public String getAssigneeOutlet() {
        return assigneeOutlet;
    }

    public void setAssigneeOutlet(String assigneeOutlet) {
        this.assigneeOutlet = assigneeOutlet;
    }
}
