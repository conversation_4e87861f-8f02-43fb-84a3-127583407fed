package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustIndexTrade implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custSource;

	private int custNum;

	private int tradeCustNum;

	private int tradeNum;

	private int currentTrade;

	private Double currentTradeAmt;

	private int lastTrade;

	private Double lastTradeAmt;

	public String getCustSource() {
		return custSource;
	}

	public void setCustSource(String custSource) {
		this.custSource = custSource;
	}

	public int getCustNum() {
		return custNum;
	}

	public void setCustNum(int custNum) {
		this.custNum = custNum;
	}

	public int getTradeCustNum() {
		return tradeCustNum;
	}

	public void setTradeCustNum(int tradeCustNum) {
		this.tradeCustNum = tradeCustNum;
	}

	public int getTradeNum() {
		return tradeNum;
	}

	public void setTradeNum(int tradeNum) {
		this.tradeNum = tradeNum;
	}

	public int getCurrentTrade() {
		return currentTrade;
	}

	public void setCurrentTrade(int currentTrade) {
		this.currentTrade = currentTrade;
	}

	public Double getCurrentTradeAmt() {
		return currentTradeAmt;
	}

	public void setCurrentTradeAmt(Double currentTradeAmt) {
		this.currentTradeAmt = currentTradeAmt;
	}

	public int getLastTrade() {
		return lastTrade;
	}

	public void setLastTrade(int lastTrade) {
		this.lastTrade = lastTrade;
	}

	public Double getLastTradeAmt() {
		return lastTradeAmt;
	}

	public void setLastTradeAmt(Double lastTradeAmt) {
		this.lastTradeAmt = lastTradeAmt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
