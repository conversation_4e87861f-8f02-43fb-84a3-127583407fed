package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

public class ManyCallDtl implements Serializable {

	private String expectTradedt; // 预计交易日期
	private String pCode; // 产品代码
	private String pName; // 产品名称
	private String outletCode; // 部门
    private String u2name; // 区域
	private String orgName;// ORGNAME
	private String consCode; // 投顾
    private String consCustName; //投顾客户号
	private String conCustNo; //
	private String callInfo; //
	private BigDecimal callAmt = new BigDecimal(0);// 预约金额
	private BigDecimal callAmt2 = new BigDecimal(0);// 预约金额
	private BigDecimal callAmt3 = new BigDecimal(0);// 预约金额


	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getExpectTradedt() {
		return expectTradedt;
	}

	public void setExpectTradedt(String expectTradedt) {
		this.expectTradedt = expectTradedt;
	}

	public String getpCode() {
		return pCode;
	}

	public void setpCode(String pCode) {
		this.pCode = pCode;
	}

	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

	public String getOutletCode() {
		return outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsCustName() {
		return consCustName;
	}

	public void setConsCustName(String consCustName) {
		this.consCustName = consCustName;
	}

	public String getCallInfo() {
		return callInfo;
	}

	public void setCallInfo(String callInfo) {
		this.callInfo = callInfo;
	}

	public BigDecimal getCallAmt() {
		return callAmt;
	}

	public void setCallAmt(BigDecimal callAmt) {
		this.callAmt = callAmt;
	}

	public BigDecimal getCallAmt2() {
		return callAmt2;
	}

	public void setCallAmt2(BigDecimal callAmt2) {
		this.callAmt2 = callAmt2;
	}

	public BigDecimal getCallAmt3() {
		return callAmt3;
	}

	public void setCallAmt3(BigDecimal callAmt3) {
		this.callAmt3 = callAmt3;
	}

    public String getConCustNo() {
        return conCustNo;
    }

    public void setConCustNo(String conCustNo) {
        this.conCustNo = conCustNo;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }
}
