package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 配置订单类型系数
 * @reason:
 * @Date: 2020/9/17 09:58
 */
public class CmPrpOrderinfoType<PERSON>oeff implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;
	
	private String orderInfoType;
	
	private String orderInfoTypeVal;

	private String accountProductType;
	
	private String accountProductTypeVal;
	
	private BigDecimal foldCoeff;
	
	private BigDecimal commissionRate;
	
	private String startDt;
	
	private String endDt;

	private String creator;

	private Date createTime;

	private String modor;
	
	private Date updateTime;
	//add by zhangshuai *********配置-订单类型系数-添加字段
	/** 原折标系数 */
	private BigDecimal oldFoldCoeff;
	/** 原佣金率（%） */
	private BigDecimal oldCommissionRate;
	/** 赎回折标系数倒扣 */
	private BigDecimal saleFoldCoeff;

	public BigDecimal getId() {
		return id;
	}

	public void setId(BigDecimal id) {
		this.id = id;
	}

	public String getOrderInfoType() {
		return orderInfoType;
	}

	public void setOrderInfoType(String orderInfoType) {
		this.orderInfoType = orderInfoType;
	}

	public String getOrderInfoTypeVal() {
		return orderInfoTypeVal;
	}

	public void setOrderInfoTypeVal(String orderInfoTypeVal) {
		this.orderInfoTypeVal = orderInfoTypeVal;
	}

	public String getAccountProductType() {
		return accountProductType;
	}

	public void setAccountProductType(String accountProductType) {
		this.accountProductType = accountProductType;
	}

	public String getAccountProductTypeVal() {
		return accountProductTypeVal;
	}

	public void setAccountProductTypeVal(String accountProductTypeVal) {
		this.accountProductTypeVal = accountProductTypeVal;
	}

	public BigDecimal getFoldCoeff() {
		return foldCoeff;
	}

	public void setFoldCoeff(BigDecimal foldCoeff) {
		this.foldCoeff = foldCoeff;
	}

	public BigDecimal getCommissionRate() {
		return commissionRate;
	}

	public void setCommissionRate(BigDecimal commissionRate) {
		this.commissionRate = commissionRate;
	}

	public String getStartDt() {
		return startDt;
	}

	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}

	public String getEndDt() {
		return endDt;
	}

	public void setEndDt(String endDt) {
		this.endDt = endDt;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getModor() {
		return modor;
	}

	public void setModor(String modor) {
		this.modor = modor;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public BigDecimal getOldFoldCoeff() {
		return oldFoldCoeff;
	}

	public void setOldFoldCoeff(BigDecimal oldFoldCoeff) {
		this.oldFoldCoeff = oldFoldCoeff;
	}

	public BigDecimal getOldCommissionRate() {
		return oldCommissionRate;
	}

	public void setOldCommissionRate(BigDecimal oldCommissionRate) {
		this.oldCommissionRate = oldCommissionRate;
	}

	public BigDecimal getSaleFoldCoeff() {
		return saleFoldCoeff;
	}

	public void setSaleFoldCoeff(BigDecimal saleFoldCoeff) {
		this.saleFoldCoeff = saleFoldCoeff;
	}
}
