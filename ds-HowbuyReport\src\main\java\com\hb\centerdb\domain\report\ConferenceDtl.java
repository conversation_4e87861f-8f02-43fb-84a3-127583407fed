package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class ConferenceDtl implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * @Description: 会议ID
	 */
	public String conferenceId;
	/**
	 * @Description: 会议名称
	 */
	public String conferenceName;
	/**
	 * @Description: 会议类型
	 */
	public String conferenceType;
	/**
	 * @Description: 会议类型名称
	 */
	public String conferenceTypeNa;
	/**
	 * @Description: 会议日期
	 */
	public String conferenceDt;
	/**
	 * @Description: 举办部门
	 */
	public String conferenceOrgCode;
	/**
	 * @Description: 举办部门名称
	 */
	public String conferenceOrgName;
	/**
	 * @Description: 投顾code
	 */
	public String consCode;
	/**
	 * @Description: 投顾姓名
	 */
	public String consName;
	/**
	 * @Description: 所属部门
	 */
	public String outletcode;
	/**
	 * @Description: 所属部门名称
	 */
	public String outletname;
	/**
	 * @Description: 投顾客户号
	 */
	public String conscustNo;
	/**
	 * @Description: 报名人姓名
	 */
	public String custName;
	/**
	 * @Description: 报名人是否投顾
	 */
	public String isCons;
	/**
	 * @Description: 报名数据来源
	 */
	public String registDataSource;
	/**
	 * @Description: 报名人数
	 */
	public int appointmentsNub;
	/**
	 * @Description: 邀请码
	 */
	public String appointmentsCode;
	/**
	 * @Description: 实际参会人数
	 */
	public int actualNub;
	/**
	 * @Description: 报名时客户类型
	 */
	public String custType;
	/**
	 * @Description: 首交日期
	 */
	private String firstTradeDt;
	/**
	 * @Description: 预约产品人数-30天
	 */
	public int fundNum30;
	/**
	 * @Description: 预约产品人数-60天
	 */
	public int fundNum60;
	/**
	 * @Description: 预约产品人数-90天
	 */
    public int fundNum90;
	/**
	 * @Description: 预约金额-30天
	 */
	public Double buyamtSum30;
	/**
	 * @Description: 预约金额-60天
	 */
	public Double buyamtSum60;
	/**
	 * @Description: 预约金额-90天
	 */
    public Double buyamtSum90;
	/**
	 * @Description: 打款金额-30天
	 */
	public Double realpayamtSum30;
	/**
	 * @Description: 打款金额-60天
	 */
	public Double realpayamtSum60;
	/**
	 * @Description: 打款金额-90天
	 */
    public Double realpayamtSum90;
	/**
	 * @Description: 会议后购买的基金代码列
	 */
	public String buyFund;
	/**
	 * @Description: 打款日期
	 */
	public String tradeverifyDt;
	/**
	 * @Description: 参会状态
	 */
    public String gdcjlabel;
	/**
	 * @Description: 参会区域名称
	 */
    public String conferenceUpperOrgName;
	/**
	 * @Description: 近90天累计打款产品数
	 */
	public int fundTotalNum90;
	/**
	 * @Description: 近90天累计打款金额
	 */
	public Double realpayTotalAmtSum90;
	/**
	 * @Description: 近90天累计打款产品
	 */
	public String realpayTotalFund90;
	/**
	 * @Description: 打款时间区间
	 */
	public String realpayTimes;

	/**
	 * @Description: 所属区域
	 */
	public String u2name;
	
	
	public String getTradeverifyDt() {
		return tradeverifyDt;
	}

	public void setTradeverifyDt(String tradeverifyDt) {
		this.tradeverifyDt = tradeverifyDt;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getConferenceId() {
		return conferenceId;
	}

	public void setConferenceId(String conferenceId) {
		this.conferenceId = conferenceId;
	}

	public String getConferenceName() {
		return conferenceName;
	}

	public void setConferenceName(String conferenceName) {
		this.conferenceName = conferenceName;
	}

	public String getConferenceOrgCode() {
		return conferenceOrgCode;
	}

	public void setConferenceOrgCode(String conferenceOrgCode) {
		this.conferenceOrgCode = conferenceOrgCode;
	}

	public String getConferenceOrgName() {
		return conferenceOrgName;
	}

	public void setConferenceOrgName(String conferenceOrgName) {
		this.conferenceOrgName = conferenceOrgName;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public int getAppointmentsNub() {
		return appointmentsNub;
	}

	public void setAppointmentsNub(int appointmentsNub) {
		this.appointmentsNub = appointmentsNub;
	}

	public String getAppointmentsCode() {
		return appointmentsCode;
	}

	public void setAppointmentsCode(String appointmentsCode) {
		this.appointmentsCode = appointmentsCode;
	}

	public int getActualNub() {
		return actualNub;
	}

	public void setActualNub(int actualNub) {
		this.actualNub = actualNub;
	}

	public int getFundNum30() {
		return fundNum30;
	}

	public void setFundNum30(int fundNum30) {
		this.fundNum30 = fundNum30;
	}

	public int getFundNum60() {
		return fundNum60;
	}

	public void setFundNum60(int fundNum60) {
		this.fundNum60 = fundNum60;
	}

	public Double getBuyamtSum30() {
		return buyamtSum30;
	}

	public void setBuyamtSum30(Double buyamtSum30) {
		this.buyamtSum30 = buyamtSum30;
	}

	public Double getBuyamtSum60() {
		return buyamtSum60;
	}

	public void setBuyamtSum60(Double buyamtSum60) {
		this.buyamtSum60 = buyamtSum60;
	}

	public Double getRealpayamtSum30() {
		return realpayamtSum30;
	}

	public void setRealpayamtSum30(Double realpayamtSum30) {
		this.realpayamtSum30 = realpayamtSum30;
	}

	public Double getRealpayamtSum60() {
		return realpayamtSum60;
	}

	public void setRealpayamtSum60(Double realpayamtSum60) {
		this.realpayamtSum60 = realpayamtSum60;
	}

	public String getBuyFund() {
		return buyFund;
	}

	public void setBuyFund(String buyFund) {
		this.buyFund = buyFund;
	}

    public int getFundNum90() {
        return fundNum90;
    }

    public void setFundNum90(int fundNum90) {
        this.fundNum90 = fundNum90;
    }

    public Double getBuyamtSum90() {
        return buyamtSum90;
    }

    public void setBuyamtSum90(Double buyamtSum90) {
        this.buyamtSum90 = buyamtSum90;
    }

    public Double getRealpayamtSum90() {
        return realpayamtSum90;
    }

    public void setRealpayamtSum90(Double realpayamtSum90) {
        this.realpayamtSum90 = realpayamtSum90;
    }

    public String getGdcjlabel() {
        return gdcjlabel;
    }

    public void setGdcjlabel(String gdcjlabel) {
        this.gdcjlabel = gdcjlabel;
    }

    public String getConferenceUpperOrgName() {
        return conferenceUpperOrgName;
    }

    public void setConferenceUpperOrgName(String conferenceUpperOrgName) {
        this.conferenceUpperOrgName = conferenceUpperOrgName;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getConferenceType() {
		return conferenceType;
	}

	public void setConferenceType(String conferenceType) {
		this.conferenceType = conferenceType;
	}

	public String getConferenceTypeNa() {
		return conferenceTypeNa;
	}

	public void setConferenceTypeNa(String conferenceTypeNa) {
		this.conferenceTypeNa = conferenceTypeNa;
	}

	public String getConferenceDt() {
		return conferenceDt;
	}

	public void setConferenceDt(String conferenceDt) {
		this.conferenceDt = conferenceDt;
	}

	public String getOutletcode() {
		return outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}

	public String getOutletname() {
		return outletname;
	}

	public void setOutletname(String outletname) {
		this.outletname = outletname;
	}

	public String getIsCons() {
		return isCons;
	}

	public void setIsCons(String isCons) {
		this.isCons = isCons;
	}

	public String getRegistDataSource() {
		return registDataSource;
	}

	public void setRegistDataSource(String registDataSource) {
		this.registDataSource = registDataSource;
	}

	public String getCustType() {
		return custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getFirstTradeDt() {
		return firstTradeDt;
	}

	public void setFirstTradeDt(String firstTradeDt) {
		this.firstTradeDt = firstTradeDt;
	}

	public int getFundTotalNum90() {
		return fundTotalNum90;
	}

	public void setFundTotalNum90(int fundTotalNum90) {
		this.fundTotalNum90 = fundTotalNum90;
	}

	public Double getRealpayTotalAmtSum90() {
		return realpayTotalAmtSum90;
	}

	public void setRealpayTotalAmtSum90(Double realpayTotalAmtSum90) {
		this.realpayTotalAmtSum90 = realpayTotalAmtSum90;
	}

	public String getRealpayTotalFund90() {
		return realpayTotalFund90;
	}

	public void setRealpayTotalFund90(String realpayTotalFund90) {
		this.realpayTotalFund90 = realpayTotalFund90;
	}

	public String getRealpayTimes() {
		return realpayTimes;
	}

	public void setRealpayTimes(String realpayTimes) {
		this.realpayTimes = realpayTimes;
	}

	public String getU2name() {
		return u2name;
	}

	public void setU2name(String u2name) {
		this.u2name = u2name;
	}
}
