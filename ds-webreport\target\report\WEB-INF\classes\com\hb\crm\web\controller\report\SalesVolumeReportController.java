package com.hb.crm.web.controller.report;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

import com.hb.centerdb.domain.report.CustTrade;
import com.hb.crm.constant.StaticVar;
import com.hb.crm.domain.report.fundnavreport.SalesVolume;
import com.hb.crm.service.system.MenuService;
import com.hb.crm.tools.PageData;
import com.hb.crm.tools.StringUtil;
import com.hb.crm.tools.dbutil.SelectControlTool;
import com.hb.crm.util.LoggerUtils;
import com.hb.crm.web.cache.ConsOrgCache;
import com.hb.crm.web.common.constant.DataConstants;
import com.hb.crm.web.common.constant.MenuCodeConstants;
import com.hb.crm.web.util.ParamUtil;
import com.hb.crm.web.util.Util;
import com.hb.crm.util.EasyExcelUtil;
import com.hb.crm.web.util.excel.write.ExcelWriter;
import com.hb.crm.tools.CommPageBean;
import com.hb.postgre.domain.trade.CustTradeNew;
import com.hb.postgre.domain.trade.DmGmNetIncreaseSum;
import com.hb.postgre.domain.trade.GdTurnoverNew;
import com.hb.postgre.service.custtrade.SalesVolumeNewService;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.write.*;
import jxl.write.biff.RowsExceededException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.LinkedHashMap;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created
 */
@Controller
@RequestMapping("/report")
public class SalesVolumeReportController {
	
	private static Logger logger =  LoggerFactory.getLogger(SalesVolumeReportController.class);

    @Autowired
    private SalesVolumeNewService salesVolumeNewService;
    @Autowired
    private MenuService menuService;
		
	@RequestMapping("/listFundnavSalesQuarterReport.do")
	public ModelAndView listFundnavOrderStatisticsDSReport() {
		return new ModelAndView("report/fundnav/FundnavSalesQuarterReport");
	}
    @RequestMapping("/ListCustNetIncreaseReport.do")
    public ModelAndView ListCustNetIncreaseReport() {
        return new ModelAndView("report/fundnav/ListCustNetIncreaseReport");
    }
    @RequestMapping("/listCustConscodeNetIncreaseReport.do")
    public ModelAndView listCustConscodeNetIncreaseReport() {
        return new ModelAndView("report/fundnav/listCustConscodeNetIncreaseReport");
    }

    @RequestMapping("/listPubFundNetIncreaseReport.do")
    public ModelAndView listPubFundNetIncreaseReport() {
        return new ModelAndView("report/fundnav/listPubFundNetIncreaseReport");
    }

    @RequestMapping("/listPubFundTransferReport.do")
    public ModelAndView listPubFundTransferReport(HttpServletRequest request) {
        String menuCodeBase64 = request.getParameter("menuCode");
        try {
            if(menuCodeBase64 != null) {
                String menuCode = new String(Base64.getDecoder().decode(menuCodeBase64), "UTF-8");
                String kpiMenuCode = "140801";
                boolean kpiMenu = Objects.equals(menuCode, kpiMenuCode);
                request.setAttribute("kpiMenu", kpiMenu);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return new ModelAndView("report/fundnav/listPubFundTransferReport");
    }



    /**
     * 公募 净申购 明细
     */
    @RequestMapping("/listPubFundNetIncreaseDtlReport.do")
    public String  listPubFundNetIncreaseDtlReport(HttpServletRequest request)  {
        String conscode = request.getParameter("conscode");
        String queryconscode =request.getParameter("queryconscode");
        String outletcode =request.getParameter("outletcode");
        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String isconsstatus = request.getParameter("isconsstatus");

        String fundglr = request.getParameter("fundglr");
        String custname = request.getParameter("custname");
        String custType = request.getParameter("custType");
        String conscustNo = request.getParameter("conscustNo");
        String bqxlmc = request.getParameter("bqxlmc");
        String productClass1 = request.getParameter("productClass1");
        String productClass2 = request.getParameter("productClass2");
        String productClass3 = request.getParameter("productClass3");

        if(StringUtil.isNotNullStr(queryconscode)){
            conscode = queryconscode ;
        }else if(StringUtil.isNotNullStr(conscode)){
            conscode = conscode ;
        }


        request.setAttribute("conscode", conscode);
        request.setAttribute("outletcode", outletcode);
        request.setAttribute("beginDt", beginDt);
        request.setAttribute("endDt", endDt);
        request.setAttribute("isconsstatus", isconsstatus);
        request.setAttribute("fundglr", fundglr);
        request.setAttribute("custname", custname);
        request.setAttribute("custType", custType);
        request.setAttribute("conscustNo", conscustNo);
        request.setAttribute("bqxlmc", bqxlmc);
        request.setAttribute("productClass1", productClass1);
        request.setAttribute("productClass2", productClass2);
        request.setAttribute("productClass3", productClass3);

        return "report/fundnav/listPubFundNetIncreaseDtlReport";
    }


    /**
     * 净申购 投顾 客户明细
     */
    @RequestMapping("/listCustConscodeNetIncreaseDtlReport.do")
    public String  listCustConscodeNetIncreaseDtlReport(HttpServletRequest request) throws UnsupportedEncodingException {
        String conscode = request.getParameter("conscode");
        String queryconscode =request.getParameter("queryconscode");
        String outletcode =request.getParameter("outletcode");
        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");
        if(StringUtil.isNotNullStr(queryconscode)){
            conscode = queryconscode ;
        }else if(StringUtil.isNotNullStr(conscode)){
            conscode = conscode ;
        }


        request.setAttribute("conscode", conscode);
        request.setAttribute("outletcode", outletcode);
        request.setAttribute("beginDt", beginDt);
        request.setAttribute("endDt", endDt);
        request.setAttribute("isconsstatus", isconsstatus);
        request.setAttribute("bqxlmc", URLDecoder.decode(bqxlmc, DataConstants.CHARSET_NAME_UTF8));
        request.setAttribute("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        request.setAttribute("isFof", isFof);


        return "report/fundnav/listCustConscodeNetIncreaseDtlReport";
    }

    /**
     * 净申购 投顾 产品明细
     */
    @RequestMapping("/listCustConscodeNetIncreaseProductDtlReport.do")
    public String  listCustConscodeNetIncreaseProductDtlReport(HttpServletRequest request) throws UnsupportedEncodingException {
        String conscode = request.getParameter("conscode");
        String queryconscode =request.getParameter("queryconscode");
        String outletcode =request.getParameter("outletcode");
        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");
        if(StringUtil.isNotNullStr(queryconscode)){
            conscode = queryconscode ;
        }else if(StringUtil.isNotNullStr(conscode)){
            conscode = conscode ;
        }


        request.setAttribute("conscode", conscode);
        request.setAttribute("outletcode", outletcode);
        request.setAttribute("beginDt", beginDt);
        request.setAttribute("endDt", endDt);
        request.setAttribute("isconsstatus", isconsstatus);
        request.setAttribute("bqxlmc", URLDecoder.decode(bqxlmc, DataConstants.CHARSET_NAME_UTF8));
        request.setAttribute("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        request.setAttribute("isFof", isFof);



        return "report/fundnav/listCustConscodeNetIncreaseProductDtlReport";
    }

    /**
     * 净申购 投顾 交易明细
     */
    @RequestMapping("/listCustConscodeNetIncreaseTradeDtlReport.do")
    public String  listCustConscodeNetIncreaseTradeDtlReport(HttpServletRequest request) throws UnsupportedEncodingException {
        String conscode = request.getParameter("conscode");
        String queryconscode =request.getParameter("queryconscode");
        String outletcode =request.getParameter("outletcode");
        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");

        if(StringUtil.isNotNullStr(queryconscode)){
            conscode = queryconscode ;
        }else if(StringUtil.isNotNullStr(conscode)){
            conscode = conscode ;
        }


        request.setAttribute("conscode", conscode);
        request.setAttribute("outletcode", outletcode);
        request.setAttribute("beginDt", beginDt);
        request.setAttribute("endDt", endDt);
        request.setAttribute("isconsstatus", isconsstatus);
        request.setAttribute("bqxlmc", URLDecoder.decode(bqxlmc, DataConstants.CHARSET_NAME_UTF8));
        request.setAttribute("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        request.setAttribute("isFof", isFof);


        return "report/fundnav/listCustConscodeNetIncreaseTradeDtlReport";
    }


    /**
     * 公募 转托管 明细
     */
    @RequestMapping("/listPubFundTransferDtlReport.do")
    public String  listPubFundTransferDtlReport(HttpServletRequest request)  {
        String conscode = request.getParameter("conscode");
        String queryconscode =request.getParameter("queryconscode");
        String outletcode =request.getParameter("outletcode");
        String appBeginDt = request.getParameter("appBeginDt");
        String appEndDt = request.getParameter("appEndDt");
        String ackBeginDt = request.getParameter("ackBeginDt");
        String ackEndDt = request.getParameter("ackEndDt");
        String isconsstatus = request.getParameter("isconsstatus");

        String fundglr = request.getParameter("fundglr");
        String custname = request.getParameter("custname");
        String orderStatus = request.getParameter("orderStatus");
        String conscustNo = request.getParameter("conscustNo");
        String tagentNo = request.getParameter("tagentNo");

        if(StringUtil.isNotNullStr(queryconscode)){
            conscode = queryconscode ;
        }else if(StringUtil.isNotNullStr(conscode)){
            conscode = conscode ;
        }

        request.setAttribute("conscode", conscode);
        request.setAttribute("outletcode", outletcode);
        request.setAttribute("appBeginDt", appBeginDt);
        request.setAttribute("appEndDt", appEndDt);
        request.setAttribute("ackBeginDt", ackBeginDt);
        request.setAttribute("ackEndDt", ackEndDt);
        request.setAttribute("isconsstatus", isconsstatus);
        request.setAttribute("fundglr", fundglr);
        request.setAttribute("custname", custname);
        request.setAttribute("orderStatus", orderStatus);
        request.setAttribute("conscustNo", conscustNo);
        request.setAttribute("tagentNo", tagentNo);
        request.setAttribute("tagentNo", tagentNo);

        return "report/fundnav/listPubFundTransferDtlReport";
    }


	/**
	 * 生成报表 
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listFundnavSalesQuarterReport_json.do")
	public Map<String, Object> listFundnavSalesQuarterReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {
		String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
		String tradeBeginDt = request.getParameter("tradeBeginDt");
		String tradeEndDt = request.getParameter("tradeEndDt");
        String curPage = request.getParameter("page");

		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
        param = new ParamUtil(request).getParamMap();
		if(StringUtil.isNotNullStr(tradeBeginDt)){
			param.put("tradeBeginDt", tradeBeginDt);
		}
		if(StringUtil.isNotNullStr(tradeEndDt)){
			param.put("tradeEndDt", tradeEndDt);
		}
		//
		if(!StringUtils.isBlank(consCode)){
			param.put("conscode", consCode);
		}

		//选择了未分配组
		if(orgCode.startsWith("other")){
			param.put("othertearm", orgCode.replaceFirst("other", ""));
		}else{
			String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
			//选择了团队
			if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
				param.put("teamcode", orgCode);
			}else{
				if(!"0".equals(orgCode)){
					List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
					param.put("outletcodes", Util.getSqlInStr(suborgs));
				}
			}
		}
		List<SalesVolume> resultList = new ArrayList<SalesVolume>();
        PageData<SalesVolume> oslist = new PageData();
		oslist = salesVolumeNewService.listSalesVolumeReportByPage(param);

        Map<String,List<SalesVolume>> outletcodemap = new HashMap<String,List<SalesVolume>>();


        for(SalesVolume obj : oslist.getListData()){
            String outletcode= obj.getOrgCode();

            if(outletcodemap.get(outletcode) != null){
                outletcodemap.get(outletcode).add(obj);
            }else{
                List<SalesVolume> temp = new ArrayList<SalesVolume>();
                temp.add(obj);
                outletcodemap.put(outletcode, temp);
            }
        }

        for(String outletcode : outletcodemap.keySet()){
            SalesVolume hj = new SalesVolume();
            if("other".equals(outletcode)){
                hj.setOrgName("其他合计");
                hj.setOrgCode("other");

            }else{
                hj.setOrgName(ConsOrgCache.getInstance().getAllOrgMap().get(outletcode)+"合计");
                hj.setOrgCode(outletcode);
            }
            for(SalesVolume order : outletcodemap.get(outletcode)){
                hj.setPayRmb(hj.getPayRmb().add(order.getPayRmb()));
                hj.setPayDiscountRmb(hj.getPayDiscountRmb().add(order.getPayDiscountRmb()));
                hj.setInnovativeDiscountRmb(hj.getInnovativeDiscountRmb().add(order.getInnovativeDiscountRmb()));
            }
            resultList.add(hj);

            for(SalesVolume order : outletcodemap.get(outletcode)){
                resultList.add(order);
            }
        }
        resultMap.put("total", oslist.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
		resultMap.put("rows", resultList);
		return resultMap;
	}


    /**
     *净申购数据 汇总   查询
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listNetAppIncreaseReport_json.do")
    public Map<String, Object> listNetAppIncreaseReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String orgCode = request.getParameter("orgCode");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("bqxlmc");//一级策略
        String sftzhw = request.getParameter("sftzhw");//是否投资海外
        String isFof = request.getParameter("isFof");

        // 设置查询参数

        Map<String, String> param = new HashMap<String, String>();
        //一级策略处理
        dealBqxlmc(bqxlmc, param);
        param.put("beginDt", beginDt);
        if(StringUtil.isNotNullStr(endDt)){
            param.put("endDt", endDt);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(sftzhw)){
            param.put("sftzhw", sftzhw);
        }
        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("isFof", isFof);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<GdTurnoverNew> resultList = new ArrayList();
        List<GdTurnoverNew> tmpList = new ArrayList();
        List<GdTurnoverNew> oslist = salesVolumeNewService.listNetAppIncreaseReport(param);


        GdTurnoverNew hj = new GdTurnoverNew();
        hj.setU1name("总计:");

        for(GdTurnoverNew obj : oslist){

            obj.setNetIncreaseRate(obj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(obj.getNetIncreaseAmtRmb().divide(obj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));

            hj.setReduceAmtRmb(hj.getReduceAmtRmb().add(obj.getReduceAmtRmb()));
            hj.setIncreaseAmtRmb(hj.getIncreaseAmtRmb().add(obj.getIncreaseAmtRmb()));
            hj.setNetIncreaseAmtRmb(hj.getNetIncreaseAmtRmb().add(obj.getNetIncreaseAmtRmb()));

            tmpList.add(obj);
        }

        hj.setNetIncreaseRate(hj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(hj.getNetIncreaseAmtRmb().divide(hj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));
        resultList.add(hj);

        for(GdTurnoverNew order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        return resultMap;
    }


    /**
     *公募净申购数据 汇总
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listPubFundNetIncreaseReport_json.do")
    public Map<String, Object> listPubFundNetIncreaseReport_json(HttpServletRequest request) throws Exception {

        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String fundglr = request.getParameter("fundglr");
        String custname = request.getParameter("custname");
        String custType = request.getParameter("custType");
        String orgCode = request.getParameter("orgCode");
        String conscode = request.getParameter("consCode");
        String isconsstatus = request.getParameter("isconsstatus");
        String conscustNo = request.getParameter("conscustNo");
        String bqxlmc = request.getParameter("bqxlmc");
        String productClass1 = request.getParameter("productClass1");
        String productClass2 = request.getParameter("productClass2");
        String productClass3 = request.getParameter("productClass3");

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        param.put("beginDt", beginDt);

        if(StringUtil.isNotNullStr(endDt)){
            param.put("endDt", endDt);
        }
        if(StringUtil.isNotNullStr(fundglr)){
            param.put("fundglr", fundglr);
        }
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }
        if(StringUtil.isNotNullStr(custType)){
            param.put("custType", custType);
        }
        if(StringUtil.isNotNullStr(conscode)){
            param.put("conscode", conscode);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(conscustNo)){
            param.put("conscustNo", conscustNo);
        }
        if(StringUtil.isNotNullStr(productClass1)){
            param.put("productClass1", productClass1);
        }
        if(StringUtil.isNotNullStr(productClass2)){
            param.put("productClass2", productClass2);
        }
        if(StringUtil.isNotNullStr(productClass3)){
            param.put("productClass3", productClass3);
        }

        //机构参数
        setOrgParam(param, orgCode);

        //一级策略处理
        dealBqxlmcNoQuot(bqxlmc, param);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<CustTrade> resultList = new ArrayList<CustTrade>();
        List<CustTrade> tmpList = new ArrayList<CustTrade>();

        List<DmGmNetIncreaseSum> oslist = new ArrayList();
        logger.info("new");
        oslist = salesVolumeNewService.listPubFundNetAppIncreaseReport(param);

        Map<String,List<CustTrade>> outletcodemap = new HashMap<String,List<CustTrade>>();

        CustTrade hj = new CustTrade();
        hj.setU1name("总计:");

        for(DmGmNetIncreaseSum dmGmNetIncreaseSum : oslist){
            CustTrade obj = new CustTrade();

            obj.setU1name(dmGmNetIncreaseSum.getU1name());
            obj.setU2name(dmGmNetIncreaseSum.getU2name());
            obj.setU3name(dmGmNetIncreaseSum.getU3name());
            obj.setConsCode(dmGmNetIncreaseSum.getConsCode());
            obj.setConsName(dmGmNetIncreaseSum.getConsName());
            obj.setReduceAmtRmb(dmGmNetIncreaseSum.getReduceAmtRmb());
            obj.setIncreaseAmtRmb(dmGmNetIncreaseSum.getIncreaseAmtRmb());
            obj.setNetIncreaseAmtRmb(dmGmNetIncreaseSum.getNetIncreaseAmtRmb());

            hj.setReduceAmtRmb(hj.getReduceAmtRmb().add(obj.getReduceAmtRmb()));
            hj.setIncreaseAmtRmb(hj.getIncreaseAmtRmb().add(obj.getIncreaseAmtRmb()));
            hj.setNetIncreaseAmtRmb(hj.getNetIncreaseAmtRmb().add(obj.getNetIncreaseAmtRmb()));

            tmpList.add(obj);
        }

        resultList.add(hj);

        for(CustTrade order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        return resultMap;
    }
    /**
     * @description 机构参数
     * @param param
     * @param orgCode
     * @return void
     * @author: jianjian.yang
     * @date: 2024/4/8 11:13
     * @since JDK 1.8
     */
    private void setOrgParam(Map<String, String> param, String orgCode){
        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
    }


    /**
     *公募转托管数据 汇总
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listPubFundTransferReport_json.do")
    public Map<String, Object> listPubFundTransferReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String appBeginDt = request.getParameter("appBeginDt");
        String appEndDt = request.getParameter("appEndDt");
        String orgCode = request.getParameter("orgCode");
        String conscode = request.getParameter("consCode");
        String orderStatus = request.getParameter("orderStatus");
        String custname = request.getParameter("custname");
        String ackBeginDt = request.getParameter("ackBeginDt");
        String ackEndDt = request.getParameter("ackEndDt");
        String fundglr = request.getParameter("fundglr");
        String isconsstatus = request.getParameter("isconsstatus");
        String conscustNo = request.getParameter("conscustNo");
        String tagentNo = request.getParameter("tagentNo");
        String userId = request.getParameter("userId");

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        param.put("appBeginDt", appBeginDt);

        if(StringUtil.isNotNullStr(appEndDt)){
            param.put("appEndDt", appEndDt);
        }
        if(StringUtil.isNotNullStr(conscode)){
            param.put("conscode", conscode);
        }
        if(StringUtil.isNotNullStr(orderStatus)){
            param.put("orderStatus", orderStatus);
        }
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }
        if(StringUtil.isNotNullStr(ackBeginDt)){
            param.put("ackBeginDt", ackBeginDt);
        }
        if(StringUtil.isNotNullStr(ackEndDt)){
            param.put("ackEndDt", ackEndDt);
        }
        if(StringUtil.isNotNullStr(fundglr)){
            param.put("fundglr", fundglr);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(conscustNo)){
            param.put("conscustNo", conscustNo);
        }
        if(StringUtil.isNotNullStr(tagentNo)){
            param.put("tagentNo", tagentNo);
        }
        if(StringUtil.isNotNullStr(userId)){
            param.put("userId", userId);
        }

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<CustTradeNew> resultList = new ArrayList<CustTradeNew>();
        List<CustTradeNew> tmpList = new ArrayList<CustTradeNew>();

        List<Map<String,Object>> oslist = new ArrayList<Map<String,Object>>();
        oslist = salesVolumeNewService.listPubFundTransferReport(param);

        Map<String,List<CustTradeNew>> outletcodemap = new HashMap<String,List<CustTradeNew>>();

        CustTradeNew hj = new CustTradeNew();
        hj.setU1name("总计:");

        for(Map<String,Object> map : oslist){
            String outletcode=Util.ObjectToString(map.get("outletcode"));
            CustTradeNew obj = new CustTradeNew();

            obj.setU1name(Util.ObjectToString(map.get("u1name")));
            obj.setU2name(Util.ObjectToString(map.get("u2name")));
            obj.setU3name(Util.ObjectToString(map.get("u3name")));
            obj.setConsCode(Util.ObjectToString(map.get("conscode")));
            obj.setConsName(Util.ObjectToString(map.get("consname")));
            obj.setCustNum(Util.ObjectToBigDecimal(map.get("custnum")));
            obj.setAppAmt(Util.ObjectToBigDecimal(map.get("appamt")));
            obj.setAvgAmt(Util.ObjectToBigDecimal(map.get("avgamt")));

            hj.setCustNum(hj.getCustNum().add(obj.getCustNum()));
            hj.setAppAmt(hj.getAppAmt().add(obj.getAppAmt()));

            hj.setAvgAmt((hj.getCustNum().compareTo(BigDecimal.ZERO) == 0?new BigDecimal(0):(hj.getAppAmt().divide(hj.getCustNum(),4,BigDecimal.ROUND_DOWN))));

            tmpList.add(obj);
        }

        resultList.add(hj);

        for(CustTradeNew order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        return resultMap;
    }

    /**
     *净申购数据 投顾层面 汇总
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCustConscodeNetIncrease_json.do")
    public Map<String, Object> listCustConscodeNetIncrease_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String consCode = request.getParameter("consCode");
        String orgCode = request.getParameter("orgCode");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        //一级策略处理
        dealBqxlmc(bqxlmc, param);
        param.put("beginDt", beginDt);
        if(StringUtil.isNotNullStr(endDt)){
            param.put("endDt", endDt);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(sftzhw)){
            param.put("sftzhw", sftzhw);
        }
        if(StringUtil.isNotNullStr(consCode)){
            param.put("conscode", consCode);
        }else{
            if(StringUtil.isNotNullStr(orgCode)){
                //选择了未分配组
                if(orgCode.startsWith("other")){
                    param.put("othertearm", orgCode.replaceFirst("other", ""));
                }else{
                    String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                    //选择了团队
                    if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                        param.put("teamcode", orgCode);
                    }else{
                        if(!"0".equals(orgCode)){
                            List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                            param.put("outletcodes", Util.getSqlInStr(suborgs));
                        }
                    }
                }
            }
        }
        param.put("isFof", isFof);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<GdTurnoverNew> resultList = new ArrayList();
        List<GdTurnoverNew> tmpList = new ArrayList();

        List<GdTurnoverNew> oslist = salesVolumeNewService.listConscodeNetAppIncreaseReport(param);

        GdTurnoverNew hj = new GdTurnoverNew();
        hj.setU1name("总计:");

        for(GdTurnoverNew obj : oslist){

            obj.setNetIncreaseRate(obj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(obj.getNetIncreaseAmtRmb().divide(obj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));


            hj.setReduceAmtRmb(hj.getReduceAmtRmb().add(obj.getReduceAmtRmb()));
            hj.setIncreaseAmtRmb(hj.getIncreaseAmtRmb().add(obj.getIncreaseAmtRmb()));
            hj.setNetIncreaseAmtRmb(hj.getNetIncreaseAmtRmb().add(obj.getNetIncreaseAmtRmb()));

            tmpList.add(obj);
        }

        hj.setNetIncreaseRate(hj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(hj.getNetIncreaseAmtRmb().divide(hj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));
        resultList.add(hj);

        for(GdTurnoverNew order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        return resultMap;
    }


    /**
     * 净申购 投顾客户明细
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCustConscodeNetIncreaseDtlReport_json.do")
    public Map<String, Object> listCustConscodeNetIncreaseDtlReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String curPage = request.getParameter("page");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        if(StringUtils.isNotEmpty(bqxlmc)){
            String[] split = URLDecoder.decode(bqxlmc, DataConstants.CHARSET_NAME_UTF8).split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
        param.put("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        param.put("isFof", isFof);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        PageData<GdTurnoverNew> DtlList = salesVolumeNewService.listCustConscodeNetIncreaseDtlReportByPage(param);
        resultMap.put("total", DtlList.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
        resultMap.put("rows", DtlList.getListData());
        return resultMap;
    }

    /**
     * 净申购 投顾 产品明细
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCustConscodeNetIncreaseProductDtlReport_json.do")
    public Map<String, Object> listCustConscodeNetIncreaseProductDtlReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String curPage = request.getParameter("page");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        if(StringUtils.isNotEmpty(bqxlmc)){
            String[] split = URLDecoder.decode(bqxlmc, DataConstants.CHARSET_NAME_UTF8).split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
        param.put("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        PageData<GdTurnoverNew> DtlList = salesVolumeNewService.listCustConscodeNetIncreaseProductDtlReportByPage(param);
        resultMap.put("total", DtlList.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
        resultMap.put("rows", DtlList.getListData());
        return resultMap;
    }

    /**
     * 净申购 投顾 交易明细
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCustConscodeNetIncreaseTradeDtlReport_json.do")
    public Map<String, Object> listCustConscodeNetIncreaseTradeDtlReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String orgCode = request.getParameter("outletcode");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String curPage = request.getParameter("page");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
       param = new ParamUtil(request).getParamMap();

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        if(StringUtils.isNotEmpty(bqxlmc)){
            String[] split = URLDecoder.decode(bqxlmc, DataConstants.CHARSET_NAME_UTF8).split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
        param.put("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        PageData<GdTurnoverNew> DtlList = salesVolumeNewService.listCustConscodeNetIncreaseTradeDtlReportByPage(param);
        resultMap.put("total", DtlList.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
        resultMap.put("rows", DtlList.getListData());
        return resultMap;
    }

    /**
     * 公募净申购  明细
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listPubFundNetIncreaseTradeDtlReport_json.do")
    public Map<String, Object> listPubFundNetIncreaseTradeDtlReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String fundglr = request.getParameter("fundglr");
        String custname = request.getParameter("custname");
        String custType = request.getParameter("custType");
        String conscustNo = request.getParameter("conscustNo");
        String curPage = request.getParameter("page");
        String bqxlmc = request.getParameter("bqxlmc");
        String productClass1 = request.getParameter("productClass1");
        String productClass2 = request.getParameter("productClass2");
        String productClass3 = request.getParameter("productClass3");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        if(StringUtil.isNotNullStr(productClass1)){
            param.put("productClass1", productClass1);
        }
        if(StringUtil.isNotNullStr(productClass2)){
            param.put("productClass2", productClass2);
        }
        if(StringUtil.isNotNullStr(productClass3)){
            param.put("productClass3", URLDecoder.decode(productClass3, DataConstants.CHARSET_NAME_UTF8));
        }
        //一级策略处理
        if(StringUtil.isNotNullStr(bqxlmc)) {
            dealBqxlmcNoQuot(bqxlmc, param);
        }
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        param.put("fundglr", fundglr);
        param.put("custname", custname);
        param.put("custType", custType);
        param.put("conscustNo", conscustNo);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        PageData<CustTradeNew> DtlList = salesVolumeNewService.listPubFundNetAppIncreaseDtlReportByPage(param);
        resultMap.put("total", DtlList.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
        resultMap.put("rows", DtlList.getListData());
        return resultMap;
    }


    /**
     * 公募转托管 明细
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listPubFundTransferDtlReport_json.do")
    public Map<String, Object> listPubFundTransferDtlReport_json(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {

        String appBeginDt = request.getParameter("appBeginDt");
        String appEndDt = request.getParameter("appEndDt");
        String orgCode = request.getParameter("outletcode");
        String conscode = request.getParameter("conscode");
        String orderStatus = request.getParameter("orderStatus");
        String custname = request.getParameter("custname");
        String ackBeginDt = request.getParameter("ackBeginDt");
        String ackEndDt = request.getParameter("ackEndDt");
        String fundglr = request.getParameter("fundglr");
        String isconsstatus = request.getParameter("isconsstatus");
        String conscustNo = request.getParameter("conscustNo");
        String tagentNo = request.getParameter("tagentNo");
        String curPage = request.getParameter("page");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("appBeginDt", appBeginDt);

        if(StringUtil.isNotNullStr(appEndDt)){
            param.put("appEndDt", appEndDt);
        }
        if(StringUtil.isNotNullStr(conscode)){
            param.put("conscode", conscode);
        }
        if(StringUtil.isNotNullStr(orderStatus)){
            param.put("orderStatus", orderStatus);
        }
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }
        if(StringUtil.isNotNullStr(ackBeginDt)){
            param.put("ackBeginDt", ackBeginDt);
        }
        if(StringUtil.isNotNullStr(ackEndDt)){
            param.put("ackEndDt", ackEndDt);
        }
        if(StringUtil.isNotNullStr(fundglr)){
            param.put("fundglr", fundglr);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(conscustNo)){
            param.put("conscustNo", conscustNo);
        }
        if(StringUtil.isNotNullStr(tagentNo)){
            param.put("tagentNo", tagentNo);
        }
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        PageData<CustTradeNew> DtlList = salesVolumeNewService.listPubFundTransferReportDtlReportByPage(param);
        resultMap.put("total", DtlList.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
        resultMap.put("rows", DtlList.getListData());
        return resultMap;
    }

	/**
	 * 导出数据
	 * @param request
	 * @param response
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/exportFundnavSalesQuarterReport.do")
	public String exportFundnavSalesQuarterReport(HttpServletRequest request,
			HttpServletResponse response) {
		String result = "";

        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String tradeBeginDt = request.getParameter("tradeBeginDt");
        String tradeEndDt = request.getParameter("tradeEndDt");

        Map<String, Object> resultMap = new HashMap<String, Object>();
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        if(StringUtil.isNotNullStr(tradeBeginDt)){
            param.put("tradeBeginDt", tradeBeginDt);
        }
        if(StringUtil.isNotNullStr(tradeEndDt)){
            param.put("tradeEndDt", tradeEndDt);
        }
        //
        if(!StringUtils.isBlank(consCode)){
            param.put("conscode", consCode);
        }

        //选择了未分配组
        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        List<SalesVolume> resultList = new ArrayList<SalesVolume>();
        List<SalesVolume> oslist = new ArrayList();
        oslist = salesVolumeNewService.listSalesVolumeReport(param);

        Map<String,List<SalesVolume>> outletcodemap = new HashMap<String,List<SalesVolume>>();


        for(SalesVolume obj : oslist){
            String outletcode= obj.getOrgCode();

            if(outletcodemap.get(outletcode) != null){
                outletcodemap.get(outletcode).add(obj);
            }else{
                List<SalesVolume> temp = new ArrayList<SalesVolume>();
                temp.add(obj);
                outletcodemap.put(outletcode, temp);
            }
        }

        for(String outletcode : outletcodemap.keySet()){
            SalesVolume hj = new SalesVolume();
            if("other".equals(outletcode)){
                hj.setOrgName("其他合计");
                hj.setOrgCode("other");

            }else{
                hj.setOrgName(ConsOrgCache.getInstance().getAllOrgMap().get(outletcode)+"合计");
                hj.setOrgCode(outletcode);
            }
            for(SalesVolume order : outletcodemap.get(outletcode)){
                hj.setPayRmb(hj.getPayRmb().add(order.getPayRmb()));
                hj.setPayDiscountRmb(hj.getPayDiscountRmb().add(order.getPayDiscountRmb()));
                hj.setInnovativeDiscountRmb(hj.getInnovativeDiscountRmb().add(order.getInnovativeDiscountRmb()));
            }
            resultList.add(hj);

            for(SalesVolume order : outletcodemap.get(outletcode)){
                resultList.add(order);
            }
        }
		
		resultMap.put("rows", resultList);
		resultMap.put("downName", "营销季报表.xls");
		try{
			exportSalesVolumeReportList(response, resultMap);
			result = "success";
		}catch(Exception ex){
			result = "error";
			ex.printStackTrace();
		}
		return result;	
	}



    /**
     * 净申购 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportNetAppIncreaseReport.do")
    public String exportNetAppIncreaseReport(HttpServletRequest request,
                                                  HttpServletResponse response) {
        String result = "";

        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String orgCode = request.getParameter("orgCode");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("downbqxlmc");//一级策略
        String sftzhw = request.getParameter("downsftzhw");//是否投资海外
        //是否自营FOF
        String isFof = request.getParameter("isFof");


        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        //一级策略处理
        dealBqxlmc(bqxlmc, param);

        param.put("beginDt", beginDt);

        if(StringUtil.isNotNullStr(endDt)){
            param.put("endDt", endDt);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(sftzhw)){
            param.put("sftzhw", sftzhw);
        }

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("isFof", isFof);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<GdTurnoverNew> resultList = new ArrayList();
        List<GdTurnoverNew> tmpList = new ArrayList();

        List<GdTurnoverNew> oslist = salesVolumeNewService.listNetAppIncreaseReport(param);

        GdTurnoverNew hj = new GdTurnoverNew();
        hj.setU1name("总计:");

        for(GdTurnoverNew obj : oslist){
            obj.setNetIncreaseRate(obj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(obj.getNetIncreaseAmtRmb().divide(obj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));


            hj.setReduceAmtRmb(hj.getReduceAmtRmb().add(obj.getReduceAmtRmb()));
            hj.setIncreaseAmtRmb(hj.getIncreaseAmtRmb().add(obj.getIncreaseAmtRmb()));
            hj.setNetIncreaseAmtRmb(hj.getNetIncreaseAmtRmb().add(obj.getNetIncreaseAmtRmb()));

            tmpList.add(obj);
        }

        hj.setNetIncreaseRate(hj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(hj.getNetIncreaseAmtRmb().divide(hj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));
        resultList.add(hj);

        for(GdTurnoverNew order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        resultMap.put("downName", "净申购报表.xls");
        try{
            exportNetAppIncreaseReportList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 净申购 投顾  导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportCustConscodeNetIncrease.do")
    public String exportCustConscodeNetIncrease(HttpServletRequest request,
                                             HttpServletResponse response) {
        String result = "";

        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String consCode = request.getParameter("consCode");
        String orgCode = request.getParameter("orgCode");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("downbqxlmc");
        String sftzhw = request.getParameter("downsftzhw");
        String isFof = request.getParameter("isFof");

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        param.put("beginDt", beginDt);

        if(StringUtil.isNotNullStr(endDt)){
            param.put("endDt", endDt);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        //一级策略处理
        dealBqxlmc(bqxlmc, param);
        if(StringUtil.isNotNullStr(sftzhw)){
            param.put("sftzhw", sftzhw);
        }


        if(StringUtil.isNotNullStr(consCode)){
            param.put("conscode", consCode);
        }else{
            if(StringUtil.isNotNullStr(orgCode)){
                //选择了未分配组
                if(orgCode.startsWith("other")){
                    param.put("othertearm", orgCode.replaceFirst("other", ""));
                }else{
                    String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                    //选择了团队
                    if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                        param.put("teamcode", orgCode);
                    }else{
                        if(!"0".equals(orgCode)){
                            List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                            param.put("outletcodes", Util.getSqlInStr(suborgs));
                        }
                    }
                }
            }
        }
        param.put("isFof", isFof);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<GdTurnoverNew> resultList = new ArrayList();
        List<GdTurnoverNew> tmpList = new ArrayList();

        List<GdTurnoverNew> oslist = new ArrayList();
        oslist = salesVolumeNewService.listConscodeNetAppIncreaseReport(param);

        Map<String,List<GdTurnoverNew>> outletcodemap = new HashMap();

        GdTurnoverNew hj = new GdTurnoverNew();
        hj.setU1name("总计:");

        for(GdTurnoverNew obj : oslist){
            obj.setNetIncreaseRate(obj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(obj.getNetIncreaseAmtRmb().divide(obj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));


            hj.setReduceAmtRmb(hj.getReduceAmtRmb().add(obj.getReduceAmtRmb()));
            hj.setIncreaseAmtRmb(hj.getIncreaseAmtRmb().add(obj.getIncreaseAmtRmb()));
            hj.setNetIncreaseAmtRmb(hj.getNetIncreaseAmtRmb().add(obj.getNetIncreaseAmtRmb()));

            tmpList.add(obj);
        }

        hj.setNetIncreaseRate(hj.getIncreaseAmtRmb().compareTo(BigDecimal.ZERO) == 0?new BigDecimal("0"):(hj.getNetIncreaseAmtRmb().divide(hj.getIncreaseAmtRmb(),4,BigDecimal.ROUND_HALF_UP)));
        resultList.add(hj);

        for(GdTurnoverNew order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        resultMap.put("downName", "净申购投顾报表.xls");
        try{
            exportCustConscodeNetIncreaseList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 公募 净申购 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportPubFundNetIncrease.do")
    public String exportPubFundNetIncrease(HttpServletRequest request,
                                                HttpServletResponse response) {
        String result = "";

        String beginDt = request.getParameter("tradeBeginDt");
        String endDt = request.getParameter("tradeEndDt");
        String fundglr = request.getParameter("fundglr");
        String custname = request.getParameter("custname");
        String custType = request.getParameter("custType");
        String orgCode = request.getParameter("orgCode");
        String conscode = request.getParameter("consCode");
        String isconsstatus = request.getParameter("isconsstatus");
        String conscustNo = request.getParameter("conscustNo");
        String bqxlmc = request.getParameter("bqxlmc");
        String productClass1 = request.getParameter("productClass1");
        String productClass2 = request.getParameter("productClass2");
        String productClass3 = request.getParameter("productClass3");

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        param.put("beginDt", beginDt);

        if(StringUtil.isNotNullStr(endDt)){
            param.put("endDt", endDt);
        }
        if(StringUtil.isNotNullStr(fundglr)){
            param.put("fundglr", fundglr);
        }
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }
        if(StringUtil.isNotNullStr(custType)){
            param.put("custType", custType);
        }
        if(StringUtil.isNotNullStr(conscode)){
            param.put("conscode", conscode);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(conscustNo)){
            param.put("conscustNo", conscustNo);
        }
        if(StringUtil.isNotNullStr(productClass1)){
            param.put("productClass1", productClass1);
        }
        if(StringUtil.isNotNullStr(productClass2)){
            param.put("productClass2", productClass2);
        }
        if(StringUtil.isNotNullStr(productClass3)){
            param.put("productClass3", productClass3);
        }

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        //一级策略处理
        dealBqxlmcNoQuot(bqxlmc, param);


        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<CustTrade> resultList = new ArrayList<CustTrade>();
        List<CustTrade> tmpList = new ArrayList<CustTrade>();

        List<DmGmNetIncreaseSum> oslist = new ArrayList();
        oslist = salesVolumeNewService.listPubFundNetAppIncreaseReport(param);

        Map<String,List<CustTrade>> outletcodemap = new HashMap<String,List<CustTrade>>();

        CustTrade hj = new CustTrade();
        hj.setU1name("总计:");

        for(DmGmNetIncreaseSum dmGmNetIncreaseSum : oslist){
            CustTrade obj = new CustTrade();

            obj.setU1name(dmGmNetIncreaseSum.getU1name());
            obj.setU2name(dmGmNetIncreaseSum.getU2name());
            obj.setU3name(dmGmNetIncreaseSum.getU3name());
            obj.setConsCode(dmGmNetIncreaseSum.getConsCode());
            obj.setConsName(dmGmNetIncreaseSum.getConsName());
            obj.setReduceAmtRmb(dmGmNetIncreaseSum.getReduceAmtRmb());
            obj.setIncreaseAmtRmb(dmGmNetIncreaseSum.getIncreaseAmtRmb());
            obj.setNetIncreaseAmtRmb(dmGmNetIncreaseSum.getNetIncreaseAmtRmb());

            hj.setReduceAmtRmb(hj.getReduceAmtRmb().add(obj.getReduceAmtRmb()));
            hj.setIncreaseAmtRmb(hj.getIncreaseAmtRmb().add(obj.getIncreaseAmtRmb()));
            hj.setNetIncreaseAmtRmb(hj.getNetIncreaseAmtRmb().add(obj.getNetIncreaseAmtRmb()));

            tmpList.add(obj);
        }

        resultList.add(hj);

        for(CustTrade order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        resultMap.put("downName", "公募净申购报表.xls");
        try{
            exportPubFundNetIncreaseList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 公募 转托管 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportPubFundTransfer.do")
    public String exportPubFundTransfer(HttpServletRequest request,
                                           HttpServletResponse response) {
        String result = "";

        String appBeginDt = request.getParameter("appBeginDt");
        String appEndDt = request.getParameter("appEndDt");
        String orgCode = request.getParameter("orgCode");
        String conscode = request.getParameter("consCode");
        String orderStatus = request.getParameter("orderStatus");
        String custname = request.getParameter("custname");
        String ackBeginDt = request.getParameter("ackBeginDt");
        String ackEndDt = request.getParameter("ackEndDt");
        String fundglr = request.getParameter("fundglr");
        String isconsstatus = request.getParameter("isconsstatus");
        String conscustNo = request.getParameter("conscustNo");
        String tagentNo = request.getParameter("tagentNo");
        String userId = request.getParameter("userId");

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        param.put("appBeginDt", appBeginDt);

        if(StringUtil.isNotNullStr(appEndDt)){
            param.put("appEndDt", appEndDt);
        }
        if(StringUtil.isNotNullStr(conscode)){
            param.put("conscode", conscode);
        }
        if(StringUtil.isNotNullStr(orderStatus)){
            param.put("orderStatus", orderStatus);
        }
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }
        if(StringUtil.isNotNullStr(ackBeginDt)){
            param.put("ackBeginDt", ackBeginDt);
        }
        if(StringUtil.isNotNullStr(ackEndDt)){
            param.put("ackEndDt", ackEndDt);
        }
        if(StringUtil.isNotNullStr(fundglr)){
            param.put("fundglr", fundglr);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(conscustNo)){
            param.put("conscustNo", conscustNo);
        }
        if(StringUtil.isNotNullStr(tagentNo)){
            param.put("tagentNo", tagentNo);
        }
        if(StringUtil.isNotNullStr(userId)){
            param.put("userId", userId);
        }

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<CustTradeNew> resultList = new ArrayList<CustTradeNew>();
        List<CustTradeNew> tmpList = new ArrayList<CustTradeNew>();

        List<Map<String,Object>> oslist = new ArrayList<Map<String,Object>>();
        oslist = salesVolumeNewService.listPubFundTransferReport(param);

        Map<String,List<CustTradeNew>> outletcodemap = new HashMap<String,List<CustTradeNew>>();

        CustTradeNew hj = new CustTradeNew();
        hj.setU1name("总计:");

        for(Map<String,Object> map : oslist){
            String outletcode=Util.ObjectToString(map.get("outletcode"));
            CustTradeNew obj = new CustTradeNew();

            obj.setU1name(Util.ObjectToString(map.get("u1name")));
            obj.setU2name(Util.ObjectToString(map.get("u2name")));
            obj.setU3name(Util.ObjectToString(map.get("u3name")));
            obj.setConsCode(Util.ObjectToString(map.get("conscode")));
            obj.setConsName(Util.ObjectToString(map.get("consname")));
            obj.setCustNum(Util.ObjectToBigDecimal(map.get("custnum")));
            obj.setAppAmt(Util.ObjectToBigDecimal(map.get("appamt")));
            obj.setAvgAmt(Util.ObjectToBigDecimal(map.get("avgamt")));

            hj.setCustNum(hj.getCustNum().add(obj.getCustNum()));
            hj.setAppAmt(hj.getAppAmt().add(obj.getAppAmt()));
            hj.setAvgAmt((hj.getCustNum().compareTo(BigDecimal.ZERO) == 0?new BigDecimal(0):(hj.getAppAmt().divide(hj.getCustNum(),4,BigDecimal.ROUND_DOWN))));


            tmpList.add(obj);
        }

        resultList.add(hj);

        for(CustTradeNew order : tmpList){
            resultList.add(order);
        }

        resultMap.put("rows", resultList);
        String menuName = menuService.getMenuName(MenuCodeConstants.PUB_TRANSFER_MENU_CODE);
        resultMap.put("downName", menuName + ".xls");
        try{
            exportPubFundTransferList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 净申购 投顾 明细 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportCustConscodeNetIncreaseDtl.do")
    public String exportCustConscodeNetIncreaseDtl(HttpServletRequest request,
                                                HttpServletResponse response) throws UnsupportedEncodingException {
        String result = "";

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String curPage = request.getParameter("page");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        param.put("isFof", isFof);
        if(StringUtils.isNotEmpty(bqxlmc)){
            String[] split = bqxlmc.split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
        param.put("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        List<GdTurnoverNew> resultList = salesVolumeNewService.listCustConscodeNetIncreaseDtlReport(param);
        resultMap.put("rows", resultList);
        resultMap.put("downName", "净申购投顾明细报表.xls");
        try{
            exportCustConscodeNetIncreaseDtlList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }

    /**
     * 净申购 投顾 产品明细 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportCustConscodeNetIncreaseProductDtl.do")
    public String exportCustConscodeNetIncreaseProductDtl(HttpServletRequest request,
                                                   HttpServletResponse response) throws UnsupportedEncodingException {
        String result = "";

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String curPage = request.getParameter("page");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String isFof = request.getParameter("isFof");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        param.put("isFof", isFof);
        if(StringUtils.isNotEmpty(bqxlmc)){
            String[] split = bqxlmc.split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
        param.put("sftzhw", URLDecoder.decode(sftzhw, DataConstants.CHARSET_NAME_UTF8));
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取手续费
        List<GdTurnoverNew> resultList = salesVolumeNewService.listCustConscodeNetIncreaseProductDtlReport(param);
        resultMap.put("rows", resultList);
        resultMap.put("downName", "净申购产品明细报表.xls");
        try{
            exportCustConscodeNetIncreaseProductDtlList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 净申购 投顾 交易明细 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportCustConscodeNetIncreaseTradeDtl.do")
    public String exportCustConscodeNetIncreaseTradeDtl(HttpServletRequest request,
                                                          HttpServletResponse response) {
        String result = "";

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String bqxlmc = request.getParameter("bqxlmc");
        String sftzhw = request.getParameter("sftzhw");
        String curPage = request.getParameter("page");
        String isFof = request.getParameter("isFof");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        param.put("isFof", isFof);
        if(StringUtils.isNotEmpty(bqxlmc)){
            String[] split = bqxlmc.split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
        param.put("sftzhw", sftzhw);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String uuid = LoggerUtils.getUuid();
        SelectControlTool.put(uuid);
        try {
            //获取手续费
            List<GdTurnoverNew> resultList = salesVolumeNewService.listCustConscodeNetIncreaseTradeDtlReport(param);
            resultMap.put("rows", resultList);
        } finally {
            SelectControlTool.SelectInfo selectInfo = SelectControlTool.get(uuid);
            SelectControlTool.remove(uuid);
            if(selectInfo != null && selectInfo.getOverflow()){
                result = "overflow";
                return result;
            }
        }
        resultMap.put("downName", "净申购交易明细报表.xls");
        try{
            exportCustConscodeNetIncreaseTradeDtlList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }

    /**
     * 公募 净申购 明细 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportPubNetIncreaseTradeDtl.do")
    public String exportPubNetIncreaseTradeDtl(HttpServletRequest request,
                                                        HttpServletResponse response) {
        String result = "";

        String conscode = request.getParameter("conscode");
        String orgCode = request.getParameter("outletcode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");
        String isconsstatus = request.getParameter("isconsstatus");
        String fundglr = request.getParameter("fundglr");
        String custname = request.getParameter("custname");
        String custType = request.getParameter("custType");
        String conscustNo = request.getParameter("conscustNo");
        String bqxlmc = request.getParameter("bqxlmc");
        String productClass1 = request.getParameter("productClass1");
        String productClass2 = request.getParameter("productClass2");
        String productClass3 = request.getParameter("productClass3");

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("beginDt", beginDt);
        param.put("endDt", endDt);
        param.put("isconsstatus", isconsstatus);
        param.put("conscode", conscode);
        param.put("fundglr", fundglr);
        param.put("custname", custname);
        param.put("custType", custType);
        param.put("conscustNo", conscustNo);
        if(StringUtil.isNotNullStr(productClass1)){
            param.put("productClass1", productClass1);
        }
        if(StringUtil.isNotNullStr(productClass2)){
            param.put("productClass2", productClass2);
        }
        if(StringUtil.isNotNullStr(productClass3)){
            param.put("productClass3", productClass3);
        }
        //一级策略处理
        if(StringUtil.isNotNullStr(bqxlmc)){
            dealBqxlmcNoQuot(bqxlmc, param);
        }
        try{
            // 分批导出，避免内存溢出
            result = exportPubFundNetIncreaseTradeDtlListBatch(response, param);
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }

    /**
	 * 预约销售统计报表
	 * 
	 * @param response
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public void exportSalesVolumeReportList(HttpServletResponse response,Map<String,Object> map) throws Exception {
		try {
			// 设置导出参数和标题
			String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
			// 清空输出流
			response.reset();
			response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
			ServletOutputStream os = response.getOutputStream();
			// 建立excel文件
			WritableWorkbook book = Workbook.createWorkbook(os);
			WritableSheet sheet = book.createSheet("营销季报表", 1);
			List<SalesVolume> srList = (List<SalesVolume>) map.get("rows");
			Label label = null ; 
			String[] COLUMN = {"部门", "投顾", "投顾客户号","客户姓名", "首次打款时间", "高端打款金额","高端折标销量","创新折标销量"};
			for (int i = 0; i < COLUMN.length; i++) {
				label = new Label(i, 0, COLUMN[i]);
				sheet.addCell(label);
			}

			if (srList != null) {
				int x = 1;
				for (int i = 0; i < srList.size(); i++) {
                    SalesVolume summary = srList.get(i);
					addSalesVolumeReportRow(x++, summary, sheet);
				}
			}
			book.write();
			// 主体内容生成结束
			book.write(); // 写入文件
			if(book!=null){
				book.close();
			}
			os.close(); // 关闭流
			System.out.println("报表导出成功！");
		} catch (Exception ex) {
			ex.printStackTrace();
			System.out.println("报表导出异常！");
		}

	}

    /**
     * 净申购 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportNetAppIncreaseReportList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("净申购报表", 1);
            List<GdTurnoverNew> srList = (List<GdTurnoverNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心", "区域", "分公司","申购量(万)", "赎回量(万)", "净申购量(万)","净申购比"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    GdTurnoverNew summary = srList.get(i);
                    addNetAppIncreaseReportRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }


    /**
     * 净申购 投顾 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportCustConscodeNetIncreaseList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("净申购投顾报表", 1);
            List<GdTurnoverNew> srList = (List<GdTurnoverNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心", "区域", "分公司","投顾","申购量(万)", "赎回量(万)", "净申购量(万)","净申购比"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    GdTurnoverNew summary = srList.get(i);
                    addConscodeNetAppIncreaseReportRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    /**
     * 公募 净申购  导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportPubFundNetIncreaseList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("公募净申购报表", 1);
            List<CustTrade> srList = (List<CustTrade>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心_交易", "区域_交易", "分公司_交易","投顾_交易","申购量(元)", "赎回量(元)", "净申购量(元)"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    CustTrade summary = srList.get(i);
                    addPubFundNetAppIncreaseReportRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    /**
     * 公募 转托管 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportPubFundTransferList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("公募转托管报表", 1);
            List<CustTradeNew> srList = (List<CustTradeNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心_交易", "区域_交易", "分公司_交易","投顾_交易","员工编码","客户数", "总金额(元)", "客均金额(元)"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    CustTradeNew summary = srList.get(i);
                    addPubFundTransferReportRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    /**
     * 公募 转托管 明细 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportPubFundTransferDtl.do")
    public String exportPubFundTransferDtl(HttpServletRequest request,
                                               HttpServletResponse response) {
        String result = "";

        String appBeginDt = request.getParameter("appBeginDt");
        String appEndDt = request.getParameter("appEndDt");
        String orgCode = request.getParameter("outletcode");
        String conscode = request.getParameter("conscode");
        String orderStatus = request.getParameter("orderStatus");
        String custname = request.getParameter("custname");
        String ackBeginDt = request.getParameter("ackBeginDt");
        String ackEndDt = request.getParameter("ackEndDt");
        String fundglr = request.getParameter("fundglr");
        String isconsstatus = request.getParameter("isconsstatus");
        String conscustNo = request.getParameter("conscustNo");
        String tagentNo = request.getParameter("tagentNo");

        Map<String, String> param = new HashMap<String, String>();

        if(orgCode.startsWith("other")){
            param.put("othertearm", orgCode.replaceFirst("other", ""));
        }else{
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
                param.put("teamcode", orgCode);
            }else{
                if(!"0".equals(orgCode)){
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    param.put("outletcodes", Util.getSqlInStr(suborgs));
                }
            }
        }
        param.put("appBeginDt", appBeginDt);

        if(StringUtil.isNotNullStr(appEndDt)){
            param.put("appEndDt", appEndDt);
        }
        if(StringUtil.isNotNullStr(conscode)){
            param.put("conscode", conscode);
        }
        if(StringUtil.isNotNullStr(orderStatus)){
            param.put("orderStatus", orderStatus);
        }
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }
        if(StringUtil.isNotNullStr(ackBeginDt)){
            param.put("ackBeginDt", ackBeginDt);
        }
        if(StringUtil.isNotNullStr(ackEndDt)){
            param.put("ackEndDt", ackEndDt);
        }
        if(StringUtil.isNotNullStr(fundglr)){
            param.put("fundglr", fundglr);
        }
        if(StringUtil.isNotNullStr(isconsstatus)){
            param.put("isconsstatus", isconsstatus);
        }
        if(StringUtil.isNotNullStr(conscustNo)){
            param.put("conscustNo", conscustNo);
        }
        if(StringUtil.isNotNullStr(tagentNo)){
            param.put("tagentNo", tagentNo);
        }
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取公募转托管
        List<CustTradeNew> resultList = salesVolumeNewService.listPubFundTransferReportDtlReport(param);
        resultMap.put("rows", resultList);
        resultMap.put("downName", "公募转托管明细报表.xls");
        try{
            exportPubFundTransferDtlList(response, resultMap);
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }

    /**
     * 净申购 投顾 明细 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportCustConscodeNetIncreaseDtlList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("净申购投顾明细报表", 1);
            List<GdTurnoverNew> srList = (List<GdTurnoverNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"投顾客户号","客户姓名","客户状态","中心", "区域", "分公司","投顾","申购量(万)", "赎回量(万)", "净申购量(万)"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    GdTurnoverNew summary = srList.get(i);
                    addCustConscodeNetIncreaseDtlListRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    /**
     * 净申购 投顾 产品明细 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportCustConscodeNetIncreaseProductDtlList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("净申购产品明细报表", 1);
            List<GdTurnoverNew> srList = (List<GdTurnoverNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心", "区域", "分公司","投顾","产品代码","产品名称","产品类型(对内)","好买产品线","管理人","申购量(万)", "赎回量(万)", "净申购量(万)", "一级策略", "是否投资海外"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    GdTurnoverNew summary = srList.get(i);
                    addCustConscodeNetIncreaseProductDtlListRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    /**
     * 净申购 投顾 交易明细 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportCustConscodeNetIncreaseTradeDtlList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("净申购交易明细报表", 1);
            List<GdTurnoverNew> srList = (List<GdTurnoverNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心", "区域", "分公司","投顾code","投顾","投顾客户号","客户姓名","客户状态","交易类型","交易日期","产品代码", "产品名称", "产品类型(对内)",
                    "好买产品线","管理人","交易量(万)","一级策略","是否投资海外","海外资产占比下限(%)","海外资产占比上限(%)","海外资产占比中值（%）","创新合同金额","创新第几年缴费"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    GdTurnoverNew summary = srList.get(i);
                    addCustConscodeNetIncreaseTradeDtlListRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }
    }


    /**
     * 公募净申购明细分批导出，避免内存溢出
     * @param response
     * @param param
     * @return
     * @throws Exception
     */
    public String exportPubFundNetIncreaseTradeDtlListBatch(HttpServletResponse response, Map<String, String> param) throws Exception {
        // 每批处理10万条数据
        final int BATCH_SIZE = 100000;
        // 最大允许导出80万条数据
        final long MAX_EXPORT_SIZE = 800000;

        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode("公募净申购明细报表.xls", DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = {"一账通号", "投顾客户号", "客户姓名","中台业务代码","交易类型","基金代码","基金管理人","基金名称","订单状态","确认日期", "确认金额", "确认份额",
                    "一级分类","二级分类","三级分类","一级策略","投顾code","投顾_交易","中心_交易","区域_交易","分公司_交易"};
            String[] beanProperty = {"hboneNo","conscustNo","conscustName","mBusiName","tradeType","fundCode","managerMan","fundName",
                    "orderStatus","ackDt","ackAmt","ackVol","productClass1","productClass2","productClass3","bqxlmc","consCode","consName","u1name",
                    "u2name","u3name"};

            List<CustTradeNew> allData = new ArrayList<>();
            int currentPage = 1;
            long totalProcessed = 0;

            // 分批查询数据
            while (true) {
                // 设置分页参数
                Map<String, String> pageParam = new HashMap<>(param);
                pageParam.put("page", String.valueOf(currentPage));
                pageParam.put("rows", String.valueOf(BATCH_SIZE));

                // 查询当前批次数据
                PageData<CustTradeNew> pageData = salesVolumeNewService.listPubFundNetAppIncreaseDtlReportByPage(pageParam);
                List<CustTradeNew> batchData = pageData.getListData();

                // 如果没有数据了，跳出循环
                if (batchData == null || batchData.isEmpty()) {
                    break;
                }

                totalProcessed += batchData.size();
                allData.addAll(batchData);

                // 检查是否超过80万条
                if (totalProcessed >= MAX_EXPORT_SIZE && batchData.size() == BATCH_SIZE) {
                    // 如果当前批次是满批次且已达到80万，说明可能还有更多数据
                    return "overflow";
                }

                // 如果当前批次数据少于批次大小，说明已经是最后一批
                if (batchData.size() < BATCH_SIZE) {
                    break;
                }

                currentPage++;
            }

            // 使用原有的ExcelWriter.writeExcelList方式导出
            ExcelWriter.writeExcelList(os, "公募净申购明细", 0, allData, columnName, beanProperty);

            logger.info("分批导出成功！总共导出 " + totalProcessed + " 条数据");
            return "success";

        } catch (Exception ex) {
            logger.error("分批导出异常！", ex);
            return "error";
        }
    }

    /**
     * 公募  净申购 明细 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportPubFundNetIncreaseTradeDtlList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            String[] columnName = {"一账通号", "投顾客户号", "客户姓名","中台业务代码","交易类型","基金代码","基金管理人","基金名称","订单状态","确认日期", "确认金额", "确认份额",
                    "一级分类","二级分类","三级分类","一级策略","投顾code","投顾_交易","中心_交易","区域_交易","分公司_交易"};
            String[] beanProperty = {"hboneNo","conscustNo","conscustName","mBusiName","tradeType","fundCode","managerMan","fundName",
                    "orderStatus","ackDt","ackAmt","ackVol","productClass1","productClass2","productClass3","bqxlmc","consCode","consName","u1name",
                    "u2name","u3name"};
            List<CustTrade> list = (List<CustTrade>) map.get("rows");
            ExcelWriter.writeExcelList(os, "公募净申购明细", 0, list, columnName, beanProperty);

        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    /**
     * 公募  转托管 明细 导出
     *
     * @param response
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void exportPubFundTransferDtlList(HttpServletResponse response,Map<String,Object> map) throws Exception {
        try {
            // 设置导出参数和标题
            String excelName = URLEncoder.encode(map.get("downName").toString(), DataConstants.CHARSET_NAME_UTF8);
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), DataConstants.CHARSET_NAME_ISO8859));
            ServletOutputStream os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            WritableSheet sheet = book.createSheet("公募转托管明细报表", 1);
            List<CustTradeNew> srList = (List<CustTradeNew>) map.get("rows");
            Label label = null ;
            String[] COLUMN = {"中心_交易", "区域_交易", "分公司_交易","投顾_交易","投顾客户号","客户姓名","产品代码","产品名称","订单状态","申请日期","申请份额","确认时间","确认份额","净值日期", "净值",
                    "交易金额","基金管理人","产品一级分类","产品二级分类","上家机构"};
            for (int i = 0; i < COLUMN.length; i++) {
                label = new Label(i, 0, COLUMN[i]);
                sheet.addCell(label);
            }

            if (srList != null) {
                int x = 1;
                for (int i = 0; i < srList.size(); i++) {
                    CustTradeNew summary = srList.get(i);
                    addPubFundTransferDtlListRow(x++, summary, sheet);
                }
            }
            book.write();
            // 主体内容生成结束
            book.write(); // 写入文件
            if(book!=null){
                book.close();
            }
            os.close(); // 关闭流
            System.out.println("报表导出成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("报表导出异常！");
        }

    }

    private void addNetAppIncreaseReportRow(int x, GdTurnoverNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
		Label label = null;
        DecimalFormat df = new DecimalFormat("0.00%");
		NumberFormat format = NumberFormat.getNumberInstance();
		format.setMinimumFractionDigits(2);
		format.setMaximumFractionDigits(2);
		NumberFormat format1 = NumberFormat.getNumberInstance();
		format1.setMinimumFractionDigits(0);
		format1.setMaximumFractionDigits(0);
		WritableCellFormat cell = new WritableCellFormat();
		cell.setAlignment(Alignment.RIGHT);
		label = new Label(0, x, info.getU1name());
		sheet.addCell(label);
		label = new Label(1, x, info.getU2name());
		sheet.addCell(label);
		label = new Label(2, x, info.getU3name());
		sheet.addCell(label);
		label = new Label(3, x, format.format(info.getIncreaseAmtRmb()),cell);
		sheet.addCell(label);
		label = new Label(4, x, format.format(info.getReduceAmtRmb()),cell);
		sheet.addCell(label);
		label = new Label(5, x, format.format(info.getNetIncreaseAmtRmb()),cell);
		sheet.addCell(label);
		label = new Label(6, x, df.format(info.getNetIncreaseRate()),cell);
		sheet.addCell(label);
	}


    private void addConscodeNetAppIncreaseReportRow(int x, GdTurnoverNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        DecimalFormat df = new DecimalFormat("0.00%");
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(1, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(2, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(3, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(4, x, format.format(info.getIncreaseAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(5, x, format.format(info.getReduceAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(6, x, format.format(info.getNetIncreaseAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(7, x, df.format(info.getNetIncreaseRate()),cell);
        sheet.addCell(label);
    }

    private void addPubFundNetAppIncreaseReportRow(int x, CustTrade info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        DecimalFormat df = new DecimalFormat("0.00%");
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(1, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(2, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(3, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(4, x, format.format(info.getIncreaseAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(5, x, format.format(info.getReduceAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(6, x, format.format(info.getNetIncreaseAmtRmb()),cell);
        sheet.addCell(label);
    }

    private void addPubFundTransferReportRow(int x, CustTradeNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        DecimalFormat df = new DecimalFormat("0.00%");
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(1, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(2, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(3, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(4, x, info.getConsCode());
        sheet.addCell(label);
        label = new Label(5, x, format1.format(info.getCustNum()),cell);
        sheet.addCell(label);
        label = new Label(6, x, format.format(info.getAppAmt()),cell);
        sheet.addCell(label);
        label = new Label(7, x, format.format(info.getAvgAmt()),cell);
        sheet.addCell(label);
    }

    private void addCustConscodeNetIncreaseDtlListRow(int x, GdTurnoverNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getConscustNo());
        sheet.addCell(label);
        label = new Label(1, x, info.getConscustName());
        sheet.addCell(label);
        label = new Label(2, x, info.getCustState());
        sheet.addCell(label);
        label = new Label(3, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(4, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(5, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(6, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(7, x, format.format(info.getIncreaseAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(8, x, format.format(info.getReduceAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(9, x, format.format(info.getNetIncreaseAmtRmb()),cell);
        sheet.addCell(label);

    }

    private void addCustConscodeNetIncreaseProductDtlListRow(int x, GdTurnoverNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(1, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(2, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(3, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(4, x, info.getFundCode());
        sheet.addCell(label);
        label = new Label(5, x, info.getFundName());
        sheet.addCell(label);
        label = new Label(6, x, info.getFundtypeInner());
        sheet.addCell(label);
        label = new Label(7, x, info.getHbtype());
        sheet.addCell(label);
        label = new Label(8, x, info.getManagerMan());
        sheet.addCell(label);
        label = new Label(9, x, format.format(info.getIncreaseAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(10, x, format.format(info.getReduceAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(11, x, format.format(info.getNetIncreaseAmtRmb()),cell);
        sheet.addCell(label);
        label = new Label(12, x, info.getBqxlmc(),cell);
        sheet.addCell(label);
        label = new Label(13, x, info.getSftzhw(),cell);
        sheet.addCell(label);

    }

    private void addCustConscodeNetIncreaseTradeDtlListRow(int x, GdTurnoverNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(1, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(2, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(3, x, info.getConsCode());
        sheet.addCell(label);
        label = new Label(4, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(5, x, info.getConscustNo());
        sheet.addCell(label);
        label = new Label(6, x, info.getConscustName());
        sheet.addCell(label);
        label = new Label(7, x, info.getCustState());
        sheet.addCell(label);
        label = new Label(8, x, info.getTradeType());
        sheet.addCell(label);
        label = new Label(9, x, info.getTradeDt());
        sheet.addCell(label);
        label = new Label(10, x, info.getFundCode());
        sheet.addCell(label);
        label = new Label(11, x, info.getFundName());
        sheet.addCell(label);
        label = new Label(12, x, info.getFundtypeInner());
        sheet.addCell(label);
        label = new Label(13, x, info.getHbtype());
        sheet.addCell(label);
        label = new Label(14, x, info.getManagerMan());
        sheet.addCell(label);
        label = new Label(15, x, format.format(info.getTradeAmt()),cell);
        sheet.addCell(label);
        label = new Label(16, x, info.getBqxlmc());
        sheet.addCell(label);
        label = new Label(17, x, info.getSftzhw());
        sheet.addCell(label);
        label = new Label(18, x, format1.format(info.getHwzczbxx()),cell);
        sheet.addCell(label);
        label = new Label(19, x, format1.format(info.getHwzczbsx()),cell);
        sheet.addCell(label);
        label = new Label(20, x, format1.format(info.getHwzczb()),cell);
        sheet.addCell(label);
        label = new Label(21, x, format.format(info.getBxContractAmt()),cell);
        sheet.addCell(label);
        label = new Label(22, x, format.format(info.getYearNo()),cell);
        sheet.addCell(label);

    }

    private void addPubFundNetIncreaseTradeDtlListRow(int x, CustTrade info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getHboneNo());
        sheet.addCell(label);
        label = new Label(1, x, info.getConscustNo());
        sheet.addCell(label);
        label = new Label(2, x, info.getConscustName());
        sheet.addCell(label);
        label = new Label(3, x, info.getmBusiName());
        sheet.addCell(label);
        label = new Label(4, x, info.getTradeType());
        sheet.addCell(label);
        label = new Label(5, x, info.getFundCode());
        sheet.addCell(label);
        label = new Label(6, x, info.getManagerMan());
        sheet.addCell(label);
        label = new Label(7, x, info.getFundName());
        sheet.addCell(label);
        label = new Label(8, x, info.getOrderStatus());
        sheet.addCell(label);
        label = new Label(9, x, info.getAckDt());
        sheet.addCell(label);
        label = new Label(10, x, format.format(info.getAckAmt()),cell);
        sheet.addCell(label);
        label = new Label(11, x, format.format(info.getAckVol()),cell);
        sheet.addCell(label);
        label = new Label(12, x, info.getProductClass1());
        sheet.addCell(label);
        label = new Label(13, x, info.getProductClass2());
        sheet.addCell(label);
        label = new Label(14, x, info.getProductClass3());
        sheet.addCell(label);
        label = new Label(15, x, info.getBqxlmc());
        sheet.addCell(label);
        label = new Label(16, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(17, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(18, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(19, x, info.getU3name());
        sheet.addCell(label);

    }

    private void addPubFundTransferDtlListRow(int x, CustTradeNew info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getU1name());
        sheet.addCell(label);
        label = new Label(1, x, info.getU2name());
        sheet.addCell(label);
        label = new Label(2, x, info.getU3name());
        sheet.addCell(label);
        label = new Label(3, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(4, x, info.getConscustNo());
        sheet.addCell(label);
        label = new Label(5, x, info.getConscustName());
        sheet.addCell(label);
        label = new Label(6, x, info.getFundCode());
        sheet.addCell(label);
        label = new Label(7, x, info.getFundName());
        sheet.addCell(label);
        label = new Label(8, x, info.getOrderStatus());
        sheet.addCell(label);
        label = new Label(9, x, info.getAppDt());
        sheet.addCell(label);
        label = new Label(10, x, format.format(info.getAppVol()),cell);
        sheet.addCell(label);
        label = new Label(11, x, info.getAckDt());
        sheet.addCell(label);
        label = new Label(12, x, format.format(info.getAckVol()),cell);
        sheet.addCell(label);
        label = new Label(13, x, info.getNavDt());
        sheet.addCell(label);
        label = new Label(14, x, format.format(info.getNav()),cell);
        sheet.addCell(label);
        label = new Label(15, x, format.format(info.getAppAmt()),cell);
        sheet.addCell(label);
        label = new Label(16, x, info.getManagerMan());
        sheet.addCell(label);
        label = new Label(17, x, info.getProductClass1());
        sheet.addCell(label);
        label = new Label(18, x, info.getProductClass2());
        sheet.addCell(label);
        label = new Label(19, x, info.getAgentName());
        sheet.addCell(label);

    }


    private void addSalesVolumeReportRow(int x, SalesVolume info, WritableSheet sheet) throws RowsExceededException, WriteException {
        Label label = null;
        NumberFormat format = NumberFormat.getNumberInstance();
        format.setMinimumFractionDigits(2);
        format.setMaximumFractionDigits(2);
        NumberFormat format1 = NumberFormat.getNumberInstance();
        format1.setMinimumFractionDigits(0);
        format1.setMaximumFractionDigits(0);
        WritableCellFormat cell = new WritableCellFormat();
        cell.setAlignment(Alignment.RIGHT);
        label = new Label(0, x, info.getOrgName());
        sheet.addCell(label);
        label = new Label(1, x, info.getConsName());
        sheet.addCell(label);
        label = new Label(2, x, info.getConscustNo());
        sheet.addCell(label);
        label = new Label(3, x, info.getConscustName());
        sheet.addCell(label);
        label = new Label(4, x, info.getFirstPayDt());
        sheet.addCell(label);

        label = new Label(5, x, format.format(info.getPayRmb()),cell);
        sheet.addCell(label);
        label = new Label(6, x, format.format(info.getPayDiscountRmb()),cell);
        sheet.addCell(label);
        label = new Label(7, x, format.format(info.getInnovativeDiscountRmb()),cell);
        sheet.addCell(label);
    }

    /**
     *  Bqxlmc字段处理   字符串转SqlInStr
     * <AUTHOR>
     * @Date  2023/8/22 14:37
     */
    private void dealBqxlmc(String bqxlmc, Map<String, String> param) {
        if(!com.howbuy.common.util.StringUtil.isEmpty(bqxlmc)){
            String bqxlmc2=  bqxlmc.substring(1);
            String bqxlmc3= bqxlmc2.substring(0,bqxlmc2.length()-1);
            String[] split = bqxlmc3.split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
    }

    /**
     * @description Bqxlmc字段处理   字符串转SqlInStr 不带多余的引号
     * @param bqxlmc	要处理的字符串
     * @param param	设置的参数
     * @return void
     * @author: jianjian.yang
     * @date: 2024/3/13 15:48
     * @since JDK 1.8
     */
    private void dealBqxlmcNoQuot(String bqxlmc, Map<String, String> param) {
        if(!com.howbuy.common.util.StringUtil.isEmpty(bqxlmc)){
            String[] split = bqxlmc.split(",");
            List<String> bqxlmcStrList = Arrays.asList(split);
            String sql_bqxlmc=Util.getSqlInStr(bqxlmcStrList);
            if(("''").equals(sql_bqxlmc)){
                sql_bqxlmc="";
            }
            param.put("bqxlmc",sql_bqxlmc);
        }
    }



}
