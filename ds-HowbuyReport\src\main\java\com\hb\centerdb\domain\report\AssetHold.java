package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类AssetHold.java
 * <AUTHOR>
 * @version 1.0
 * @created 20170726
 */
public class AssetHold implements Serializable {

	private static final long serialVersionUID = 1L;

	private String orgName;
    private String u2name;  //区域
	private String consName;
	private Double xbalRsTotal1; // 总存量客户人数及市值
	private Double sumMktTotal1;
	private Double onlyXbalRsTotal;
	private Double onlySumMktTotal;
	private Double xbalRs0;// 现金管理工具存量客户人数及市值
	private Double xbalRs0Rate;
	private Double sumMkt0;
	private Double sumMkt0Rate;
	private Double onlyXbalRs0;
	private Double onlyXbalRs0Rate;
	private Double onlySumMkt0;
	private Double onlySumMkt0Rate;
	private Double xbalRs1; // 债券存量客户人数及市值
	private Double xbalRs1Rate;
	private Double sumMkt1;
	private Double sumMkt1Rate;
	private Double onlyXbalRs1;
	private Double onlyXbalRs1Rate;
	private Double onlySumMkt1;
	private Double onlySumMkt1Rate;
	private Double xbalRs2; // 固定收益存量客户人数及市值
	private Double xbalRs2Rate;
	private Double sumMkt2;
	private Double sumMkt2Rate;
	private Double onlyXbalRs2;
	private Double onlyXbalRs2Rate;
	private Double onlySumMkt2;
	private Double onlySumMkt2Rate;
	private Double xbalRs3; // 对冲存量客户人数及市值
	private Double xbalRs3Rate;
	private Double sumMkt3;
	private Double sumMkt3Rate;
	private Double onlyXbalRs3;
	private Double onlyXbalRs3Rate;
	private Double onlySumMkt3;
	private Double onlySumMkt3Rate;
	private Double xbalRsCta; // CTA存量客户人数及市值
	private Double xbalRsCtaRate;
	private Double sumMktCta;
	private Double sumMktCtaRate;
	private Double onlySumMktCta;
	private Double onlySumMktCtaRate;
	private Double onlyXbalRsCta;
	private Double onlyXbalRsCtaRate;
	private Double xbalRs4; // 股票存量客户人数及市值
	private Double xbalRs4Rate;
	private Double sumMkt4;
	private Double sumMkt4Rate;
	private Double onlyXbalRs4;
	private Double onlyXbalRs4Rate;
	private Double onlySumMkt4;
	private Double onlySumMkt4Rate;
	private Double xbalRs5; // PEVC存量客户人数及市值
	private Double xbalRs5Rate;
	private Double sumMkt5;
	private Double sumMkt5Rate;
	private Double onlyXbalRs5;
	private Double onlyXbalRs5Rate;
	private Double onlySumMkt5;
	private Double onlySumMkt5Rate;
	private Double xbalRs6; // 房地产基金存量客户人数及市值
	private Double xbalRs6Rate;
	private Double sumMkt6;
	private Double sumMkt6Rate;
	private Double onlyXbalRs6;
	private Double onlyXbalRs6Rate;
	private Double onlySumMkt6;
	private Double onlySumMkt6Rate;
	private Double xbalRsHw; // 海外存量客户人数及市值
	private Double xbalRsHwRate;
	private Double sumMktHw;
	private Double sumMktHwRate;
	private Double onlyXbalRsHw;
	private Double onlyXbalRsHwRate;
	private Double onlySumMktHw;
	private Double onlySumMktHwRate;
	private Double xbalRsDz; // 定增存量客户人数及市值
	private Double xbalRsDzRate;
	private Double sumMktDz;
	private Double sumMktDzRate;
	private Double onlyXbalRsDz;
	private Double onlyXbalRsDzRate;
	private Double onlySumMktDz;
	private Double onlySumMktDzRate;
	private Double xbalRs7; // 其他存量客户人数及市值
	private Double xbalRs7Rate;
	private Double sumMkt7;
	private Double sumMkt7Rate;
	private Double onlyXbalRs7;
	private Double onlyXbalRs7Rate;
	private Double onlySumMkt7;
	private Double onlySumMkt7Rate;

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public Double getXbalRsTotal1() {
		return xbalRsTotal1;
	}

	public void setXbalRsTotal1(Double xbalRsTotal1) {
		this.xbalRsTotal1 = xbalRsTotal1;
	}

	public Double getSumMktTotal1() {
		return sumMktTotal1;
	}

	public void setSumMktTotal1(Double sumMktTotal1) {
		this.sumMktTotal1 = sumMktTotal1;
	}

	public Double getOnlyXbalRsTotal() {
		return onlyXbalRsTotal;
	}

	public void setOnlyXbalRsTotal(Double onlyXbalRsTotal) {
		this.onlyXbalRsTotal = onlyXbalRsTotal;
	}

	public Double getOnlySumMktTotal() {
		return onlySumMktTotal;
	}

	public void setOnlySumMktTotal(Double onlySumMktTotal) {
		this.onlySumMktTotal = onlySumMktTotal;
	}

	public Double getXbalRs0() {
		return xbalRs0;
	}

	public void setXbalRs0(Double xbalRs0) {
		this.xbalRs0 = xbalRs0;
	}

	public Double getXbalRs0Rate() {
		return xbalRs0Rate;
	}

	public void setXbalRs0Rate(Double xbalRs0Rate) {
		this.xbalRs0Rate = xbalRs0Rate;
	}

	public Double getSumMkt0() {
		return sumMkt0;
	}

	public void setSumMkt0(Double sumMkt0) {
		this.sumMkt0 = sumMkt0;
	}

	public Double getSumMkt0Rate() {
		return sumMkt0Rate;
	}

	public void setSumMkt0Rate(Double sumMkt0Rate) {
		this.sumMkt0Rate = sumMkt0Rate;
	}

	public Double getOnlyXbalRs0() {
		return onlyXbalRs0;
	}

	public void setOnlyXbalRs0(Double onlyXbalRs0) {
		this.onlyXbalRs0 = onlyXbalRs0;
	}

	public Double getOnlyXbalRs0Rate() {
		return onlyXbalRs0Rate;
	}

	public void setOnlyXbalRs0Rate(Double onlyXbalRs0Rate) {
		this.onlyXbalRs0Rate = onlyXbalRs0Rate;
	}

	public Double getOnlySumMkt0() {
		return onlySumMkt0;
	}

	public void setOnlySumMkt0(Double onlySumMkt0) {
		this.onlySumMkt0 = onlySumMkt0;
	}

	public Double getOnlySumMkt0Rate() {
		return onlySumMkt0Rate;
	}

	public void setOnlySumMkt0Rate(Double onlySumMkt0Rate) {
		this.onlySumMkt0Rate = onlySumMkt0Rate;
	}

	public Double getXbalRs1() {
		return xbalRs1;
	}

	public void setXbalRs1(Double xbalRs1) {
		this.xbalRs1 = xbalRs1;
	}

	public Double getXbalRs1Rate() {
		return xbalRs1Rate;
	}

	public void setXbalRs1Rate(Double xbalRs1Rate) {
		this.xbalRs1Rate = xbalRs1Rate;
	}

	public Double getSumMkt1() {
		return sumMkt1;
	}

	public void setSumMkt1(Double sumMkt1) {
		this.sumMkt1 = sumMkt1;
	}

	public Double getSumMkt1Rate() {
		return sumMkt1Rate;
	}

	public void setSumMkt1Rate(Double sumMkt1Rate) {
		this.sumMkt1Rate = sumMkt1Rate;
	}

	public Double getOnlyXbalRs1() {
		return onlyXbalRs1;
	}

	public void setOnlyXbalRs1(Double onlyXbalRs1) {
		this.onlyXbalRs1 = onlyXbalRs1;
	}

	public Double getOnlyXbalRs1Rate() {
		return onlyXbalRs1Rate;
	}

	public void setOnlyXbalRs1Rate(Double onlyXbalRs1Rate) {
		this.onlyXbalRs1Rate = onlyXbalRs1Rate;
	}

	public Double getOnlySumMkt1() {
		return onlySumMkt1;
	}

	public void setOnlySumMkt1(Double onlySumMkt1) {
		this.onlySumMkt1 = onlySumMkt1;
	}

	public Double getOnlySumMkt1Rate() {
		return onlySumMkt1Rate;
	}

	public void setOnlySumMkt1Rate(Double onlySumMkt1Rate) {
		this.onlySumMkt1Rate = onlySumMkt1Rate;
	}

	public Double getXbalRs2() {
		return xbalRs2;
	}

	public void setXbalRs2(Double xbalRs2) {
		this.xbalRs2 = xbalRs2;
	}

	public Double getXbalRs2Rate() {
		return xbalRs2Rate;
	}

	public void setXbalRs2Rate(Double xbalRs2Rate) {
		this.xbalRs2Rate = xbalRs2Rate;
	}

	public Double getSumMkt2() {
		return sumMkt2;
	}

	public void setSumMkt2(Double sumMkt2) {
		this.sumMkt2 = sumMkt2;
	}

	public Double getSumMkt2Rate() {
		return sumMkt2Rate;
	}

	public void setSumMkt2Rate(Double sumMkt2Rate) {
		this.sumMkt2Rate = sumMkt2Rate;
	}

	public Double getOnlyXbalRs2() {
		return onlyXbalRs2;
	}

	public void setOnlyXbalRs2(Double onlyXbalRs2) {
		this.onlyXbalRs2 = onlyXbalRs2;
	}

	public Double getOnlyXbalRs2Rate() {
		return onlyXbalRs2Rate;
	}

	public void setOnlyXbalRs2Rate(Double onlyXbalRs2Rate) {
		this.onlyXbalRs2Rate = onlyXbalRs2Rate;
	}

	public Double getOnlySumMkt2() {
		return onlySumMkt2;
	}

	public void setOnlySumMkt2(Double onlySumMkt2) {
		this.onlySumMkt2 = onlySumMkt2;
	}

	public Double getOnlySumMkt2Rate() {
		return onlySumMkt2Rate;
	}

	public void setOnlySumMkt2Rate(Double onlySumMkt2Rate) {
		this.onlySumMkt2Rate = onlySumMkt2Rate;
	}

	public Double getXbalRs3() {
		return xbalRs3;
	}

	public void setXbalRs3(Double xbalRs3) {
		this.xbalRs3 = xbalRs3;
	}

	public Double getXbalRs3Rate() {
		return xbalRs3Rate;
	}

	public void setXbalRs3Rate(Double xbalRs3Rate) {
		this.xbalRs3Rate = xbalRs3Rate;
	}

	public Double getSumMkt3() {
		return sumMkt3;
	}

	public void setSumMkt3(Double sumMkt3) {
		this.sumMkt3 = sumMkt3;
	}

	public Double getSumMkt3Rate() {
		return sumMkt3Rate;
	}

	public void setSumMkt3Rate(Double sumMkt3Rate) {
		this.sumMkt3Rate = sumMkt3Rate;
	}

	public Double getOnlyXbalRs3() {
		return onlyXbalRs3;
	}

	public void setOnlyXbalRs3(Double onlyXbalRs3) {
		this.onlyXbalRs3 = onlyXbalRs3;
	}

	public Double getOnlyXbalRs3Rate() {
		return onlyXbalRs3Rate;
	}

	public void setOnlyXbalRs3Rate(Double onlyXbalRs3Rate) {
		this.onlyXbalRs3Rate = onlyXbalRs3Rate;
	}

	public Double getOnlySumMkt3() {
		return onlySumMkt3;
	}

	public void setOnlySumMkt3(Double onlySumMkt3) {
		this.onlySumMkt3 = onlySumMkt3;
	}

	public Double getOnlySumMkt3Rate() {
		return onlySumMkt3Rate;
	}

	public void setOnlySumMkt3Rate(Double onlySumMkt3Rate) {
		this.onlySumMkt3Rate = onlySumMkt3Rate;
	}

	public Double getXbalRsCta() {
		return xbalRsCta;
	}

	public void setXbalRsCta(Double xbalRsCta) {
		this.xbalRsCta = xbalRsCta;
	}

	public Double getXbalRsCtaRate() {
		return xbalRsCtaRate;
	}

	public void setXbalRsCtaRate(Double xbalRsCtaRate) {
		this.xbalRsCtaRate = xbalRsCtaRate;
	}

	public Double getSumMktCta() {
		return sumMktCta;
	}

	public void setSumMktCta(Double sumMktCta) {
		this.sumMktCta = sumMktCta;
	}

	public Double getSumMktCtaRate() {
		return sumMktCtaRate;
	}

	public void setSumMktCtaRate(Double sumMktCtaRate) {
		this.sumMktCtaRate = sumMktCtaRate;
	}

	public Double getOnlySumMktCta() {
		return onlySumMktCta;
	}

	public void setOnlySumMktCta(Double onlySumMktCta) {
		this.onlySumMktCta = onlySumMktCta;
	}

	public Double getOnlySumMktCtaRate() {
		return onlySumMktCtaRate;
	}

	public void setOnlySumMktCtaRate(Double onlySumMktCtaRate) {
		this.onlySumMktCtaRate = onlySumMktCtaRate;
	}

	public Double getOnlyXbalRsCta() {
		return onlyXbalRsCta;
	}

	public void setOnlyXbalRsCta(Double onlyXbalRsCta) {
		this.onlyXbalRsCta = onlyXbalRsCta;
	}

	public Double getOnlyXbalRsCtaRate() {
		return onlyXbalRsCtaRate;
	}

	public void setOnlyXbalRsCtaRate(Double onlyXbalRsCtaRate) {
		this.onlyXbalRsCtaRate = onlyXbalRsCtaRate;
	}

	public Double getXbalRs4() {
		return xbalRs4;
	}

	public void setXbalRs4(Double xbalRs4) {
		this.xbalRs4 = xbalRs4;
	}

	public Double getXbalRs4Rate() {
		return xbalRs4Rate;
	}

	public void setXbalRs4Rate(Double xbalRs4Rate) {
		this.xbalRs4Rate = xbalRs4Rate;
	}

	public Double getSumMkt4() {
		return sumMkt4;
	}

	public void setSumMkt4(Double sumMkt4) {
		this.sumMkt4 = sumMkt4;
	}

	public Double getSumMkt4Rate() {
		return sumMkt4Rate;
	}

	public void setSumMkt4Rate(Double sumMkt4Rate) {
		this.sumMkt4Rate = sumMkt4Rate;
	}

	public Double getOnlyXbalRs4() {
		return onlyXbalRs4;
	}

	public void setOnlyXbalRs4(Double onlyXbalRs4) {
		this.onlyXbalRs4 = onlyXbalRs4;
	}

	public Double getOnlyXbalRs4Rate() {
		return onlyXbalRs4Rate;
	}

	public void setOnlyXbalRs4Rate(Double onlyXbalRs4Rate) {
		this.onlyXbalRs4Rate = onlyXbalRs4Rate;
	}

	public Double getOnlySumMkt4() {
		return onlySumMkt4;
	}

	public void setOnlySumMkt4(Double onlySumMkt4) {
		this.onlySumMkt4 = onlySumMkt4;
	}

	public Double getOnlySumMkt4Rate() {
		return onlySumMkt4Rate;
	}

	public void setOnlySumMkt4Rate(Double onlySumMkt4Rate) {
		this.onlySumMkt4Rate = onlySumMkt4Rate;
	}

	public Double getXbalRs5() {
		return xbalRs5;
	}

	public void setXbalRs5(Double xbalRs5) {
		this.xbalRs5 = xbalRs5;
	}

	public Double getXbalRs5Rate() {
		return xbalRs5Rate;
	}

	public void setXbalRs5Rate(Double xbalRs5Rate) {
		this.xbalRs5Rate = xbalRs5Rate;
	}

	public Double getSumMkt5() {
		return sumMkt5;
	}

	public void setSumMkt5(Double sumMkt5) {
		this.sumMkt5 = sumMkt5;
	}

	public Double getSumMkt5Rate() {
		return sumMkt5Rate;
	}

	public void setSumMkt5Rate(Double sumMkt5Rate) {
		this.sumMkt5Rate = sumMkt5Rate;
	}

	public Double getOnlyXbalRs5() {
		return onlyXbalRs5;
	}

	public void setOnlyXbalRs5(Double onlyXbalRs5) {
		this.onlyXbalRs5 = onlyXbalRs5;
	}

	public Double getOnlyXbalRs5Rate() {
		return onlyXbalRs5Rate;
	}

	public void setOnlyXbalRs5Rate(Double onlyXbalRs5Rate) {
		this.onlyXbalRs5Rate = onlyXbalRs5Rate;
	}

	public Double getOnlySumMkt5() {
		return onlySumMkt5;
	}

	public void setOnlySumMkt5(Double onlySumMkt5) {
		this.onlySumMkt5 = onlySumMkt5;
	}

	public Double getOnlySumMkt5Rate() {
		return onlySumMkt5Rate;
	}

	public void setOnlySumMkt5Rate(Double onlySumMkt5Rate) {
		this.onlySumMkt5Rate = onlySumMkt5Rate;
	}

	public Double getXbalRs6() {
		return xbalRs6;
	}

	public void setXbalRs6(Double xbalRs6) {
		this.xbalRs6 = xbalRs6;
	}

	public Double getXbalRs6Rate() {
		return xbalRs6Rate;
	}

	public void setXbalRs6Rate(Double xbalRs6Rate) {
		this.xbalRs6Rate = xbalRs6Rate;
	}

	public Double getSumMkt6() {
		return sumMkt6;
	}

	public void setSumMkt6(Double sumMkt6) {
		this.sumMkt6 = sumMkt6;
	}

	public Double getSumMkt6Rate() {
		return sumMkt6Rate;
	}

	public void setSumMkt6Rate(Double sumMkt6Rate) {
		this.sumMkt6Rate = sumMkt6Rate;
	}

	public Double getOnlyXbalRs6() {
		return onlyXbalRs6;
	}

	public void setOnlyXbalRs6(Double onlyXbalRs6) {
		this.onlyXbalRs6 = onlyXbalRs6;
	}

	public Double getOnlyXbalRs6Rate() {
		return onlyXbalRs6Rate;
	}

	public void setOnlyXbalRs6Rate(Double onlyXbalRs6Rate) {
		this.onlyXbalRs6Rate = onlyXbalRs6Rate;
	}

	public Double getOnlySumMkt6() {
		return onlySumMkt6;
	}

	public void setOnlySumMkt6(Double onlySumMkt6) {
		this.onlySumMkt6 = onlySumMkt6;
	}

	public Double getOnlySumMkt6Rate() {
		return onlySumMkt6Rate;
	}

	public void setOnlySumMkt6Rate(Double onlySumMkt6Rate) {
		this.onlySumMkt6Rate = onlySumMkt6Rate;
	}

	public Double getXbalRsHw() {
		return xbalRsHw;
	}

	public void setXbalRsHw(Double xbalRsHw) {
		this.xbalRsHw = xbalRsHw;
	}

	public Double getXbalRsHwRate() {
		return xbalRsHwRate;
	}

	public void setXbalRsHwRate(Double xbalRsHwRate) {
		this.xbalRsHwRate = xbalRsHwRate;
	}

	public Double getSumMktHw() {
		return sumMktHw;
	}

	public void setSumMktHw(Double sumMktHw) {
		this.sumMktHw = sumMktHw;
	}

	public Double getSumMktHwRate() {
		return sumMktHwRate;
	}

	public void setSumMktHwRate(Double sumMktHwRate) {
		this.sumMktHwRate = sumMktHwRate;
	}

	public Double getOnlyXbalRsHw() {
		return onlyXbalRsHw;
	}

	public void setOnlyXbalRsHw(Double onlyXbalRsHw) {
		this.onlyXbalRsHw = onlyXbalRsHw;
	}

	public Double getOnlyXbalRsHwRate() {
		return onlyXbalRsHwRate;
	}

	public void setOnlyXbalRsHwRate(Double onlyXbalRsHwRate) {
		this.onlyXbalRsHwRate = onlyXbalRsHwRate;
	}

	public Double getOnlySumMktHw() {
		return onlySumMktHw;
	}

	public void setOnlySumMktHw(Double onlySumMktHw) {
		this.onlySumMktHw = onlySumMktHw;
	}

	public Double getOnlySumMktHwRate() {
		return onlySumMktHwRate;
	}

	public void setOnlySumMktHwRate(Double onlySumMktHwRate) {
		this.onlySumMktHwRate = onlySumMktHwRate;
	}

	public Double getXbalRsDz() {
		return xbalRsDz;
	}

	public void setXbalRsDz(Double xbalRsDz) {
		this.xbalRsDz = xbalRsDz;
	}

	public Double getXbalRsDzRate() {
		return xbalRsDzRate;
	}

	public void setXbalRsDzRate(Double xbalRsDzRate) {
		this.xbalRsDzRate = xbalRsDzRate;
	}

	public Double getSumMktDz() {
		return sumMktDz;
	}

	public void setSumMktDz(Double sumMktDz) {
		this.sumMktDz = sumMktDz;
	}

	public Double getSumMktDzRate() {
		return sumMktDzRate;
	}

	public void setSumMktDzRate(Double sumMktDzRate) {
		this.sumMktDzRate = sumMktDzRate;
	}

	public Double getOnlyXbalRsDz() {
		return onlyXbalRsDz;
	}

	public void setOnlyXbalRsDz(Double onlyXbalRsDz) {
		this.onlyXbalRsDz = onlyXbalRsDz;
	}

	public Double getOnlyXbalRsDzRate() {
		return onlyXbalRsDzRate;
	}

	public void setOnlyXbalRsDzRate(Double onlyXbalRsDzRate) {
		this.onlyXbalRsDzRate = onlyXbalRsDzRate;
	}

	public Double getOnlySumMktDz() {
		return onlySumMktDz;
	}

	public void setOnlySumMktDz(Double onlySumMktDz) {
		this.onlySumMktDz = onlySumMktDz;
	}

	public Double getOnlySumMktDzRate() {
		return onlySumMktDzRate;
	}

	public void setOnlySumMktDzRate(Double onlySumMktDzRate) {
		this.onlySumMktDzRate = onlySumMktDzRate;
	}

	public Double getXbalRs7() {
		return xbalRs7;
	}

	public void setXbalRs7(Double xbalRs7) {
		this.xbalRs7 = xbalRs7;
	}

	public Double getXbalRs7Rate() {
		return xbalRs7Rate;
	}

	public void setXbalRs7Rate(Double xbalRs7Rate) {
		this.xbalRs7Rate = xbalRs7Rate;
	}

	public Double getSumMkt7() {
		return sumMkt7;
	}

	public void setSumMkt7(Double sumMkt7) {
		this.sumMkt7 = sumMkt7;
	}

	public Double getSumMkt7Rate() {
		return sumMkt7Rate;
	}

	public void setSumMkt7Rate(Double sumMkt7Rate) {
		this.sumMkt7Rate = sumMkt7Rate;
	}

	public Double getOnlyXbalRs7() {
		return onlyXbalRs7;
	}

	public void setOnlyXbalRs7(Double onlyXbalRs7) {
		this.onlyXbalRs7 = onlyXbalRs7;
	}

	public Double getOnlyXbalRs7Rate() {
		return onlyXbalRs7Rate;
	}

	public void setOnlyXbalRs7Rate(Double onlyXbalRs7Rate) {
		this.onlyXbalRs7Rate = onlyXbalRs7Rate;
	}

	public Double getOnlySumMkt7() {
		return onlySumMkt7;
	}

	public void setOnlySumMkt7(Double onlySumMkt7) {
		this.onlySumMkt7 = onlySumMkt7;
	}

	public Double getOnlySumMkt7Rate() {
		return onlySumMkt7Rate;
	}

	public void setOnlySumMkt7Rate(Double onlySumMkt7Rate) {
		this.onlySumMkt7Rate = onlySumMkt7Rate;
	}

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
