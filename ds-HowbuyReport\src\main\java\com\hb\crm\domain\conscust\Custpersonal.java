package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Custpersonal.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Custpersonal implements Serializable {

private static final long serialVersionUID = 1L;

	private String custno;
	
	private String idtype;
	
	private String idno;
	
	private String custname;
	
	private String provcode;
	
	private String citycode;
	
	private String edulevel;
	
	private String vocation;
	
	private String inclevel;
	
	private String birthday;
	
	private String gender;
	
	private String married;
	
	private String pincome;
	
	private String fincome;
	
	private String decisionflag;
	
	private String interests;
	
	private String familycondition;
	
	private String contacttime;
	
	private String contactmethod;
	
	private String sendinfoflag;
	
	private String recvemailflag;
	
	private String recvtelflag;
	
	private String recvmsgflag;
	
	private String company;
	
	private String risklevel;
	
	private String selfrisklevel;
	
	private String addr;
	
	private String postcode;
	
	private String addr2;
	
	private String postcode2;
	
	private String addr3;
	
	private String postcode3;
	
	private String mobile;
	
	private String mobile2;
	
	private String telno;
	
	private String fax;
	
	private String pagerno;
	
	private String email;
	
	private String email2;
	
	private String hometelno;
	
	private String officetelno;
	
	private String otherinvest;
	
	private String salon;
	
	private String beforeinvest;
	
	private String im;
	
	private String regdt;
	
	private String uddt;
	
	private Date stimestamp;
	
	private String ainvestamt;
	
	private String ainvestfamt;
	
	private String selfdefflag;
	
	private Integer custgrade;
	
	private String custglevel;
	
	private String logondt;
	
	private String raisefundsflag;
	
	private String visitfqcy;
	
	private String devdirection;
	
	private String subsource;
	
	private String subsourcetype;
	
	private String msn;
	
	private String dlvymode;
	
	private String buyingfreeprod;
	
	private String safequestion1;
	
	private String answer1;
	
	private String safequestion2;
	
	private String answer2;
	
	private String regSource;
	
	private Integer channelId;
	
	private String parModel;
	
	private String subModel;
	
	private String ver;
	
	private Integer productId;
	
	private String freeprod;
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getIdtype() {
		return this.idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}
	
	public String getIdno() {
		return this.idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}
	
	public String getCustname() {
		return this.custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}
	
	public String getProvcode() {
		return this.provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}
	
	public String getCitycode() {
		return this.citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}
	
	public String getEdulevel() {
		return this.edulevel;
	}

	public void setEdulevel(String edulevel) {
		this.edulevel = edulevel;
	}
	
	public String getVocation() {
		return this.vocation;
	}

	public void setVocation(String vocation) {
		this.vocation = vocation;
	}
	
	public String getInclevel() {
		return this.inclevel;
	}

	public void setInclevel(String inclevel) {
		this.inclevel = inclevel;
	}
	
	public String getBirthday() {
		return this.birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	
	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}
	
	public String getMarried() {
		return this.married;
	}

	public void setMarried(String married) {
		this.married = married;
	}
	
	public String getPincome() {
		return this.pincome;
	}

	public void setPincome(String pincome) {
		this.pincome = pincome;
	}
	
	public String getFincome() {
		return this.fincome;
	}

	public void setFincome(String fincome) {
		this.fincome = fincome;
	}
	
	public String getDecisionflag() {
		return this.decisionflag;
	}

	public void setDecisionflag(String decisionflag) {
		this.decisionflag = decisionflag;
	}
	
	public String getInterests() {
		return this.interests;
	}

	public void setInterests(String interests) {
		this.interests = interests;
	}
	
	public String getFamilycondition() {
		return this.familycondition;
	}

	public void setFamilycondition(String familycondition) {
		this.familycondition = familycondition;
	}
	
	public String getContacttime() {
		return this.contacttime;
	}

	public void setContacttime(String contacttime) {
		this.contacttime = contacttime;
	}
	
	public String getContactmethod() {
		return this.contactmethod;
	}

	public void setContactmethod(String contactmethod) {
		this.contactmethod = contactmethod;
	}
	
	public String getSendinfoflag() {
		return this.sendinfoflag;
	}

	public void setSendinfoflag(String sendinfoflag) {
		this.sendinfoflag = sendinfoflag;
	}
	
	public String getRecvemailflag() {
		return this.recvemailflag;
	}

	public void setRecvemailflag(String recvemailflag) {
		this.recvemailflag = recvemailflag;
	}
	
	public String getRecvtelflag() {
		return this.recvtelflag;
	}

	public void setRecvtelflag(String recvtelflag) {
		this.recvtelflag = recvtelflag;
	}
	
	public String getRecvmsgflag() {
		return this.recvmsgflag;
	}

	public void setRecvmsgflag(String recvmsgflag) {
		this.recvmsgflag = recvmsgflag;
	}
	
	public String getCompany() {
		return this.company;
	}

	public void setCompany(String company) {
		this.company = company;
	}
	
	public String getRisklevel() {
		return this.risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}
	
	public String getSelfrisklevel() {
		return this.selfrisklevel;
	}

	public void setSelfrisklevel(String selfrisklevel) {
		this.selfrisklevel = selfrisklevel;
	}
	
	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}
	
	public String getPostcode() {
		return this.postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}
	
	public String getAddr2() {
		return this.addr2;
	}

	public void setAddr2(String addr2) {
		this.addr2 = addr2;
	}
	
	public String getPostcode2() {
		return this.postcode2;
	}

	public void setPostcode2(String postcode2) {
		this.postcode2 = postcode2;
	}
	
	public String getAddr3() {
		return this.addr3;
	}

	public void setAddr3(String addr3) {
		this.addr3 = addr3;
	}
	
	public String getPostcode3() {
		return this.postcode3;
	}

	public void setPostcode3(String postcode3) {
		this.postcode3 = postcode3;
	}
	
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	public String getMobile2() {
		return this.mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}
	
	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}
	
	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}
	
	public String getPagerno() {
		return this.pagerno;
	}

	public void setPagerno(String pagerno) {
		this.pagerno = pagerno;
	}
	
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getEmail2() {
		return this.email2;
	}

	public void setEmail2(String email2) {
		this.email2 = email2;
	}
	
	public String getHometelno() {
		return this.hometelno;
	}

	public void setHometelno(String hometelno) {
		this.hometelno = hometelno;
	}
	
	public String getOfficetelno() {
		return this.officetelno;
	}

	public void setOfficetelno(String officetelno) {
		this.officetelno = officetelno;
	}
	
	public String getOtherinvest() {
		return this.otherinvest;
	}

	public void setOtherinvest(String otherinvest) {
		this.otherinvest = otherinvest;
	}
	
	public String getSalon() {
		return this.salon;
	}

	public void setSalon(String salon) {
		this.salon = salon;
	}
	
	public String getBeforeinvest() {
		return this.beforeinvest;
	}

	public void setBeforeinvest(String beforeinvest) {
		this.beforeinvest = beforeinvest;
	}
	
	public String getIm() {
		return this.im;
	}

	public void setIm(String im) {
		this.im = im;
	}
	
	public String getRegdt() {
		return this.regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}
	
	public String getUddt() {
		return this.uddt;
	}

	public void setUddt(String uddt) {
		this.uddt = uddt;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	
	public String getAinvestamt() {
		return this.ainvestamt;
	}

	public void setAinvestamt(String ainvestamt) {
		this.ainvestamt = ainvestamt;
	}
	
	public String getAinvestfamt() {
		return this.ainvestfamt;
	}

	public void setAinvestfamt(String ainvestfamt) {
		this.ainvestfamt = ainvestfamt;
	}
	
	public String getSelfdefflag() {
		return this.selfdefflag;
	}

	public void setSelfdefflag(String selfdefflag) {
		this.selfdefflag = selfdefflag;
	}
	
	public Integer getCustgrade() {
		return this.custgrade;
	}

	public void setCustgrade(Integer custgrade) {
		this.custgrade = custgrade;
	}
	
	public String getCustglevel() {
		return this.custglevel;
	}

	public void setCustglevel(String custglevel) {
		this.custglevel = custglevel;
	}
	
	public String getLogondt() {
		return this.logondt;
	}

	public void setLogondt(String logondt) {
		this.logondt = logondt;
	}
	
	public String getRaisefundsflag() {
		return this.raisefundsflag;
	}

	public void setRaisefundsflag(String raisefundsflag) {
		this.raisefundsflag = raisefundsflag;
	}
	
	public String getVisitfqcy() {
		return this.visitfqcy;
	}

	public void setVisitfqcy(String visitfqcy) {
		this.visitfqcy = visitfqcy;
	}
	
	public String getDevdirection() {
		return this.devdirection;
	}

	public void setDevdirection(String devdirection) {
		this.devdirection = devdirection;
	}
	
	public String getSubsource() {
		return this.subsource;
	}

	public void setSubsource(String subsource) {
		this.subsource = subsource;
	}
	
	public String getSubsourcetype() {
		return this.subsourcetype;
	}

	public void setSubsourcetype(String subsourcetype) {
		this.subsourcetype = subsourcetype;
	}
	
	public String getMsn() {
		return this.msn;
	}

	public void setMsn(String msn) {
		this.msn = msn;
	}
	
	public String getDlvymode() {
		return this.dlvymode;
	}

	public void setDlvymode(String dlvymode) {
		this.dlvymode = dlvymode;
	}
	
	public String getBuyingfreeprod() {
		return this.buyingfreeprod;
	}

	public void setBuyingfreeprod(String buyingfreeprod) {
		this.buyingfreeprod = buyingfreeprod;
	}
	
	public String getSafequestion1() {
		return this.safequestion1;
	}

	public void setSafequestion1(String safequestion1) {
		this.safequestion1 = safequestion1;
	}
	
	public String getAnswer1() {
		return this.answer1;
	}

	public void setAnswer1(String answer1) {
		this.answer1 = answer1;
	}
	
	public String getSafequestion2() {
		return this.safequestion2;
	}

	public void setSafequestion2(String safequestion2) {
		this.safequestion2 = safequestion2;
	}
	
	public String getAnswer2() {
		return this.answer2;
	}

	public void setAnswer2(String answer2) {
		this.answer2 = answer2;
	}
	
	public String getRegSource() {
		return this.regSource;
	}

	public void setRegSource(String regSource) {
		this.regSource = regSource;
	}
	
	public Integer getChannelId() {
		return this.channelId;
	}

	public void setChannelId(Integer channelId) {
		this.channelId = channelId;
	}
	
	public String getParModel() {
		return this.parModel;
	}

	public void setParModel(String parModel) {
		this.parModel = parModel;
	}
	
	public String getSubModel() {
		return this.subModel;
	}

	public void setSubModel(String subModel) {
		this.subModel = subModel;
	}
	
	public String getVer() {
		return this.ver;
	}

	public void setVer(String ver) {
		this.ver = ver;
	}
	
	public Integer getProductId() {
		return this.productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getFreeprod() {
	return freeprod;
}

public void setFreeprod(String freeprod) {
	this.freeprod = freeprod;
}
}
