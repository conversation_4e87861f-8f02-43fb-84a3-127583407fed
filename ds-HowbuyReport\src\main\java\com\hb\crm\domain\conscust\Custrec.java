package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Custrec.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Custrec implements Serializable {

private static final long serialVersionUID = 1L;

	private String appserialno;
	
	private String custno;
	
	private String custusername;
	
	private String invsttype;
	
	private String custstatus;
	
	private String custtype;
	
	private String custlevel;
	
	private String txflag;
	
	private String ipaddr;
	
	private String ipcity;
	
	private String actcode;
	
	private String brokercode;
	
	private String conscode;
	
	private String intrcustno;
	
	private String source;
	
	private String pcustid;
	
	private String knowchan;
	
	private String otherchan;
	
	private String activeflag;
	
	private String vrfycode;
	
	private Date stimestamp;
	
	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getCustusername() {
		return this.custusername;
	}

	public void setCustusername(String custusername) {
		this.custusername = custusername;
	}
	
	public String getInvsttype() {
		return this.invsttype;
	}

	public void setInvsttype(String invsttype) {
		this.invsttype = invsttype;
	}
	
	public String getCuststatus() {
		return this.custstatus;
	}

	public void setCuststatus(String custstatus) {
		this.custstatus = custstatus;
	}
	
	public String getCusttype() {
		return this.custtype;
	}

	public void setCusttype(String custtype) {
		this.custtype = custtype;
	}
	
	public String getCustlevel() {
		return this.custlevel;
	}

	public void setCustlevel(String custlevel) {
		this.custlevel = custlevel;
	}
	
	public String getTxflag() {
		return this.txflag;
	}

	public void setTxflag(String txflag) {
		this.txflag = txflag;
	}
	
	public String getIpaddr() {
		return this.ipaddr;
	}

	public void setIpaddr(String ipaddr) {
		this.ipaddr = ipaddr;
	}
	
	public String getIpcity() {
		return this.ipcity;
	}

	public void setIpcity(String ipcity) {
		this.ipcity = ipcity;
	}
	
	public String getActcode() {
		return this.actcode;
	}

	public void setActcode(String actcode) {
		this.actcode = actcode;
	}
	
	public String getBrokercode() {
		return this.brokercode;
	}

	public void setBrokercode(String brokercode) {
		this.brokercode = brokercode;
	}
	
	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	
	public String getIntrcustno() {
		return this.intrcustno;
	}

	public void setIntrcustno(String intrcustno) {
		this.intrcustno = intrcustno;
	}
	
	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}
	
	public String getPcustid() {
		return this.pcustid;
	}

	public void setPcustid(String pcustid) {
		this.pcustid = pcustid;
	}
	
	public String getKnowchan() {
		return this.knowchan;
	}

	public void setKnowchan(String knowchan) {
		this.knowchan = knowchan;
	}
	
	public String getOtherchan() {
		return this.otherchan;
	}

	public void setOtherchan(String otherchan) {
		this.otherchan = otherchan;
	}
	
	public String getActiveflag() {
		return this.activeflag;
	}

	public void setActiveflag(String activeflag) {
		this.activeflag = activeflag;
	}
	
	public String getVrfycode() {
		return this.vrfycode;
	}

	public void setVrfycode(String vrfycode) {
		this.vrfycode = vrfycode;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
