package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类CustSourceDeal.java
 * <AUTHOR>
 * @version 1.0
 * @created 20170726
 */
public class CustSourceDeal implements Serializable {

	private static final long serialVersionUID = 1L;

	private String outletcode;
	private String orgName;
    private String u2name;//区域
	private String consName;
	private Integer gszyFirstTotal=0; // 公司资源一手累计总客户数
	private Integer gszyFirstCjTotal=0;// 公司资源一手总成交客户数
	private Double gszyFirstCjRate=0.0D;// 公司资源一手成交比
	private Integer gszyCurrentTotal=0;// 公司资源一手当前总客户数
	private Integer gszyCurrentCjTotal=0;// 公司资源一手当前总成交客户数
	private Double gszyCurrentCjRate=0.0D;// 公司资源一手当前成交比
	private Integer tgkfFirstTotal=0;//投顾开发一手总客户数
	private Integer tgkfFirstCjTotal=0;//投顾开发一手总成交客户数
	private Double tgkfFirstCjRate=0.0D;//投顾开发一手成交比
	private Integer mgmFirstTotal=0;//MGM一手总客户数
	private Integer mgmFirstCjTotal=0;//MGM一手总成交客户数
	private Double mgmFirstCjRate=0.0D;//MGM一手成交比
	private Integer actFirstTotal=0; // 一致行动人一手总客户数
	private Integer actFirstCjTotal=0;//一致行动人一手总成交客户数
	private Double actFirstCjRate=0.0D;//一致行动人一手成交比
    private Integer qzSecondTotal=0; // 二手潜在总客户数
    private Integer qzSecondCjTotal=0;//二手潜在总成交客户数
    private Double qzSecondCjRate=0.0D;//二手潜在成交比
    private Integer balSecondTotal=0; // 二手存量总客户数
    private Integer balSecondCjTotal=0;//二手存量总成交客户数
    private Double balSecondCjRate=0.0D;//二手存量成交比
    private Integer balSecondTotal0=0; // 二手0存量总客户数
    private Integer balSecondCjTotal0=0;//二手0存量总成交客户数
    private Double balSecondCjRate0=0.0D;//二手0存量成交比
    private Integer innerActTotal=0; // 内部活动总客户数
    private Integer innerActCjTotal=0;//内部活动总成交客户数
    private Double innerActCjRate=0.0D;//内部活动成交比
    private Double selfCjRate=0.0D;//投顾自我开发占比

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public Integer getGszyFirstTotal() {
        return gszyFirstTotal;
    }

    public void setGszyFirstTotal(Integer gszyFirstTotal) {
        this.gszyFirstTotal = gszyFirstTotal;
    }

    public Integer getGszyFirstCjTotal() {
        return gszyFirstCjTotal;
    }

    public void setGszyFirstCjTotal(Integer gszyFirstCjTotal) {
        this.gszyFirstCjTotal = gszyFirstCjTotal;
    }


    public Integer getGszyCurrentTotal() {
        return gszyCurrentTotal;
    }

    public void setGszyCurrentTotal(Integer gszyCurrentTotal) {
        this.gszyCurrentTotal = gszyCurrentTotal;
    }

    public Integer getGszyCurrentCjTotal() {
        return gszyCurrentCjTotal;
    }

    public void setGszyCurrentCjTotal(Integer gszyCurrentCjTotal) {
        this.gszyCurrentCjTotal = gszyCurrentCjTotal;
    }


    public Integer getTgkfFirstTotal() {
        return tgkfFirstTotal;
    }

    public void setTgkfFirstTotal(Integer tgkfFirstTotal) {
        this.tgkfFirstTotal = tgkfFirstTotal;
    }

    public Integer getTgkfFirstCjTotal() {
        return tgkfFirstCjTotal;
    }

    public void setTgkfFirstCjTotal(Integer tgkfFirstCjTotal) {
        this.tgkfFirstCjTotal = tgkfFirstCjTotal;
    }



    public Integer getMgmFirstTotal() {
        return mgmFirstTotal;
    }

    public void setMgmFirstTotal(Integer mgmFirstTotal) {
        this.mgmFirstTotal = mgmFirstTotal;
    }

    public Integer getMgmFirstCjTotal() {
        return mgmFirstCjTotal;
    }

    public void setMgmFirstCjTotal(Integer mgmFirstCjTotal) {
        this.mgmFirstCjTotal = mgmFirstCjTotal;
    }


    public Integer getActFirstTotal() {
        return actFirstTotal;
    }

    public void setActFirstTotal(Integer actFirstTotal) {
        this.actFirstTotal = actFirstTotal;
    }

    public Integer getActFirstCjTotal() {
        return actFirstCjTotal;
    }

    public void setActFirstCjTotal(Integer actFirstCjTotal) {
        this.actFirstCjTotal = actFirstCjTotal;
    }


    public Integer getQzSecondTotal() {
        return qzSecondTotal;
    }

    public void setQzSecondTotal(Integer qzSecondTotal) {
        this.qzSecondTotal = qzSecondTotal;
    }

    public Integer getQzSecondCjTotal() {
        return qzSecondCjTotal;
    }

    public void setQzSecondCjTotal(Integer qzSecondCjTotal) {
        this.qzSecondCjTotal = qzSecondCjTotal;
    }


    public Integer getBalSecondTotal() {
        return balSecondTotal;
    }

    public void setBalSecondTotal(Integer balSecondTotal) {
        this.balSecondTotal = balSecondTotal;
    }

    public Integer getBalSecondCjTotal() {
        return balSecondCjTotal;
    }

    public void setBalSecondCjTotal(Integer balSecondCjTotal) {
        this.balSecondCjTotal = balSecondCjTotal;
    }


    public Integer getBalSecondTotal0() {
        return balSecondTotal0;
    }

    public void setBalSecondTotal0(Integer balSecondTotal0) {
        this.balSecondTotal0 = balSecondTotal0;
    }

    public Integer getBalSecondCjTotal0() {
        return balSecondCjTotal0;
    }

    public void setBalSecondCjTotal0(Integer balSecondCjTotal0) {
        this.balSecondCjTotal0 = balSecondCjTotal0;
    }


    public Integer getInnerActTotal() {
        return innerActTotal;
    }

    public void setInnerActTotal(Integer innerActTotal) {
        this.innerActTotal = innerActTotal;
    }

    public Integer getInnerActCjTotal() {
        return innerActCjTotal;
    }

    public void setInnerActCjTotal(Integer innerActCjTotal) {
        this.innerActCjTotal = innerActCjTotal;
    }

    public Double getGszyFirstCjRate() {
        return gszyFirstCjRate;
    }

    public void setGszyFirstCjRate(Double gszyFirstCjRate) {
        this.gszyFirstCjRate = gszyFirstCjRate;
    }

    public Double getGszyCurrentCjRate() {
        return gszyCurrentCjRate;
    }

    public void setGszyCurrentCjRate(Double gszyCurrentCjRate) {
        this.gszyCurrentCjRate = gszyCurrentCjRate;
    }

    public Double getTgkfFirstCjRate() {
        return tgkfFirstCjRate;
    }

    public void setTgkfFirstCjRate(Double tgkfFirstCjRate) {
        this.tgkfFirstCjRate = tgkfFirstCjRate;
    }

    public Double getMgmFirstCjRate() {
        return mgmFirstCjRate;
    }

    public void setMgmFirstCjRate(Double mgmFirstCjRate) {
        this.mgmFirstCjRate = mgmFirstCjRate;
    }

    public Double getActFirstCjRate() {
        return actFirstCjRate;
    }

    public void setActFirstCjRate(Double actFirstCjRate) {
        this.actFirstCjRate = actFirstCjRate;
    }

    public Double getQzSecondCjRate() {
        return qzSecondCjRate;
    }

    public void setQzSecondCjRate(Double qzSecondCjRate) {
        this.qzSecondCjRate = qzSecondCjRate;
    }

    public Double getBalSecondCjRate() {
        return balSecondCjRate;
    }

    public void setBalSecondCjRate(Double balSecondCjRate) {
        this.balSecondCjRate = balSecondCjRate;
    }

    public Double getBalSecondCjRate0() {
        return balSecondCjRate0;
    }

    public void setBalSecondCjRate0(Double balSecondCjRate0) {
        this.balSecondCjRate0 = balSecondCjRate0;
    }

    public Double getInnerActCjRate() {
        return innerActCjRate;
    }

    public void setInnerActCjRate(Double innerActCjRate) {
        this.innerActCjRate = innerActCjRate;
    }

    public Double getSelfCjRate() {
        return selfCjRate;
    }

    public void setSelfCjRate(Double selfCjRate) {
        this.selfCjRate = selfCjRate;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
