package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: CustRedem.java
 * <AUTHOR>
 * @version 1.0
 * @created 20200813
 */
public class CustRedem implements Serializable {


    private static final long serialVersionUID = -6019872240921713310L;

    private String orgCode;
    private String orgName;
    private String u2name;//区域
    private String consCode;
	private String consName;
    private String conscustNo;//投顾客户号
    private String conscustName;//投顾姓名
    private BigDecimal fundNum = new BigDecimal(0); // 持有产品数
    private String fundCode; // 持有产品代码
    private String fundType;//持有产品类型
    private String fundName;//持有产品名称(简称)
    private BigDecimal balanceVol = new BigDecimal(0);//持有份额
    private BigDecimal redemBalanceVol = new BigDecimal(0);//赎回份额
    private String expecttradedt;//预计交易日期
    private String redemAfterState; //赎回后状态
    private String warnDt; //预警时间
    private String remark; //备注
    private String tradestate; //交易状态
    private BigDecimal redemNum = new BigDecimal(0); // 赎回客户数
    private BigDecimal activeNum = new BigDecimal(0); // 激活客户数
    private BigDecimal activeRate = new BigDecimal(0); // 激活率

    private BigDecimal redemMarketCap = new BigDecimal(0); // 赎回市值
    private String againAckDt; // 赎回再成交时间
    private String againFundCode; // 再成交产品代码
    private String againFundType;//再成交产品类型
    private String againFundName;//再成交产品名称(简称)
    private BigDecimal againAckRmb = new BigDecimal(0) ;//再成交金额（万）
    private BigDecimal againAckRmbSum = new BigDecimal(0);//累计成交金额（万）
    private String redemMonth; // 赎回年份

    private String fundCodeCount; // 赎回再成交产品数量


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getConscustName() {
        return conscustName;
    }

    public void setConscustName(String conscustName) {
        this.conscustName = conscustName;
    }

    public BigDecimal getFundNum() {
        return fundNum;
    }

    public void setFundNum(BigDecimal fundNum) {
        this.fundNum = fundNum;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getRedemBalanceVol() {
        return redemBalanceVol;
    }

    public void setRedemBalanceVol(BigDecimal redemBalanceVol) {
        this.redemBalanceVol = redemBalanceVol;
    }

    public String getExpecttradedt() {
        return expecttradedt;
    }

    public void setExpecttradedt(String expecttradedt) {
        this.expecttradedt = expecttradedt;
    }

    public String getRedemAfterState() {
        return redemAfterState;
    }

    public void setRedemAfterState(String redemAfterState) {
        this.redemAfterState = redemAfterState;
    }

    public String getWarnDt() {
        return warnDt;
    }

    public void setWarnDt(String warnDt) {
        this.warnDt = warnDt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getRedemNum() {
        return redemNum;
    }

    public void setRedemNum(BigDecimal redemNum) {
        this.redemNum = redemNum;
    }

    public BigDecimal getActiveNum() {
        return activeNum;
    }

    public void setActiveNum(BigDecimal activeNum) {
        this.activeNum = activeNum;
    }

    public BigDecimal getActiveRate() {
        return activeRate;
    }

    public void setActiveRate(BigDecimal activeRate) {
        this.activeRate = activeRate;
    }

    public BigDecimal getRedemMarketCap() {
        return redemMarketCap;
    }

    public void setRedemMarketCap(BigDecimal redemMarketCap) {
        this.redemMarketCap = redemMarketCap;
    }

    public String getAgainAckDt() {
        return againAckDt;
    }

    public void setAgainAckDt(String againAckDt) {
        this.againAckDt = againAckDt;
    }

    public String getAgainFundCode() {
        return againFundCode;
    }

    public void setAgainFundCode(String againFundCode) {
        this.againFundCode = againFundCode;
    }

    public String getAgainFundType() {
        return againFundType;
    }

    public void setAgainFundType(String againFundType) {
        this.againFundType = againFundType;
    }

    public String getAgainFundName() {
        return againFundName;
    }

    public void setAgainFundName(String againFundName) {
        this.againFundName = againFundName;
    }

    public BigDecimal getAgainAckRmb() {
        return againAckRmb;
    }

    public void setAgainAckRmb(BigDecimal againAckRmb) {
        this.againAckRmb = againAckRmb;
    }

    public BigDecimal getAgainAckRmbSum() {
        return againAckRmbSum;
    }

    public void setAgainAckRmbSum(BigDecimal againAckRmbSum) {
        this.againAckRmbSum = againAckRmbSum;
    }

    public String getRedemMonth() {
        return redemMonth;
    }

    public void setRedemMonth(String redemMonth) {
        this.redemMonth = redemMonth;
    }

    public String getFundCodeCount() {
        return fundCodeCount;
    }

    public void setFundCodeCount(String fundCodeCount) {
        this.fundCodeCount = fundCodeCount;
    }

    public String getTradestate() {
        return tradestate;
    }

    public void setTradestate(String tradestate) {
        this.tradestate = tradestate;
    }
}
