package com.hb.crm.domain.callreport;


public class CallOutReport{

	private String custName; //"客户姓名",
	private String areaName; //"地区",
	private String sourceDate; //"预约时间",
	private String usermarkInfo; //"用户标签",
	private String commContent; //"沟通录入",
	private String bookingContent; //"预约内容",
	private String sourceAddr; //链接
	private String advisorName; //"投顾姓名",
	private String orgName;// "所属团队",
	private String distributeDate; //"分配时间",
	private String consultInfo; //"咨询事项",
	private String userId; //"处理人",
	private String disposeCount; //"再处理次数",
	private String disposeDate; //"再处理时间",
	private String handleDate; //"处理时间",
	private String taskType; //任务类型",
	private String conscustNo; //"投顾客户号",
	private String taskTypeDetail; //"类型描述",
	private String calloutStatus; //"呼出状态
	private String wirelessChannel; //无线渠道
	
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public String getSourceDate() {
		return sourceDate;
	}
	public void setSourceDate(String sourceDate) {
		this.sourceDate = sourceDate;
	}
	public String getUsermarkInfo() {
		return usermarkInfo;
	}
	public void setUsermarkInfo(String usermarkInfo) {
		this.usermarkInfo = usermarkInfo;
	}
	public String getCommContent() {
		return commContent;
	}
	public void setCommContent(String commContent) {
		this.commContent = commContent;
	}
	public String getBookingContent() {
		return bookingContent;
	}
	public void setBookingContent(String bookingContent) {
		this.bookingContent = bookingContent;
	}
	public String getAdvisorName() {
		return advisorName;
	}
	public void setAdvisorName(String advisorName) {
		this.advisorName = advisorName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getDistributeDate() {
		return distributeDate;
	}
	public void setDistributeDate(String distributeDate) {
		this.distributeDate = distributeDate;
	}
	public String getConsultInfo() {
		return consultInfo;
	}
	public void setConsultInfo(String consultInfo) {
		this.consultInfo = consultInfo;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getDisposeCount() {
		return disposeCount;
	}
	public void setDisposeCount(String disposeCount) {
		this.disposeCount = disposeCount;
	}
	public String getDisposeDate() {
		return disposeDate;
	}
	public void setDisposeDate(String disposeDate) {
		this.disposeDate = disposeDate;
	}
	public String getHandleDate() {
		return handleDate;
	}
	public void setHandleDate(String handleDate) {
		this.handleDate = handleDate;
	}
	public String getTaskType() {
		return taskType;
	}
	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}
	public String getConscustNo() {
		return conscustNo;
	}
	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}
	public String getTaskTypeDetail() {
		return taskTypeDetail;
	}
	public void setTaskTypeDetail(String taskTypeDetail) {
		this.taskTypeDetail = taskTypeDetail;
	}
	public String getCalloutStatus() {
		return calloutStatus;
	}
	public void setCalloutStatus(String calloutStatus) {
		this.calloutStatus = calloutStatus;
	}
	public String getWirelessChannel() {
		return wirelessChannel;
	}
	public void setWirelessChannel(String wirelessChannel) {
		this.wirelessChannel = wirelessChannel;
	}
	public String getSourceAddr() {
		return sourceAddr;
	}
	public void setSourceAddr(String sourceAddr) {
		this.sourceAddr = sourceAddr;
	}
	
}
