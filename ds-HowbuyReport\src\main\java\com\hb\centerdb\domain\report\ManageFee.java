package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class ManageFee implements Serializable {

    private static final long serialVersionUID = 3693922603387326746L;

    private String year;//年份
	
	private String period;//周期
	
	private String fundType;//类型

    private String fundCode;//基金代码
	
	private String company;//公司名称

    private String fundAttr;//投资基金简称
	
	private String contractDt;//合同周期
	
	private String conscustno;//投顾客户号
	
	private String custname;//客户姓名

    private String tradetype;//交易类型

    private String det;//下单部门

    private String consname;//所属投顾

    private String conscode;//投顾code

    private String u1Name;//中心

    private String u2Name;//部门1

    private String u3Name;//部门2

	private Double ackamt=0d;//金额（万）
	
	private Double ackvol=0d;//确认份额
	
	private Double nav=0d;//成交净值
	
	private Double mFeeTax=0d;//管理费

    private Double cFeeTax=0d;//业绩提成

    private Double mFee=0d;//税后管理费

    private Double cFee=0d;//税后业绩提成

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getContractDt() {
        return contractDt;
    }

    public void setContractDt(String contractDt) {
        this.contractDt = contractDt;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getCustname() {
        return custname;
    }

    public void setCustname(String custname) {
        this.custname = custname;
    }

    public String getTradetype() {
        return tradetype;
    }

    public void setTradetype(String tradetype) {
        this.tradetype = tradetype;
    }

    public String getDet() {
        return det;
    }

    public void setDet(String det) {
        this.det = det;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getU3Name() {
        return u3Name;
    }

    public void setU3Name(String u3Name) {
        this.u3Name = u3Name;
    }

    public Double getAckamt() {
        return ackamt;
    }

    public void setAckamt(Double ackamt) {
        this.ackamt = ackamt;
    }

    public Double getAckvol() {
        return ackvol;
    }

    public void setAckvol(Double ackvol) {
        this.ackvol = ackvol;
    }

    public Double getNav() {
        return nav;
    }

    public void setNav(Double nav) {
        this.nav = nav;
    }

    public Double getmFeeTax() {
        return mFeeTax;
    }

    public void setmFeeTax(Double mFeeTax) {
        this.mFeeTax = mFeeTax;
    }

    public Double getcFeeTax() {
        return cFeeTax;
    }

    public void setcFeeTax(Double cFeeTax) {
        this.cFeeTax = cFeeTax;
    }

    public Double getmFee() {
        return mFee;
    }

    public void setmFee(Double mFee) {
        this.mFee = mFee;
    }

    public Double getcFee() {
        return cFee;
    }

    public void setcFee(Double cFee) {
        this.cFee = cFee;
    }
}
