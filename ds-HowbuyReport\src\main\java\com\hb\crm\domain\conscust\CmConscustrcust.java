package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类CmConscustrcust.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmConscustrcust implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustrcustid;
	
	private String conscustno;
	
	private String custno;
	
	private String isrweb;
	
	private String isrelated;
	
	private String creator;
	
	private String credt;
	
	private String moddt;
	
	private Date stimestamp;
	
	public String getConscustrcustid() {
		return this.conscustrcustid;
	}

	public void setConscustrcustid(String conscustrcustid) {
		this.conscustrcustid = conscustrcustid;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getIsrweb() {
		return this.isrweb;
	}

	public void setIsrweb(String isrweb) {
		this.isrweb = isrweb;
	}
	
	public String getIsrelated() {
		return this.isrelated;
	}

	public void setIsrelated(String isrelated) {
		this.isrelated = isrelated;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
