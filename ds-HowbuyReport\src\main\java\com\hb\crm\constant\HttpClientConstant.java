package com.hb.crm.constant;

import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSONObject;

public class HttpClientConstant {
	
	public static final int SUCC_STATS = 100;
	
	//业务类型
	public static final Map<String,String> TRADETYPEMAP= new HashMap<String,String>();

	static{
		TRADETYPEMAP.put("01", "承保业务");
		TRADETYPEMAP.put("02", "退保业务");
		TRADETYPEMAP.put("03", "满期领取");
	}
	
	//产品类型
	public static final Map<String,String> PROTYPEMAP= new HashMap<String,String>();
	static{
		PROTYPEMAP.put("01", "万能险");
		PROTYPEMAP.put("02", "投连险");
		PROTYPEMAP.put("00", "其他");
	}
	
	//存入方式
	public static final Map<String,String> SAVETYPEMAP= new HashMap<String,String>();
	static{
		SAVETYPEMAP.put("01", "储蓄罐支付");
		SAVETYPEMAP.put("02", "保险公司银行卡代扣");
		SAVETYPEMAP.put("03", "支付宝支付");
		SAVETYPEMAP.put("04", "微信支付");
		SAVETYPEMAP.put("00", "其他");
	}
	
	//交易确认标志
	public static final Map<String,String> TRADEACKMAP= new HashMap<String,String>();
	static{
		TRADEACKMAP.put("0000", "未提交");
		TRADEACKMAP.put("0001", "核保通过");
		TRADEACKMAP.put("0002", "核保失败");
		TRADEACKMAP.put("0101", "支付成功");
		TRADEACKMAP.put("0102", "支付失败");
		TRADEACKMAP.put("0103", "支付确认中");
		TRADEACKMAP.put("0201", "承保受理成功");
		TRADEACKMAP.put("0202", "承保受理失败");
		TRADEACKMAP.put("0203", "承保成功");
		TRADEACKMAP.put("0204", "承保失败");
		TRADEACKMAP.put("0301", "正常退保受理成功");
		TRADEACKMAP.put("0302", "正常退保受理失败");
		TRADEACKMAP.put("0303", "正常退保成功");
		TRADEACKMAP.put("0304", "正常退保失败");
		TRADEACKMAP.put("0305", "退保中");
	}
	public static  JSONObject post(String url,String json){
		HttpClient client = new DefaultHttpClient();
		HttpPost post = new HttpPost(url);
		JSONObject jsonObject = new JSONObject();  
		JSONObject response = null;  
        try {  
            StringEntity s = new StringEntity(json);  
            s.setContentEncoding("gbk");  
            s.setContentType("application/json"); 
            post.setEntity(s);   
            HttpResponse res = client.execute(post);
            String resData = EntityUtils.toString(res.getEntity());
            response = jsonObject.parseObject(resData); 
        } catch (Exception e) {  
            throw new RuntimeException(e);  
        }  
		return response;
	}

}
