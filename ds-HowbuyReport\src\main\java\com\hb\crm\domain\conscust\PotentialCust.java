package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.sql.Timestamp;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;

/** <AUTHOR> CodeGenerator */
public class PotentialCust implements Serializable {

    /** identifier field */
    private String PCustID = "";

    /** nullable persistent field */
    private String PCustLevel = "";

    /** nullable persistent field */
    private Integer PGrade;

    /** nullable persistent field */
    private String PCustStatus = "";

    /** nullable persistent field */
    private String IDType = "";

    /** nullable persistent field */
    private String IDNo = "";

    /** nullable persistent field */
    private String custName = "";

    /** nullable persistent field */
    private String provCode = "";

    /** nullable persistent field */
    private String cityCode = "";

    /** nullable persistent field */
    private String eduLevel = "";

    /** nullable persistent field */
    private String vocation = "";

    /** nullable persistent field */
    private String incLevel = "";

    /** nullable persistent field */
    private String birthday = "";

    /** nullable persistent field */
    private String gender = "";

    /** nullable persistent field */
    private String married = "";

    /** nullable persistent field */
    private String PIncome = "";

    /** nullable persistent field */
    private String fincome = "";

    /** nullable persistent field */
    private String decisionFlag = "";

    /** nullable persistent field */
    private String interests = "";

    /** nullable persistent field */
    private String familyCondition = "";

    /** nullable persistent field */
    private String contactTime = "";

    /** nullable persistent field */
    private String contactMethod = "";

    /** nullable persistent field */
    private String sendInfoFlag = "";

    /** nullable persistent field */
    private String recvEmailFlag = "";

    /** nullable persistent field */
    private String recvTelFlag = "";

    /** nullable persistent field */
    private String recvMsgFlag = "";

    /** nullable persistent field */
    private String company = "";

    /** nullable persistent field */
    private String riskLevel = "";

    /** nullable persistent field */
    private String selfRiskLevel = "";

    /** nullable persistent field */
    private String addr = "";

    /** nullable persistent field */
    private String postCode = "";

    /** nullable persistent field */
    private String addr2 = "";

    /** nullable persistent field */
    private String postCode2 = "";

    /** nullable persistent field */
    private String addr3 = "";

    /** nullable persistent field */
    private String postCode3 = "";

    /** nullable persistent field */
    private String mobile = "";

    /** nullable persistent field */
    private String mobile2 = "";

    /** nullable persistent field */
    private String telNo = "";

    /** nullable persistent field */
    private String fax = "";

    /** nullable persistent field */
    private String pagerNo = "";

    /** nullable persistent field */
    private String email = "";

    /** nullable persistent field */
    private String email2 = "";

    /** nullable persistent field */
    private String homeTelNo = "";

    /** nullable persistent field */
    private String officeTelNo = "";

    /** nullable persistent field */
    private String actCode = "";

    /** nullable persistent field */
    private String brokerCode = "";

    /** nullable persistent field */
    private String consCode = "";

    /** nullable persistent field */
    private String intrCustNo = "";

    /** nullable persistent field */
    private String source = "";
    
    /** nullable persistent field */
    private String newSourceNo = "";

    /** nullable persistent field */
    private String knowChan = "";

    /** nullable persistent field */
    private String otherChan = "";

    /** nullable persistent field */
    private String otherInvest = "";

    /** nullable persistent field */
    private String salon = "";

    /** nullable persistent field */
    private double WInvestAmt =0.00;

    /** nullable persistent field */
    private String WCustLevel = "";

    /** nullable persistent field */
    private String financeMemo = "";

    /** nullable persistent field */
    private String beforeInvest = "";

    /** nullable persistent field */
    private String IM = "";

    /** nullable persistent field */
    private String regDt = "";

    /** nullable persistent field */
    private String udDt = "";

   
    
    /**
     * 可投资资产.
     */
    private String AInvestAMT;
    /**
     * 可投资基金资产.
     */
    private String AInvestFAMT;
    
    private String selfDefFlag;
    
    /**
     * 沟通频率.
     */
    private String visitFqcy;
    /**
     * 发展方向.
     */
    private String devDirection;
    /**
     * 来源细分。
     */
    private String subSource;
    /**
     * 客户来源细分分类.
     */
    private String subSourceType;
    
    private String batchID;
    
    private String consCustNo;
    /**
     * 备注
     */
    private String memo;
    /**
     * 时间戳
     */
    private Timestamp stimestamp;
    /**
	 * @return the batchID
	 */
	public String getBatchID() {
		return batchID;
	}
	

	/**
	 * @param batchID the batchID to set
	 */
	public void setBatchID(String batchID) {
		this.batchID = batchID;
	}

	//@Temporal(TemporalType.TIMESTAMP)
	public Timestamp getStimestamp() {
		return stimestamp;
	}

	public void setStimestamp(Timestamp stimestamp) {
		this.stimestamp = stimestamp;
	}

	/**
	 * @return the consCustNo
	 */
	public String getConsCustNo() {
		return consCustNo;
	}

	/**
	 * @param consCustNo the consCustNo to set
	 */
	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	/**
     * @return the visitFqcy
     */
    public String getVisitFqcy() {
        return visitFqcy;
    }

    /**
     * @param visitFqcy the visitFqcy to set
     */
    public void setVisitFqcy(String visitFqcy) {
        this.visitFqcy = visitFqcy;
    }

    /**
     * @return the devDirection
     */
    public String getDevDirection() {
        return devDirection;
    }

    /**
     * @param devDirection the devDirection to set
     */
    public void setDevDirection(String devDirection) {
        this.devDirection = devDirection;
    }

    /**
     * @return the subSource
     */
    public String getSubSource() {
        return subSource;
    }

    /**
     * @param subSource the subSource to set
     */
    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    /**
     * @return the subSourceType
     */
    public String getSubSourceType() {
        return subSourceType;
    }

    /**
     * @param subSourceType the subSourceType to set
     */
    public void setSubSourceType(String subSourceType) {
        this.subSourceType = subSourceType;
    }

    /**
     * @return the aInvestAMT
     */
    public String getAInvestAMT() {
        return AInvestAMT;
    }

    /**
     * @param investAMT the aInvestAMT to set
     */
    public void setAInvestAMT(String investAMT) {
        AInvestAMT = investAMT;
    }

    /**
     * @return the aInvestFAMT
     */
    public String getAInvestFAMT() {
        return AInvestFAMT;
    }

    /**
     * @param investFAMT the aInvestFAMT to set
     */
    public void setAInvestFAMT(String investFAMT) {
        AInvestFAMT = investFAMT;
    }

  
    /** default constructor */
    public PotentialCust() {
    }

    public String getPCustID() {
        return this.PCustID;
    }

    public void setPCustID(String PCustID) {
        this.PCustID = PCustID;
    }

    public String getPCustLevel() {
        return this.PCustLevel;
    }

    public void setPCustLevel(String PCustLevel) {
        this.PCustLevel = PCustLevel;
    }

    public Integer getPGrade() {
        return this.PGrade;
    }

    public void setPGrade(Integer PGrade) {
        this.PGrade = PGrade;
    }

    public String getPCustStatus() {
        return this.PCustStatus;
    }

    public void setPCustStatus(String PCustStatus) {
        this.PCustStatus = PCustStatus;
    }

    public String getIDType() {
        return this.IDType;
    }

    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    public String getIDNo() {
        return this.IDNo;
    }

    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    public String getCustName() {
        return this.custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProvCode() {
        return this.provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }

    public String getCityCode() {
        return this.cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getEduLevel() {
        return this.eduLevel;
    }

    public void setEduLevel(String eduLevel) {
        this.eduLevel = eduLevel;
    }

    public String getVocation() {
        return this.vocation;
    }

    public void setVocation(String vocation) {
        this.vocation = vocation;
    }

    public String getIncLevel() {
        return this.incLevel;
    }

    public void setIncLevel(String incLevel) {
        this.incLevel = incLevel;
    }

    public String getBirthday() {
        return this.birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getGender() {
        return this.gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getMarried() {
        return this.married;
    }

    public void setMarried(String married) {
        this.married = married;
    }

    public String getPIncome() {
        return this.PIncome;
    }

    public void setPIncome(String PIncome) {
        this.PIncome = PIncome;
    }

    public String getFincome() {
        return this.fincome;
    }

    public void setFincome(String fincome) {
        this.fincome = fincome;
    }

    public String getDecisionFlag() {
        return this.decisionFlag;
    }

    public void setDecisionFlag(String decisionFlag) {
        this.decisionFlag = decisionFlag;
    }

    public String getInterests() {
        return this.interests;
    }

    public void setInterests(String interests) {
        this.interests = interests;
    }

    public String getFamilyCondition() {
        return this.familyCondition;
    }

    public void setFamilyCondition(String familyCondition) {
        this.familyCondition = familyCondition;
    }

    public String getContactTime() {
        return this.contactTime;
    }

    public void setContactTime(String contactTime) {
        this.contactTime = contactTime;
    }

    public String getContactMethod() {
        return this.contactMethod;
    }

    public void setContactMethod(String contactMethod) {
        this.contactMethod = contactMethod;
    }

    public String getSendInfoFlag() {
        return this.sendInfoFlag;
    }

    public void setSendInfoFlag(String sendInfoFlag) {
        this.sendInfoFlag = sendInfoFlag;
    }

    public String getRecvEmailFlag() {
        return this.recvEmailFlag;
    }

    public void setRecvEmailFlag(String recvEmailFlag) {
        this.recvEmailFlag = recvEmailFlag;
    }

    public String getRecvTelFlag() {
        return this.recvTelFlag;
    }

    public void setRecvTelFlag(String recvTelFlag) {
        this.recvTelFlag = recvTelFlag;
    }

    public String getRecvMsgFlag() {
        return this.recvMsgFlag;
    }

    public void setRecvMsgFlag(String recvMsgFlag) {
        this.recvMsgFlag = recvMsgFlag;
    }

    public String getCompany() {
        return this.company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getRiskLevel() {
        return this.riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getSelfRiskLevel() {
        return this.selfRiskLevel;
    }

    public void setSelfRiskLevel(String selfRiskLevel) {
        this.selfRiskLevel = selfRiskLevel;
    }

    public String getAddr() {
        return this.addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public String getPostCode() {
        return this.postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getAddr2() {
        return this.addr2;
    }

    public void setAddr2(String addr2) {
        this.addr2 = addr2;
    }

    public String getPostCode2() {
        return this.postCode2;
    }

    public void setPostCode2(String postCode2) {
        this.postCode2 = postCode2;
    }

    public String getAddr3() {
        return this.addr3;
    }

    public void setAddr3(String addr3) {
        this.addr3 = addr3;
    }

    public String getPostCode3() {
        return this.postCode3;
    }

    public void setPostCode3(String postCode3) {
        this.postCode3 = postCode3;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile2() {
        return this.mobile2;
    }

    public void setMobile2(String mobile2) {
        this.mobile2 = mobile2;
    }

    public String getTelNo() {
        return this.telNo;
    }

    public void setTelNo(String telNo) {
        this.telNo = telNo;
    }

    public String getFax() {
        return this.fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getPagerNo() {
        return this.pagerNo;
    }

    public void setPagerNo(String pagerNo) {
        this.pagerNo = pagerNo;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail2() {
        return this.email2;
    }

    public void setEmail2(String email2) {
        this.email2 = email2;
    }

    public String getHomeTelNo() {
        return this.homeTelNo;
    }

    public void setHomeTelNo(String homeTelNo) {
        this.homeTelNo = homeTelNo;
    }

    public String getOfficeTelNo() {
        return this.officeTelNo;
    }

    public void setOfficeTelNo(String officeTelNo) {
        this.officeTelNo = officeTelNo;
    }

    public String getActCode() {
        return this.actCode;
    }

    public void setActCode(String actCode) {
        this.actCode = actCode;
    }

    public String getBrokerCode() {
        return this.brokerCode;
    }

    public void setBrokerCode(String brokerCode) {
        this.brokerCode = brokerCode;
    }

    public String getConsCode() {
        return this.consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getIntrCustNo() {
        return this.intrCustNo;
    }

    public void setIntrCustNo(String intrCustNo) {
        this.intrCustNo = intrCustNo;
    }

    public String getSource() {
        return this.source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getNewSourceNo() {
		return newSourceNo;
	}

	public void setNewSourceNo(String newSourceNo) {
		this.newSourceNo = newSourceNo;
	}


	public String getKnowChan() {
        return this.knowChan;
    }

    public void setKnowChan(String knowChan) {
        this.knowChan = knowChan;
    }

    public String getOtherChan() {
        return this.otherChan;
    }

    public void setOtherChan(String otherChan) {
        this.otherChan = otherChan;
    }

    public String getOtherInvest() {
        return this.otherInvest;
    }

    public void setOtherInvest(String otherInvest) {
        this.otherInvest = otherInvest;
    }

    public String getSalon() {
        return this.salon;
    }

    public void setSalon(String salon) {
        this.salon = salon;
    }

    public double getWInvestAmt() {
        return this.WInvestAmt;
    }

    public void setWInvestAmt(double WInvestAmt) {
        this.WInvestAmt = WInvestAmt;
    }

    public String getWCustLevel() {
        return this.WCustLevel;
    }

    public void setWCustLevel(String WCustLevel) {
        this.WCustLevel = WCustLevel;
    }

    public String getFinanceMemo() {
        return this.financeMemo;
    }

    public void setFinanceMemo(String financeMemo) {
        this.financeMemo = financeMemo;
    }

    public String getBeforeInvest() {
        return this.beforeInvest;
    }

    public void setBeforeInvest(String beforeInvest) {
        this.beforeInvest = beforeInvest;
    }

    public String getIM() {
        return this.IM;
    }

    public void setIM(String IM) {
        this.IM = IM;
    }

    public String getRegDt() {
        return this.regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }

    public String getUdDt() {
        return this.udDt;
    }

    public void setUdDt(String udDt) {
        this.udDt = udDt;
    }


    @Override
	public String toString() {
        return new ToStringBuilder(this)
            .append("PCustID", getPCustID())
            .toString();
    }

    @Override
	public boolean equals(Object other) {
        if ( !(other instanceof PotentialCust) ) return false;
        PotentialCust castOther = (PotentialCust) other;
        return new EqualsBuilder()
            .append(this.getPCustID(), castOther.getPCustID())
            .isEquals();
    }

    @Override
	public int hashCode() {
        return new HashCodeBuilder()
            .append(getPCustID())
            .toHashCode();
    }

    /**
     * @return the selfDefFlag
     */
    public String getSelfDefFlag() {
        return selfDefFlag;
    }

    /**
     * @param selfDefFlag the selfDefFlag to set
     */
    public void setSelfDefFlag(String selfDefFlag) {
        this.selfDefFlag = selfDefFlag;
    }

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}


}
