package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类Fundgroup.java
 * @version 1.0
 */
public class FundgroupDs implements Serializable {

    private static final long serialVersionUID = 2994799431646349065L;

    private String id;

    private String groupname;
	
	private String fundcode;

	private String memo;

	private String creator;

	private String modifier;

	private String credt;

	private String moddt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getGroupname() {
		return groupname;
	}

	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}
	
	public String getFundcode() {
		return fundcode;
	}

	public void setFundcode(String fundcode) {
		this.fundcode = fundcode;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
