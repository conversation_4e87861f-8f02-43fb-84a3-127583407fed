/**
 * 2011-03-03 chris.song	贵宾产品
 */
package com.hb.crm.constant.help;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 产品信息.
 */
public enum ConsCustProductEnum {
	/**
	 * 私募.
	 */
	PriFund(1,true),
	/**
	 * 交行安家.
	 */
	AnjiaBOCOM(2, true),
	/**
	 * 海通安家.
	 */
	AnjiaHaiT(3,true),
	/**
	 * 一对多.
	 */
	OneToMore(4,true),
	/**
	 * VC/PE.
	 */
	VcOrPe(5, true),
	/**
	 * TOT/FOF.
	 */
	TotOrFof(6,true),
	/**
	 * 固定收益.
	 */
	FixRatio(7, true),
	/**
	 * 诊断.
	 */
	AnjiaDongF(8, false),
	/**
	 * 公募
	 */
	PubFund(12,true)
	;
	private int value;
	
	private boolean needCheck;

    public int getValue() {
        return value;
    }
    public boolean getNeedCheck() {
    	return needCheck;
    }
    ConsCustProductEnum(int value, boolean needCheck) {
        this.value = value;
        this.needCheck = needCheck;
    }
    public final static Map<Integer, String> productDescMAP;//产品描述
    public final static Map<Integer, Boolean> productSpecMAP;//产品特性
    public final static int[] vipProducts = new int[] {PriFund.value, OneToMore.value, VcOrPe.value, TotOrFof.value, FixRatio.value };
    
    static{

    	productDescMAP = new HashMap<Integer, String>();
    	productSpecMAP = new HashMap<Integer, Boolean>();
    	productDescMAP.put(1, "私募");
    	productDescMAP.put(2, "交行安家");
    	productDescMAP.put(3, "海通安家");
    	productDescMAP.put(4, "一对多");
    	productDescMAP.put(5, "TOT/FOF");
    	productDescMAP.put(6, "VC/PE");
    	productDescMAP.put(7, "固定收益");
    	productDescMAP.put(8, "东方安家");
    	
    	productSpecMAP.put(1, true);
    	productSpecMAP.put(2, true);
    	productSpecMAP.put(3, true);
    	productSpecMAP.put(4, true);
    	productSpecMAP.put(5, true);
    	productSpecMAP.put(6, true);
    	productSpecMAP.put(7, true);
    	productSpecMAP.put(8, true);
        
    }
    
    /**
     * 返回对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final int value) {
        return ConsCustProductEnum.productDescMAP.get(
        		value);
    }
    public static boolean getProductSpec(final int value) {
    	return ConsCustProductEnum.productSpecMAP.get(
        		value);
    }

    public static int[] getVipProducts() {
    	return vipProducts;
    }
}
