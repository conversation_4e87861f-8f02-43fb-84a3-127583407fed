package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类CmCustfamily.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmCustfamily implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;
	
	private String custname;
	
	private String conscode;
	
	private String consname;
	
	private String checkflag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String credt;
	
	private String moddt;
	
	private String checkdt;
	
	private String source;
	private String sourcename;
	private String subsource;
	private String subsourcetype;
	private String subnum;
	private String custsourceremark;
	private String regdt;
	private String remark;
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getCheckflag() {
		return this.checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	
	public String getCheckdt() {
		return this.checkdt;
	}

	public void setCheckdt(String checkdt) {
		this.checkdt = checkdt;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getSource() {
	return source;
}

public void setSource(String source) {
	this.source = source;
}

public String getSourcename() {
	return sourcename;
}

public void setSourcename(String sourcename) {
	this.sourcename = sourcename;
}

public String getSubsource() {
	return subsource;
}

public void setSubsource(String subsource) {
	this.subsource = subsource;
}

public String getSubsourcetype() {
	return subsourcetype;
}

public void setSubsourcetype(String subsourcetype) {
	this.subsourcetype = subsourcetype;
}

public String getSubnum() {
	return subnum;
}

public void setSubnum(String subnum) {
	this.subnum = subnum;
}

public String getCustsourceremark() {
	return custsourceremark;
}

public void setCustsourceremark(String custsourceremark) {
	this.custsourceremark = custsourceremark;
}

public String getRegdt() {
	return regdt;
}

public void setRegdt(String regdt) {
	this.regdt = regdt;
}

public String getRemark() {
	return remark;
}

public void setRemark(String remark) {
	this.remark = remark;
}
}
