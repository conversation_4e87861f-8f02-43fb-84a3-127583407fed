package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类SalesFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class SalesFee implements Serializable {

private static final long serialVersionUID = 1L;

	
	private String fundManCode;//基金公司代码
	
	private String fundManName;//基金公司名称
	
	private String fundCode;//基金代码
	
	private String fundName;//基金名称
	
	private String fundType;//基金类型
	
	private String fundTypeName;//基金类型名称
	
	private Double avgBalVol=0d;//平均保有份额
	
	private Double avgBalAmt=0d;//平均保有金额
	
	private Double sumBalVol=0d;//累计保有份额
	
	private Double sumBalAmt=0d;//累计保有金额
	
	private Double salesFee=0d;//销售服务费
	
	private Double tailFee=0d;//尾随佣金
	
	private String methodType;//计算方式 0:四舍五入 1:截取
	
	// add by shiping.lin 20170404
	
	private Double saleFeeRate=0d; // 销售服务费率
	
	private Double fundManFeeRate=0d;	//管理费率
	
	private Double fundManFeeDivRatio=0d;	//尾佣分成比例
	
	
	public Double getSaleFeeRate() {
		return saleFeeRate;
	}


	public void setSaleFeeRate(Double saleFeeRate) {
		this.saleFeeRate = saleFeeRate;
	}


	public Double getFundManFeeRate() {
		return fundManFeeRate;
	}


	public void setFundManFeeRate(Double fundManFeeRate) {
		this.fundManFeeRate = fundManFeeRate;
	}


	public Double getFundManFeeDivRatio() {
		return fundManFeeDivRatio;
	}


	public void setFundManFeeDivRatio(Double fundManFeeDivRatio) {
		this.fundManFeeDivRatio = fundManFeeDivRatio;
	}


	public String getMethodType() {
		return methodType;
	}


	public void setMethodType(String methodType) {
		this.methodType = methodType;
	}





	public String getFundManCode() {
		return fundManCode;
	}





	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}





	public String getFundManName() {
		return fundManName;
	}





	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}





	public String getFundCode() {
		return fundCode;
	}





	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}





	public String getFundName() {
		return fundName;
	}





	public void setFundName(String fundName) {
		this.fundName = fundName;
	}





	public String getFundType() {
		return fundType;
	}





	public void setFundType(String fundType) {
		this.fundType = fundType;
	}





	public String getFundTypeName() {
		return fundTypeName;
	}





	public void setFundTypeName(String fundTypeName) {
		this.fundTypeName = fundTypeName;
	}





	public Double getAvgBalVol() {
		return avgBalVol;
	}





	public void setAvgBalVol(Double avgBalVol) {
		this.avgBalVol = avgBalVol;
	}





	public Double getAvgBalAmt() {
		return avgBalAmt;
	}





	public void setAvgBalAmt(Double avgBalAmt) {
		this.avgBalAmt = avgBalAmt;
	}





	public Double getSumBalVol() {
		return sumBalVol;
	}





	public void setSumBalVol(Double sumBalVol) {
		this.sumBalVol = sumBalVol;
	}





	public Double getSumBalAmt() {
		return sumBalAmt;
	}





	public void setSumBalAmt(Double sumBalAmt) {
		this.sumBalAmt = sumBalAmt;
	}





	public Double getSalesFee() {
		return salesFee;
	}





	public void setSalesFee(Double salesFee) {
		this.salesFee = salesFee;
	}





	public Double getTailFee() {
		return tailFee;
	}





	public void setTailFee(Double tailFee) {
		this.tailFee = tailFee;
	}





	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
