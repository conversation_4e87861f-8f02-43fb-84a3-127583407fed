package com.hb.crm.domain.conscust;

import java.util.List;

public class ConsVisitInfo {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String creator ="";
	
	private String conscustno ="";
	
	private String custname ="";
	
	private String visitsummary="";
	
	
	
	//回访人
	private String consCode="";
	
	private String consName="";
	//回访日期
	private String visittime="";
	//回访数量
	private int visitcount;
	//回访对象
	private String custNames;
	
	private List<String> conscustnos;

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getVisittime() {
		return visittime;
	}

	public void setVisittime(String visittime) {
		this.visittime = visittime;
	}

	public int getVisitcount() {
		return visitcount;
	}

	public void setVisitcount(int visitcount) {
		this.visitcount = visitcount;
	}

	public String getCustNames() {
		return custNames;
	}

	public void setCustNames(String custNames) {
		this.custNames = custNames;
	}
	

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public List<String> getConscustnos() {
		return conscustnos;
	}

	public void setConscustnos(List<String> conscustnos) {
		this.conscustnos = conscustnos;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getVisitsummary() {
		return visitsummary;
	}

	public void setVisitsummary(String visitsummary) {
		this.visitsummary = visitsummary;
	}
	
	
	
	
	
	

}
