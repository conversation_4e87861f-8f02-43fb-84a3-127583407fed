<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hb.crm.persistence.conscust.CmConscustMapper">   
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	
	 <select id="getCmConscust" parameterType="Map" resultType="CmConscust" useCache="false">
	    SELECT
	          *
	    FROM CM_CONSCUST
	    where 1=1  
	              <if test="conscustno != null"> AND conscustno = #{conscustno} </if>             
                  <if test="conscustlvl != null"> AND conscustlvl = #{conscustlvl} </if>             
                  <if test="conscustgrade != null"> AND conscustgrade = #{conscustgrade} </if>             
                  <if test="conscuststatus != null"> AND conscuststatus = #{conscuststatus} </if>             
                  <if test="idtype != null"> AND idtype = #{idtype} </if>             
                  <if test="idno != null"> AND idno = #{idno} </if>             
                  <if test="custname != null"> AND custname = #{custname} </if>             
                  <if test="provcode != null"> AND provcode = #{provcode} </if>             
                  <if test="citycode != null"> AND citycode = #{citycode} </if>             
                  <if test="edulevel != null"> AND edulevel = #{edulevel} </if>             
                  <if test="vocation != null"> AND vocation = #{vocation} </if>             
                  <if test="inclevel != null"> AND inclevel = #{inclevel} </if>             
                  <if test="birthday != null"> AND birthday = #{birthday} </if>             
                  <if test="gender != null"> AND gender = #{gender} </if>             
                  <if test="married != null"> AND married = #{married} </if>             
                  <if test="pincome != null"> AND pincome = #{pincome} </if>             
                  <if test="fincome != null"> AND fincome = #{fincome} </if>             
                  <if test="decisionflag != null"> AND decisionflag = #{decisionflag} </if>             
                  <if test="interests != null"> AND interests = #{interests} </if>             
                  <if test="familycondition != null"> AND familycondition = #{familycondition} </if>             
                  <if test="contacttime != null"> AND contacttime = #{contacttime} </if>             
                  <if test="contactmethod != null"> AND contactmethod = #{contactmethod} </if>             
                  <if test="sendinfoflag != null"> AND sendinfoflag = #{sendinfoflag} </if>             
                  <if test="recvtelflag != null"> AND recvtelflag = #{recvtelflag} </if>             
                  <if test="recvemailflag != null"> AND recvemailflag = #{recvemailflag} </if>             
                  <if test="recvmsgflag != null"> AND recvmsgflag = #{recvmsgflag} </if>             
                  <if test="company != null"> AND company = #{company} </if>             
                  <if test="risklevel != null"> AND risklevel = #{risklevel} </if>             
                  <if test="selfrisklevel != null"> AND selfrisklevel = #{selfrisklevel} </if>             
                  <if test="addr != null"> AND addr = #{addr} </if>             
                  <if test="postcode != null"> AND postcode = #{postcode} </if>             
                  <if test="mobile != null"> AND mobile = #{mobile} </if>             
                  <if test="telno != null"> AND telno = #{telno} </if>             
                  <if test="fax != null"> AND fax = #{fax} </if>             
                  <if test="email != null"> AND email = #{email} </if>             
                  <if test="hometelno != null"> AND hometelno = #{hometelno} </if>             
                  <if test="officetelno != null"> AND officetelno = #{officetelno} </if>             
                  <if test="actcode != null"> AND actcode = #{actcode} </if>             
                  <if test="intrcustno != null"> AND intrcustno = #{intrcustno} </if>             
                  <if test="source != null"> AND source = #{source} </if>             
                  <if test="knowchan != null"> AND knowchan = #{knowchan} </if>             
                  <if test="otherchan != null"> AND otherchan = #{otherchan} </if>             
                  <if test="otherinvest != null"> AND otherinvest = #{otherinvest} </if>             
                  <if test="salon != null"> AND salon = #{salon} </if>             
                  <if test="beforeinvest != null"> AND beforeinvest = #{beforeinvest} </if>             
                  <if test="im != null"> AND im = #{im} </if>             
                  <if test="msn != null"> AND msn = #{msn} </if>             
                  <if test="ainvestamt != null"> AND ainvestamt = #{ainvestamt} </if>             
                  <if test="ainvestfamt != null"> AND ainvestfamt = #{ainvestfamt} </if>             
                  <if test="selfdefflag != null"> AND selfdefflag = #{selfdefflag} </if>             
                  <if test="visitfqcy != null"> AND visitfqcy = #{visitfqcy} </if>             
                  <if test="devdirection != null"> AND devdirection = #{devdirection} </if>             
                  <if test="saledirection != null"> AND saledirection = #{saledirection} </if>             
                  <if test="subsource != null"> AND subsource = #{subsource} </if>             
                  <if test="subsourcetype != null"> AND subsourcetype = #{subsourcetype} </if>             
                  <if test="saleprocess != null"> AND saleprocess = #{saleprocess} </if>             
                  <if test="mergedconscust != null"> AND mergedconscust = #{mergedconscust} </if>             
                  <if test="addr2 != null"> AND addr2 = #{addr2} </if>             
                  <if test="postcode2 != null"> AND postcode2 = #{postcode2} </if>             
                  <if test="mobile2 != null"> AND mobile2 = #{mobile2} </if>             
                  <if test="email2 != null"> AND email2 = #{email2} </if>             
                  <if test="knowhowbuy != null"> AND knowhowbuy = #{knowhowbuy} </if>             
                  <if test="subknow != null"> AND subknow = #{subknow} </if>             
                  <if test="subknowtype != null"> AND subknowtype = #{subknowtype} </if>             
                  <if test="buyingprod != null"> AND buyingprod = #{buyingprod} </if>             
                  <if test="buyedprod != null"> AND buyedprod = #{buyedprod} </if>             
                  <if test="freeprod != null"> AND freeprod = #{freeprod} </if>             
                  <if test="specialflag != null"> AND specialflag = #{specialflag} </if>             
                  <if test="dlvymode != null"> AND dlvymode = #{dlvymode} </if>             
                  <if test="remark != null"> AND remark = #{remark} </if>             
                  <if test="regdt != null"> AND regdt = #{regdt} </if>             
                  <if test="uddt != null"> AND uddt = #{uddt} </if>             
                  <if test="pririsklevel != null"> AND pririsklevel = #{pririsklevel} </if>             
                  <if test="linkman != null"> AND linkman = #{linkman} </if>             
                  <if test="linktel != null"> AND linktel = #{linktel} </if>             
                  <if test="linkmobile != null"> AND linkmobile = #{linkmobile} </if>             
                  <if test="linkemail != null"> AND linkemail = #{linkemail} </if>             
                  <if test="linkpostcode != null"> AND linkpostcode = #{linkpostcode} </if>             
                  <if test="linkaddr != null"> AND linkaddr = #{linkaddr} </if>             
                  <if test="capacity != null"> AND capacity = #{capacity} </if>             
                  <if test="activityno != null"> AND activityno = #{activityno} </if>             
                  <if test="partnerno != null"> AND partnerno = #{partnerno} </if>             
                  <if test="gpsinvestlevel != null"> AND gpsinvestlevel = #{gpsinvestlevel} </if>             
                  <if test="gpsrisklevel != null"> AND gpsrisklevel = #{gpsrisklevel} </if>             
                  <if test="isboss != null"> AND isboss = #{isboss} </if>             
                  <if test="financeneed != null"> AND financeneed = #{financeneed} </if>             
                  <if test="isjoinclub != null"> AND isjoinclub = #{isjoinclub} </if>             
                  <if test="tmpBeforeinvest != null"> AND tmp_beforeinvest = #{tmpBeforeinvest} </if>             
                  <if test="tmpOtherinvest != null"> AND tmp_otherinvest = #{tmpOtherinvest} </if>             
                  <if test="isrisktip != null"> AND isrisktip = #{isrisktip} </if>             
                  <if test="tempemailflag != null"> AND tempemailflag = #{tempemailflag} </if>             
                  <if test="custsourceremark != null"> AND custsourceremark = #{custsourceremark} </if> 
                  <if test="invsttype != null"> AND invsttype = #{invsttype} </if>        
                  <if test="hbvipUsername != null"> AND hbvipUsername = #{hbvipUsername} </if>   
                  <if test="newsourceno != null"> AND newsourceno = #{newsourceno} </if> 
                  <if test="hboneno != null"> AND hbone_no = #{hboneno} </if>    
        	  </select>
	  
	  
	  <insert id="insertCmConscust" parameterType="CmConscust" >
	    INSERT INTO CM_CONSCUST (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="conscustno != null"> conscustno, </if> 
	      	      <if test="conscustlvl != null"> conscustlvl, </if> 
	      	      <if test="conscustgrade != null"> conscustgrade, </if> 
	      	      <if test="conscuststatus != null"> conscuststatus, </if> 
	      	      <if test="idtype != null"> idtype, </if> 
	      	      <if test="idno != null"> idno, </if> 
	      	      <if test="custname != null"> custname, </if> 
	      	      <if test="provcode != null"> provcode, </if> 
	      	      <if test="citycode != null"> citycode, </if> 
	      	      <if test="edulevel != null"> edulevel, </if> 
	      	      <if test="vocation != null"> vocation, </if> 
	      	      <if test="inclevel != null"> inclevel, </if> 
	      	      <if test="birthday != null"> birthday, </if> 
	      	      <if test="gender != null"> gender, </if> 
	      	      <if test="married != null"> married, </if> 
	      	      <if test="pincome != null"> pincome, </if> 
	      	      <if test="fincome != null"> fincome, </if> 
	      	      <if test="decisionflag != null"> decisionflag, </if> 
	      	      <if test="interests != null"> interests, </if> 
	      	      <if test="familycondition != null"> familycondition, </if> 
	      	      <if test="contacttime != null"> contacttime, </if> 
	      	      <if test="contactmethod != null"> contactmethod, </if> 
	      	      <if test="sendinfoflag != null"> sendinfoflag, </if> 
	      	      <if test="recvtelflag != null"> recvtelflag, </if> 
	      	      <if test="recvemailflag != null"> recvemailflag, </if> 
	      	      <if test="recvmsgflag != null"> recvmsgflag, </if> 
	      	      <if test="company != null"> company, </if> 
	      	      <if test="risklevel != null"> risklevel, </if> 
	      	      <if test="selfrisklevel != null"> selfrisklevel, </if> 
	      	      <if test="addr != null"> addr, </if> 
	      	      <if test="postcode != null"> postcode, </if> 
	      	      <if test="mobile != null"> mobile, </if> 
	      	      <if test="telno != null"> telno, </if> 
	      	      <if test="fax != null"> fax, </if> 
	      	      <if test="email != null"> email, </if> 
	      	      <if test="hometelno != null"> hometelno, </if> 
	      	      <if test="officetelno != null"> officetelno, </if> 
	      	      <if test="actcode != null"> actcode, </if> 
	      	      <if test="intrcustno != null"> intrcustno, </if> 
	      	      <if test="source != null"> source, </if> 
	      	      <if test="knowchan != null"> knowchan, </if> 
	      	      <if test="otherchan != null"> otherchan, </if> 
	      	      <if test="otherinvest != null"> otherinvest, </if> 
	      	      <if test="salon != null"> salon, </if> 
	      	      <if test="beforeinvest != null"> beforeinvest, </if> 
	      	      <if test="im != null"> im, </if> 
	      	      <if test="msn != null"> msn, </if> 
	      	      <if test="ainvestamt != null"> ainvestamt, </if> 
	      	      <if test="ainvestfamt != null"> ainvestfamt, </if> 
	      	      <if test="selfdefflag != null"> selfdefflag, </if> 
	      	      <if test="visitfqcy != null"> visitfqcy, </if> 
	      	      <if test="devdirection != null"> devdirection, </if> 
	      	      <if test="saledirection != null"> saledirection, </if> 
	      	      <if test="subsource != null"> subsource, </if> 
	      	      <if test="subsourcetype != null"> subsourcetype, </if> 
	      	      <if test="saleprocess != null"> saleprocess, </if> 
	      	      <if test="mergedconscust != null"> mergedconscust, </if> 
	      	      <if test="addr2 != null"> addr2, </if> 
	      	      <if test="postcode2 != null"> postcode2, </if> 
	      	      <if test="mobile2 != null"> mobile2, </if> 
	      	      <if test="email2 != null"> email2, </if> 
	      	      <if test="knowhowbuy != null"> knowhowbuy, </if> 
	      	      <if test="subknow != null"> subknow, </if> 
	      	      <if test="subknowtype != null"> subknowtype, </if> 
	      	      <if test="buyingprod != null"> buyingprod, </if> 
	      	      <if test="buyedprod != null"> buyedprod, </if> 
	      	      <if test="freeprod != null"> freeprod, </if> 
	      	      <if test="specialflag != null"> specialflag, </if> 
	      	      <if test="dlvymode != null"> dlvymode, </if> 
	      	      <if test="remark != null"> remark, </if> 
	      	      <if test="regdt != null"> regdt, </if> 
	      	      <if test="uddt != null"> uddt, </if> 
	      	      <if test="pririsklevel != null"> pririsklevel, </if> 
	      	      <if test="linkman != null"> linkman, </if> 
	      	      <if test="linktel != null"> linktel, </if> 
	      	      <if test="linkmobile != null"> linkmobile, </if> 
	      	      <if test="linkemail != null"> linkemail, </if> 
	      	      <if test="linkpostcode != null"> linkpostcode, </if> 
	      	      <if test="linkaddr != null"> linkaddr, </if> 
	      	      <if test="capacity != null"> capacity, </if> 
	      	      <if test="activityno != null"> activityno, </if> 
	      	      <if test="partnerno != null"> partnerno, </if> 
	      	      <if test="gpsinvestlevel != null"> gpsinvestlevel, </if> 
	      	      <if test="gpsrisklevel != null"> gpsrisklevel, </if> 
	      	      <if test="isboss != null"> isboss, </if> 
	      	      <if test="financeneed != null"> financeneed, </if> 
	      	      <if test="isjoinclub != null"> isjoinclub, </if> 
	      	      <if test="tmpBeforeinvest != null"> tmp_beforeinvest, </if> 
	      	      <if test="tmpOtherinvest != null"> tmp_otherinvest, </if> 
	      	      <if test="isrisktip != null"> isrisktip, </if> 
	      	      <if test="tempemailflag != null"> tempemailflag, </if> 
	      	      <if test="custsourceremark != null"> custsourceremark, </if> 
	      	      <if test="iswritebook != null"> iswritebook, </if>
	      	      <if test="invsttype != null"> invsttype, </if>  
	      	      <if test="hbvipUsername != null"> hbvipUsername, </if>
	      	      <if test="newsourceno != null"> newsourceno, </if>
	      	      <if test="hboneno != null">hbone_no,</if>
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="conscustno != null"> #{conscustno}, </if> 
	      	      <if test="conscustlvl != null"> #{conscustlvl}, </if> 
	      	      <if test="conscustgrade != null"> #{conscustgrade}, </if> 
	      	      <if test="conscuststatus != null"> #{conscuststatus}, </if> 
	      	      <if test="idtype != null"> #{idtype}, </if> 
	      	      <if test="idno != null and idtype == '0'.toString() and invsttype == '1'.toString()"> UPPER(#{idno}), </if> 	      	      
	      	      <if test="idno != null and (idtype != '0'.toString() or invsttype != '1'.toString())"> #{idno}, </if>
	      	      <if test="custname != null"> #{custname}, </if> 
	      	      <if test="provcode != null"> #{provcode}, </if> 
	      	      <if test="citycode != null"> #{citycode}, </if> 
	      	      <if test="edulevel != null"> #{edulevel}, </if> 
	      	      <if test="vocation != null"> #{vocation}, </if> 
	      	      <if test="inclevel != null"> #{inclevel}, </if> 
	      	      <if test="birthday != null"> #{birthday}, </if> 
	      	      <if test="gender != null"> #{gender}, </if> 
	      	      <if test="married != null"> #{married}, </if> 
	      	      <if test="pincome != null"> #{pincome}, </if> 
	      	      <if test="fincome != null"> #{fincome}, </if> 
	      	      <if test="decisionflag != null"> #{decisionflag}, </if> 
	      	      <if test="interests != null"> #{interests}, </if> 
	      	      <if test="familycondition != null"> #{familycondition}, </if> 
	      	      <if test="contacttime != null"> #{contacttime}, </if> 
	      	      <if test="contactmethod != null"> #{contactmethod}, </if> 
	      	      <if test="sendinfoflag != null"> #{sendinfoflag}, </if> 
	      	      <if test="recvtelflag != null"> #{recvtelflag}, </if> 
	      	      <if test="recvemailflag != null"> #{recvemailflag}, </if> 
	      	      <if test="recvmsgflag != null"> #{recvmsgflag}, </if> 
	      	      <if test="company != null"> #{company}, </if> 
	      	      <if test="risklevel != null"> #{risklevel}, </if> 
	      	      <if test="selfrisklevel != null"> #{selfrisklevel}, </if> 
	      	      <if test="addr != null"> #{addr}, </if> 
	      	      <if test="postcode != null"> #{postcode}, </if> 
	      	      <if test="mobile != null"> #{mobile}, </if> 
	      	      <if test="telno != null"> #{telno}, </if> 
	      	      <if test="fax != null"> #{fax}, </if> 
	      	      <if test="email != null"> #{email}, </if> 
	      	      <if test="hometelno != null"> #{hometelno}, </if> 
	      	      <if test="officetelno != null"> #{officetelno}, </if> 
	      	      <if test="actcode != null"> #{actcode}, </if> 
	      	      <if test="intrcustno != null"> #{intrcustno}, </if> 
	      	      <if test="source != null"> #{source}, </if> 
	      	      <if test="knowchan != null"> #{knowchan}, </if> 
	      	      <if test="otherchan != null"> #{otherchan}, </if> 
	      	      <if test="otherinvest != null"> #{otherinvest}, </if> 
	      	      <if test="salon != null"> #{salon}, </if> 
	      	      <if test="beforeinvest != null"> #{beforeinvest}, </if> 
	      	      <if test="im != null"> #{im}, </if> 
	      	      <if test="msn != null"> #{msn}, </if> 
	      	      <if test="ainvestamt != null"> #{ainvestamt}, </if> 
	      	      <if test="ainvestfamt != null"> #{ainvestfamt}, </if> 
	      	      <if test="selfdefflag != null"> #{selfdefflag}, </if> 
	      	      <if test="visitfqcy != null"> #{visitfqcy}, </if> 
	      	      <if test="devdirection != null"> #{devdirection}, </if> 
	      	      <if test="saledirection != null"> #{saledirection}, </if> 
	      	      <if test="subsource != null"> #{subsource}, </if> 
	      	      <if test="subsourcetype != null"> #{subsourcetype}, </if> 
	      	      <if test="saleprocess != null"> #{saleprocess}, </if> 
	      	      <if test="mergedconscust != null"> #{mergedconscust}, </if> 
	      	      <if test="addr2 != null"> #{addr2}, </if> 
	      	      <if test="postcode2 != null"> #{postcode2}, </if> 
	      	      <if test="mobile2 != null"> #{mobile2}, </if> 
	      	      <if test="email2 != null"> #{email2}, </if> 
	      	      <if test="knowhowbuy != null"> #{knowhowbuy}, </if> 
	      	      <if test="subknow != null"> #{subknow}, </if> 
	      	      <if test="subknowtype != null"> #{subknowtype}, </if> 
	      	      <if test="buyingprod != null"> #{buyingprod}, </if> 
	      	      <if test="buyedprod != null"> #{buyedprod}, </if> 
	      	      <if test="freeprod != null"> #{freeprod}, </if> 
	      	      <if test="specialflag != null"> #{specialflag}, </if> 
	      	      <if test="dlvymode != null"> #{dlvymode}, </if> 
	      	      <if test="remark != null"> #{remark}, </if> 
	      	      <if test="regdt != null"> #{regdt}, </if> 
	      	      <if test="uddt != null"> #{uddt}, </if> 
	      	      <if test="pririsklevel != null"> #{pririsklevel}, </if> 
	      	      <if test="linkman != null"> #{linkman}, </if> 
	      	      <if test="linktel != null"> #{linktel}, </if> 
	      	      <if test="linkmobile != null"> #{linkmobile}, </if> 
	      	      <if test="linkemail != null"> #{linkemail}, </if> 
	      	      <if test="linkpostcode != null"> #{linkpostcode}, </if> 
	      	      <if test="linkaddr != null"> #{linkaddr}, </if> 
	      	      <if test="capacity != null"> #{capacity}, </if> 
	      	      <if test="activityno != null"> #{activityno}, </if> 
	      	      <if test="partnerno != null"> #{partnerno}, </if> 
	      	      <if test="gpsinvestlevel != null"> #{gpsinvestlevel}, </if> 
	      	      <if test="gpsrisklevel != null"> #{gpsrisklevel}, </if> 
	      	      <if test="isboss != null"> #{isboss}, </if> 
	      	      <if test="financeneed != null"> #{financeneed}, </if> 
	      	      <if test="isjoinclub != null"> #{isjoinclub}, </if> 
	      	      <if test="tmpBeforeinvest != null"> #{tmpBeforeinvest}, </if> 
	      	      <if test="tmpOtherinvest != null"> #{tmpOtherinvest}, </if> 
	      	      <if test="isrisktip != null"> #{isrisktip}, </if> 
	      	      <if test="tempemailflag != null"> #{tempemailflag}, </if> 
	      	      <if test="custsourceremark != null"> #{custsourceremark}, </if> 
	      	      <if test="iswritebook != null"> #{iswritebook}, </if> 
	      	      <if test="invsttype != null"> #{invsttype},  </if>  
	      	      <if test="hbvipUsername != null"> #{hbvipUsername},  </if>
	      	      <if test="newsourceno != null"> #{newsourceno},  </if>
	      	      <if test="hboneno != null">#{hboneno},</if>
	               </trim>	
         )      
	  </insert>
	  
	  
	  <update id="updateCmConscust" parameterType="CmConscust" >
	    UPDATE CM_CONSCUST	    
	    <set>
	                <if test="conscustno != null"> conscustno = #{conscustno}, </if>             
                    <if test="conscustlvl != null"> conscustlvl = #{conscustlvl}, </if>             
                    <if test="conscustgrade != null"> conscustgrade = #{conscustgrade}, </if>             
                    <if test="conscuststatus != null"> conscuststatus = #{conscuststatus}, </if>             
                    <if test="idtype != null"> idtype = #{idtype}, </if>             
                    <if test="idno != null and idtype == '0'.toString() and invsttype == '1'.toString()"> idno = UPPER(#{idno}), </if> 	      	      
	      	      	<if test="idno != null and (idtype != '0'.toString() or invsttype != '1'.toString())"> idno = #{idno}, </if>               
                    <if test="custname != null"> custname = #{custname}, </if>             
                    <if test="provcode != null"> provcode = #{provcode}, </if>             
                    <if test="citycode != null"> citycode = #{citycode}, </if>             
                    <if test="edulevel != null"> edulevel = #{edulevel}, </if>             
                    <if test="vocation != null"> vocation = #{vocation}, </if>             
                    <if test="inclevel != null"> inclevel = #{inclevel}, </if>             
                    <if test="birthday != null"> birthday = #{birthday}, </if>             
                    <if test="gender != null"> gender = #{gender}, </if>             
                    <if test="married != null"> married = #{married}, </if>             
                    <if test="pincome != null"> pincome = #{pincome}, </if>             
                    <if test="fincome != null"> fincome = #{fincome}, </if>             
                    <if test="decisionflag != null"> decisionflag = #{decisionflag}, </if>             
                    <if test="interests != null"> interests = #{interests}, </if>             
                    <if test="familycondition != null"> familycondition = #{familycondition}, </if>             
                    <if test="contacttime != null"> contacttime = #{contacttime}, </if>             
                    <if test="contactmethod != null"> contactmethod = #{contactmethod}, </if>             
                    <if test="sendinfoflag != null"> sendinfoflag = #{sendinfoflag}, </if>             
                    <if test="recvtelflag != null"> recvtelflag = #{recvtelflag}, </if>             
                    <if test="recvemailflag != null"> recvemailflag = #{recvemailflag}, </if>             
                    <if test="recvmsgflag != null"> recvmsgflag = #{recvmsgflag}, </if>             
                    <if test="company != null"> company = #{company}, </if>             
                    <if test="risklevel != null"> risklevel = #{risklevel}, </if>             
                    <if test="selfrisklevel != null"> selfrisklevel = #{selfrisklevel}, </if>             
                    <if test="addr != null"> addr = #{addr}, </if>             
                    <if test="postcode != null"> postcode = #{postcode}, </if>             
                    <if test="mobile != null"> mobile = #{mobile}, </if>             
                    <if test="telno != null"> telno = #{telno}, </if>             
                    <if test="fax != null"> fax = #{fax}, </if>             
                    <if test="email != null"> email = #{email}, </if>             
                    <if test="hometelno != null"> hometelno = #{hometelno}, </if>             
                    <if test="officetelno != null"> officetelno = #{officetelno}, </if>             
                    <if test="actcode != null"> actcode = #{actcode}, </if>             
                    <if test="intrcustno != null"> intrcustno = #{intrcustno}, </if>             
                    <if test="source != null"> source = #{source}, </if>             
                    <if test="knowchan != null"> knowchan = #{knowchan}, </if>             
                    <if test="otherchan != null"> otherchan = #{otherchan}, </if>             
                    <if test="otherinvest != null"> otherinvest = #{otherinvest}, </if>             
                    <if test="salon != null"> salon = #{salon}, </if>             
                    <if test="beforeinvest != null"> beforeinvest = #{beforeinvest}, </if>             
                    <if test="im != null"> im = #{im}, </if>             
                    <if test="msn != null"> msn = #{msn}, </if>             
                    <if test="ainvestamt != null"> ainvestamt = #{ainvestamt}, </if>             
                    <if test="ainvestfamt != null"> ainvestfamt = #{ainvestfamt}, </if>             
                    <if test="selfdefflag != null"> selfdefflag = #{selfdefflag}, </if>             
                    <if test="visitfqcy != null"> visitfqcy = #{visitfqcy}, </if>             
                    <if test="devdirection != null"> devdirection = #{devdirection}, </if>             
                    <if test="saledirection != null"> saledirection = #{saledirection}, </if>             
                    <if test="subsource != null"> subsource = #{subsource}, </if>             
                    <if test="subsourcetype != null"> subsourcetype = #{subsourcetype}, </if>             
                    <if test="saleprocess != null"> saleprocess = #{saleprocess}, </if>             
                    <if test="mergedconscust != null"> mergedconscust = #{mergedconscust}, </if>             
                    <if test="addr2 != null"> addr2 = #{addr2}, </if>             
                    <if test="postcode2 != null"> postcode2 = #{postcode2}, </if>             
                    <if test="mobile2 != null"> mobile2 = #{mobile2}, </if>             
                    <if test="email2 != null"> email2 = #{email2}, </if>             
                    <if test="knowhowbuy != null"> knowhowbuy = #{knowhowbuy}, </if>             
                    <if test="subknow != null"> subknow = #{subknow}, </if>             
                    <if test="subknowtype != null"> subknowtype = #{subknowtype}, </if>             
                    <if test="buyingprod != null"> buyingprod = #{buyingprod}, </if>             
                    <if test="buyedprod != null"> buyedprod = #{buyedprod}, </if>             
                    <if test="freeprod != null"> freeprod = #{freeprod}, </if>             
                    <if test="specialflag != null"> specialflag = #{specialflag}, </if>             
                    <if test="dlvymode != null"> dlvymode = #{dlvymode}, </if>             
                    <if test="remark != null"> remark = #{remark}, </if>             
                    <if test="regdt != null"> regdt = #{regdt}, </if>             
                    <if test="uddt != null"> uddt = #{uddt}, </if>             
                    <if test="pririsklevel != null"> pririsklevel = #{pririsklevel}, </if>             
                    <if test="linkman != null"> linkman = #{linkman}, </if>             
                    <if test="linktel != null"> linktel = #{linktel}, </if>             
                    <if test="linkmobile != null"> linkmobile = #{linkmobile}, </if>             
                    <if test="linkemail != null"> linkemail = #{linkemail}, </if>             
                    <if test="linkpostcode != null"> linkpostcode = #{linkpostcode}, </if>             
                    <if test="linkaddr != null"> linkaddr = #{linkaddr}, </if>             
                    <if test="capacity != null"> capacity = #{capacity}, </if>             
                    <if test="activityno != null"> activityno = #{activityno}, </if>             
                    <if test="partnerno != null"> partnerno = #{partnerno}, </if>             
                    <if test="gpsinvestlevel != null"> gpsinvestlevel = #{gpsinvestlevel}, </if>             
                    <if test="gpsrisklevel != null"> gpsrisklevel = #{gpsrisklevel}, </if>             
                    <if test="isboss != null"> isboss = #{isboss}, </if>             
                    <if test="financeneed != null"> financeneed = #{financeneed}, </if>             
                    <if test="isjoinclub != null"> isjoinclub = #{isjoinclub}, </if>             
                    <if test="tmpBeforeinvest != null"> tmp_beforeinvest = #{tmpBeforeinvest}, </if>             
                    <if test="tmpOtherinvest != null"> tmp_otherinvest = #{tmpOtherinvest}, </if>             
                    <if test="isrisktip != null"> isrisktip = #{isrisktip}, </if>             
                    <if test="tempemailflag != null"> tempemailflag = #{tempemailflag}, </if>             
                    <if test="custsourceremark != null"> custsourceremark = #{custsourceremark}, </if>   
                    <if test="iswritebook != null"> iswritebook = #{iswritebook}, </if> 
                    <if test="invsttype != null"> invsttype = #{invsttype},  </if>          
                    <if test="hbvipUsername != null"> hbvipUsername = #{hbvipUsername},  </if>
                    <if test="newsourceno != null"> newsourceno = #{newsourceno},  </if> 
                 </set>
          where conscustno = #{conscustno}
	  </update>
	  
	  
	  <delete id="delCmConscust" parameterType="String">
	    DELETE  from CM_CONSCUST
	    where conscustno = #{conscustno}
	  </delete>
	  
	  
	  <delete id="delListCmConscust" parameterType="String">
	    DELETE  from CM_CONSCUST
	    where id in ($id$)
	        
	  </delete>
	  
	  
	  <select id="listCmConscust" parameterType="Map" resultType="CmConscust" useCache="false">
	    SELECT
	          *
	    FROM CM_CONSCUST
	    where 1=1  
	              <if test="conscustno != null"> AND conscustno = #{conscustno} </if>             
                  <if test="conscustlvl != null"> AND conscustlvl = #{conscustlvl} </if>             
                  <if test="conscustgrade != null"> AND conscustgrade = #{conscustgrade} </if>             
                  <if test="conscuststatus != null"> AND conscuststatus = #{conscuststatus} </if>             
                  <if test="idtype != null"> AND idtype = #{idtype} </if>             
                  <if test="idno != null"> AND idno = #{idno} </if>             
                  <if test="custname != null"> AND custname = #{custname} </if>             
                  <if test="provcode != null"> AND provcode = #{provcode} </if>             
                  <if test="citycode != null"> AND citycode = #{citycode} </if>             
                  <if test="edulevel != null"> AND edulevel = #{edulevel} </if>             
                  <if test="vocation != null"> AND vocation = #{vocation} </if>             
                  <if test="inclevel != null"> AND inclevel = #{inclevel} </if>             
                  <if test="birthday != null"> AND birthday = #{birthday} </if>             
                  <if test="gender != null"> AND gender = #{gender} </if>             
                  <if test="married != null"> AND married = #{married} </if>             
                  <if test="pincome != null"> AND pincome = #{pincome} </if>             
                  <if test="fincome != null"> AND fincome = #{fincome} </if>             
                  <if test="decisionflag != null"> AND decisionflag = #{decisionflag} </if>             
                  <if test="interests != null"> AND interests = #{interests} </if>             
                  <if test="familycondition != null"> AND familycondition = #{familycondition} </if>             
                  <if test="contacttime != null"> AND contacttime = #{contacttime} </if>             
                  <if test="contactmethod != null"> AND contactmethod = #{contactmethod} </if>             
                  <if test="sendinfoflag != null"> AND sendinfoflag = #{sendinfoflag} </if>             
                  <if test="recvtelflag != null"> AND recvtelflag = #{recvtelflag} </if>             
                  <if test="recvemailflag != null"> AND recvemailflag = #{recvemailflag} </if>             
                  <if test="recvmsgflag != null"> AND recvmsgflag = #{recvmsgflag} </if>             
                  <if test="company != null"> AND company = #{company} </if>             
                  <if test="risklevel != null"> AND risklevel = #{risklevel} </if>             
                  <if test="selfrisklevel != null"> AND selfrisklevel = #{selfrisklevel} </if>             
                  <if test="addr != null"> AND addr = #{addr} </if>             
                  <if test="postcode != null"> AND postcode = #{postcode} </if>             
                  <if test="mobile != null"> AND mobile = #{mobile} </if>             
                  <if test="telno != null"> AND telno = #{telno} </if>             
                  <if test="fax != null"> AND fax = #{fax} </if>             
                  <if test="email != null"> AND email = #{email} </if>             
                  <if test="hometelno != null"> AND hometelno = #{hometelno} </if>             
                  <if test="officetelno != null"> AND officetelno = #{officetelno} </if>             
                  <if test="actcode != null"> AND actcode = #{actcode} </if>             
                  <if test="intrcustno != null"> AND intrcustno = #{intrcustno} </if>             
                  <if test="source != null"> AND source = #{source} </if>             
                  <if test="knowchan != null"> AND knowchan = #{knowchan} </if>             
                  <if test="otherchan != null"> AND otherchan = #{otherchan} </if>             
                  <if test="otherinvest != null"> AND otherinvest = #{otherinvest} </if>             
                  <if test="salon != null"> AND salon = #{salon} </if>             
                  <if test="beforeinvest != null"> AND beforeinvest = #{beforeinvest} </if>             
                  <if test="im != null"> AND im = #{im} </if>             
                  <if test="msn != null"> AND msn = #{msn} </if>             
                  <if test="ainvestamt != null"> AND ainvestamt = #{ainvestamt} </if>             
                  <if test="ainvestfamt != null"> AND ainvestfamt = #{ainvestfamt} </if>             
                  <if test="selfdefflag != null"> AND selfdefflag = #{selfdefflag} </if>             
                  <if test="visitfqcy != null"> AND visitfqcy = #{visitfqcy} </if>             
                  <if test="devdirection != null"> AND devdirection = #{devdirection} </if>             
                  <if test="saledirection != null"> AND saledirection = #{saledirection} </if>             
                  <if test="subsource != null"> AND subsource = #{subsource} </if>             
                  <if test="subsourcetype != null"> AND subsourcetype = #{subsourcetype} </if>             
                  <if test="saleprocess != null"> AND saleprocess = #{saleprocess} </if>             
                  <if test="mergedconscust != null"> AND mergedconscust = #{mergedconscust} </if>             
                  <if test="addr2 != null"> AND addr2 = #{addr2} </if>             
                  <if test="postcode2 != null"> AND postcode2 = #{postcode2} </if>             
                  <if test="mobile2 != null"> AND mobile2 = #{mobile2} </if>             
                  <if test="email2 != null"> AND email2 = #{email2} </if>             
                  <if test="knowhowbuy != null"> AND knowhowbuy = #{knowhowbuy} </if>             
                  <if test="subknow != null"> AND subknow = #{subknow} </if>             
                  <if test="subknowtype != null"> AND subknowtype = #{subknowtype} </if>             
                  <if test="buyingprod != null"> AND buyingprod = #{buyingprod} </if>             
                  <if test="buyedprod != null"> AND buyedprod = #{buyedprod} </if>             
                  <if test="freeprod != null"> AND freeprod = #{freeprod} </if>             
                  <if test="specialflag != null"> AND specialflag = #{specialflag} </if>             
                  <if test="dlvymode != null"> AND dlvymode = #{dlvymode} </if>             
                  <if test="remark != null"> AND remark = #{remark} </if>             
                  <if test="regdt != null"> AND regdt = #{regdt} </if>             
                  <if test="uddt != null"> AND uddt = #{uddt} </if>             
                  <if test="pririsklevel != null"> AND pririsklevel = #{pririsklevel} </if>             
                  <if test="linkman != null"> AND linkman = #{linkman} </if>             
                  <if test="linktel != null"> AND linktel = #{linktel} </if>             
                  <if test="linkmobile != null"> AND linkmobile = #{linkmobile} </if>             
                  <if test="linkemail != null"> AND linkemail = #{linkemail} </if>             
                  <if test="linkpostcode != null"> AND linkpostcode = #{linkpostcode} </if>             
                  <if test="linkaddr != null"> AND linkaddr = #{linkaddr} </if>             
                  <if test="capacity != null"> AND capacity = #{capacity} </if>             
                  <if test="activityno != null"> AND activityno = #{activityno} </if>             
                  <if test="partnerno != null"> AND partnerno = #{partnerno} </if>             
                  <if test="gpsinvestlevel != null"> AND gpsinvestlevel = #{gpsinvestlevel} </if>             
                  <if test="gpsrisklevel != null"> AND gpsrisklevel = #{gpsrisklevel} </if>             
                  <if test="isboss != null"> AND isboss = #{isboss} </if>             
                  <if test="financeneed != null"> AND financeneed = #{financeneed} </if>             
                  <if test="isjoinclub != null"> AND isjoinclub = #{isjoinclub} </if>             
                  <if test="tmpBeforeinvest != null"> AND tmp_beforeinvest = #{tmpBeforeinvest} </if>             
                  <if test="tmpOtherinvest != null"> AND tmp_otherinvest = #{tmpOtherinvest} </if>             
                  <if test="isrisktip != null"> AND isrisktip = #{isrisktip} </if>             
                  <if test="tempemailflag != null"> AND tempemailflag = #{tempemailflag} </if>             
                  <if test="custsourceremark != null"> AND custsourceremark = #{custsourceremark} </if> 
                  <if test="invsttype != null"> AND invsttype = #{invsttype} </if>         
                  <if test="hbvipUsername != null"> AND hbvipUsername = #{hbvipUsername} </if>
                  <if test="newsourceno != null"> AND newsourceno = #{newsourceno} </if>     
        	  </select>
	  
	  <select id="listCmConscustByPage" parameterType="Map" resultType="CmConscust" useCache="false">
	    SELECT
	          *
	    FROM CM_CONSCUST
	    where 1=1   
	              <if test="param.conscustno != null"> AND conscustno = #{param.conscustno} </if>             
                  <if test="param.conscustlvl != null"> AND conscustlvl = #{param.conscustlvl} </if>             
                  <if test="param.conscustgrade != null"> AND conscustgrade = #{param.conscustgrade} </if>             
                  <if test="param.conscuststatus != null"> AND conscuststatus = #{param.conscuststatus} </if>             
                  <if test="param.idtype != null"> AND idtype = #{param.idtype} </if>             
                  <if test="param.idno != null"> AND idno = #{param.idno} </if>             
                  <if test="param.custname != null"> AND custname = #{param.custname} </if>             
                  <if test="param.provcode != null"> AND provcode = #{param.provcode} </if>             
                  <if test="param.citycode != null"> AND citycode = #{param.citycode} </if>             
                  <if test="param.edulevel != null"> AND edulevel = #{param.edulevel} </if>             
                  <if test="param.vocation != null"> AND vocation = #{param.vocation} </if>             
                  <if test="param.inclevel != null"> AND inclevel = #{param.inclevel} </if>             
                  <if test="param.birthday != null"> AND birthday = #{param.birthday} </if>             
                  <if test="param.gender != null"> AND gender = #{param.gender} </if>             
                  <if test="param.married != null"> AND married = #{param.married} </if>             
                  <if test="param.pincome != null"> AND pincome = #{param.pincome} </if>             
                  <if test="param.fincome != null"> AND fincome = #{param.fincome} </if>             
                  <if test="param.decisionflag != null"> AND decisionflag = #{param.decisionflag} </if>             
                  <if test="param.interests != null"> AND interests = #{param.interests} </if>             
                  <if test="param.familycondition != null"> AND familycondition = #{param.familycondition} </if>             
                  <if test="param.contacttime != null"> AND contacttime = #{param.contacttime} </if>             
                  <if test="param.contactmethod != null"> AND contactmethod = #{param.contactmethod} </if>             
                  <if test="param.sendinfoflag != null"> AND sendinfoflag = #{param.sendinfoflag} </if>             
                  <if test="param.recvtelflag != null"> AND recvtelflag = #{param.recvtelflag} </if>             
                  <if test="param.recvemailflag != null"> AND recvemailflag = #{param.recvemailflag} </if>             
                  <if test="param.recvmsgflag != null"> AND recvmsgflag = #{param.recvmsgflag} </if>             
                  <if test="param.company != null"> AND company = #{param.company} </if>             
                  <if test="param.risklevel != null"> AND risklevel = #{param.risklevel} </if>             
                  <if test="param.selfrisklevel != null"> AND selfrisklevel = #{param.selfrisklevel} </if>             
                  <if test="param.addr != null"> AND addr = #{param.addr} </if>             
                  <if test="param.postcode != null"> AND postcode = #{param.postcode} </if>             
                  <if test="param.mobile != null"> AND mobile = #{param.mobile} </if>             
                  <if test="param.telno != null"> AND telno = #{param.telno} </if>             
                  <if test="param.fax != null"> AND fax = #{param.fax} </if>             
                  <if test="param.email != null"> AND email = #{param.email} </if>             
                  <if test="param.hometelno != null"> AND hometelno = #{param.hometelno} </if>             
                  <if test="param.officetelno != null"> AND officetelno = #{param.officetelno} </if>             
                  <if test="param.actcode != null"> AND actcode = #{param.actcode} </if>             
                  <if test="param.intrcustno != null"> AND intrcustno = #{param.intrcustno} </if>             
                  <if test="param.source != null"> AND source = #{param.source} </if>             
                  <if test="param.knowchan != null"> AND knowchan = #{param.knowchan} </if>             
                  <if test="param.otherchan != null"> AND otherchan = #{param.otherchan} </if>             
                  <if test="param.otherinvest != null"> AND otherinvest = #{param.otherinvest} </if>             
                  <if test="param.salon != null"> AND salon = #{param.salon} </if>             
                  <if test="param.beforeinvest != null"> AND beforeinvest = #{param.beforeinvest} </if>             
                  <if test="param.im != null"> AND im = #{param.im} </if>             
                  <if test="param.msn != null"> AND msn = #{param.msn} </if>             
                  <if test="param.ainvestamt != null"> AND ainvestamt = #{param.ainvestamt} </if>             
                  <if test="param.ainvestfamt != null"> AND ainvestfamt = #{param.ainvestfamt} </if>             
                  <if test="param.selfdefflag != null"> AND selfdefflag = #{param.selfdefflag} </if>             
                  <if test="param.visitfqcy != null"> AND visitfqcy = #{param.visitfqcy} </if>             
                  <if test="param.devdirection != null"> AND devdirection = #{param.devdirection} </if>             
                  <if test="param.saledirection != null"> AND saledirection = #{param.saledirection} </if>             
                  <if test="param.subsource != null"> AND subsource = #{param.subsource} </if>             
                  <if test="param.subsourcetype != null"> AND subsourcetype = #{param.subsourcetype} </if>             
                  <if test="param.saleprocess != null"> AND saleprocess = #{param.saleprocess} </if>             
                  <if test="param.mergedconscust != null"> AND mergedconscust = #{param.mergedconscust} </if>             
                  <if test="param.addr2 != null"> AND addr2 = #{param.addr2} </if>             
                  <if test="param.postcode2 != null"> AND postcode2 = #{param.postcode2} </if>             
                  <if test="param.mobile2 != null"> AND mobile2 = #{param.mobile2} </if>             
                  <if test="param.email2 != null"> AND email2 = #{param.email2} </if>             
                  <if test="param.knowhowbuy != null"> AND knowhowbuy = #{param.knowhowbuy} </if>             
                  <if test="param.subknow != null"> AND subknow = #{param.subknow} </if>             
                  <if test="param.subknowtype != null"> AND subknowtype = #{param.subknowtype} </if>             
                  <if test="param.buyingprod != null"> AND buyingprod = #{param.buyingprod} </if>             
                  <if test="param.buyedprod != null"> AND buyedprod = #{param.buyedprod} </if>             
                  <if test="param.freeprod != null"> AND freeprod = #{param.freeprod} </if>             
                  <if test="param.specialflag != null"> AND specialflag = #{param.specialflag} </if>             
                  <if test="param.dlvymode != null"> AND dlvymode = #{param.dlvymode} </if>             
                  <if test="param.remark != null"> AND remark = #{param.remark} </if>             
                  <if test="param.regdt != null"> AND regdt = #{param.regdt} </if>             
                  <if test="param.uddt != null"> AND uddt = #{param.uddt} </if>             
                  <if test="param.pririsklevel != null"> AND pririsklevel = #{param.pririsklevel} </if>             
                  <if test="param.linkman != null"> AND linkman = #{param.linkman} </if>             
                  <if test="param.linktel != null"> AND linktel = #{param.linktel} </if>             
                  <if test="param.linkmobile != null"> AND linkmobile = #{param.linkmobile} </if>             
                  <if test="param.linkemail != null"> AND linkemail = #{param.linkemail} </if>             
                  <if test="param.linkpostcode != null"> AND linkpostcode = #{param.linkpostcode} </if>             
                  <if test="param.linkaddr != null"> AND linkaddr = #{param.linkaddr} </if>             
                  <if test="param.capacity != null"> AND capacity = #{param.capacity} </if>             
                  <if test="param.activityno != null"> AND activityno = #{param.activityno} </if>             
                  <if test="param.partnerno != null"> AND partnerno = #{param.partnerno} </if>             
                  <if test="param.gpsinvestlevel != null"> AND gpsinvestlevel = #{param.gpsinvestlevel} </if>             
                  <if test="param.gpsrisklevel != null"> AND gpsrisklevel = #{param.gpsrisklevel} </if>             
                  <if test="param.isboss != null"> AND isboss = #{param.isboss} </if>             
                  <if test="param.financeneed != null"> AND financeneed = #{param.financeneed} </if>             
                  <if test="param.isjoinclub != null"> AND isjoinclub = #{param.isjoinclub} </if>             
                  <if test="param.tmpBeforeinvest != null"> AND tmp_beforeinvest = #{param.tmpBeforeinvest} </if>             
                  <if test="param.tmpOtherinvest != null"> AND tmp_otherinvest = #{param.tmpOtherinvest} </if>             
                  <if test="param.isrisktip != null"> AND isrisktip = #{param.isrisktip} </if>             
                  <if test="param.tempemailflag != null"> AND tempemailflag = #{param.tempemailflag} </if>             
                  <if test="param.custsourceremark != null"> AND custsourceremark = #{param.custsourceremark} </if>
                  <if test="param.invsttype != null"> AND invsttype = #{param.invsttype} </if> 
                  <if test="param.hbvipUsername != null"> AND hbvipUsername = #{param.hbvipUsername} </if>
                  <if test="param.newsourceno != null"> AND newsourceno = #{param.newsourceno} </if>              
        	  </select>
	  
	  <select id="getCmConscustCount" parameterType="Map" resultType="int" useCache="false">
	    SELECT
	          COUNT(*)
	    FROM CM_CONSCUST
	    where 1=1  
	              <if test="conscustno != null"> AND conscustno = #{conscustno} </if>             
                  <if test="conscustlvl != null"> AND conscustlvl = #{conscustlvl} </if>             
                  <if test="conscustgrade != null"> AND conscustgrade = #{conscustgrade} </if>             
                  <if test="conscuststatus != null"> AND conscuststatus = #{conscuststatus} </if>             
                  <if test="idtype != null"> AND idtype = #{idtype} </if>             
                  <if test="idno != null"> AND idno = #{idno} </if>             
                  <if test="custname != null"> AND custname = #{custname} </if>             
                  <if test="provcode != null"> AND provcode = #{provcode} </if>             
                  <if test="citycode != null"> AND citycode = #{citycode} </if>             
                  <if test="edulevel != null"> AND edulevel = #{edulevel} </if>             
                  <if test="vocation != null"> AND vocation = #{vocation} </if>             
                  <if test="inclevel != null"> AND inclevel = #{inclevel} </if>             
                  <if test="birthday != null"> AND birthday = #{birthday} </if>             
                  <if test="gender != null"> AND gender = #{gender} </if>             
                  <if test="married != null"> AND married = #{married} </if>             
                  <if test="pincome != null"> AND pincome = #{pincome} </if>             
                  <if test="fincome != null"> AND fincome = #{fincome} </if>             
                  <if test="decisionflag != null"> AND decisionflag = #{decisionflag} </if>             
                  <if test="interests != null"> AND interests = #{interests} </if>             
                  <if test="familycondition != null"> AND familycondition = #{familycondition} </if>             
                  <if test="contacttime != null"> AND contacttime = #{contacttime} </if>             
                  <if test="contactmethod != null"> AND contactmethod = #{contactmethod} </if>             
                  <if test="sendinfoflag != null"> AND sendinfoflag = #{sendinfoflag} </if>             
                  <if test="recvtelflag != null"> AND recvtelflag = #{recvtelflag} </if>             
                  <if test="recvemailflag != null"> AND recvemailflag = #{recvemailflag} </if>             
                  <if test="recvmsgflag != null"> AND recvmsgflag = #{recvmsgflag} </if>             
                  <if test="company != null"> AND company = #{company} </if>             
                  <if test="risklevel != null"> AND risklevel = #{risklevel} </if>             
                  <if test="selfrisklevel != null"> AND selfrisklevel = #{selfrisklevel} </if>             
                  <if test="addr != null"> AND addr = #{addr} </if>             
                  <if test="postcode != null"> AND postcode = #{postcode} </if>             
                  <if test="mobile != null"> AND mobile = #{mobile} </if>             
                  <if test="telno != null"> AND telno = #{telno} </if>             
                  <if test="fax != null"> AND fax = #{fax} </if>             
                  <if test="email != null"> AND email = #{email} </if>             
                  <if test="hometelno != null"> AND hometelno = #{hometelno} </if>             
                  <if test="officetelno != null"> AND officetelno = #{officetelno} </if>             
                  <if test="actcode != null"> AND actcode = #{actcode} </if>             
                  <if test="intrcustno != null"> AND intrcustno = #{intrcustno} </if>             
                  <if test="source != null"> AND source = #{source} </if>             
                  <if test="knowchan != null"> AND knowchan = #{knowchan} </if>             
                  <if test="otherchan != null"> AND otherchan = #{otherchan} </if>             
                  <if test="otherinvest != null"> AND otherinvest = #{otherinvest} </if>             
                  <if test="salon != null"> AND salon = #{salon} </if>             
                  <if test="beforeinvest != null"> AND beforeinvest = #{beforeinvest} </if>             
                  <if test="im != null"> AND im = #{im} </if>             
                  <if test="msn != null"> AND msn = #{msn} </if>             
                  <if test="ainvestamt != null"> AND ainvestamt = #{ainvestamt} </if>             
                  <if test="ainvestfamt != null"> AND ainvestfamt = #{ainvestfamt} </if>             
                  <if test="selfdefflag != null"> AND selfdefflag = #{selfdefflag} </if>             
                  <if test="visitfqcy != null"> AND visitfqcy = #{visitfqcy} </if>             
                  <if test="devdirection != null"> AND devdirection = #{devdirection} </if>             
                  <if test="saledirection != null"> AND saledirection = #{saledirection} </if>             
                  <if test="subsource != null"> AND subsource = #{subsource} </if>             
                  <if test="subsourcetype != null"> AND subsourcetype = #{subsourcetype} </if>             
                  <if test="saleprocess != null"> AND saleprocess = #{saleprocess} </if>             
                  <if test="mergedconscust != null"> AND mergedconscust = #{mergedconscust} </if>             
                  <if test="addr2 != null"> AND addr2 = #{addr2} </if>             
                  <if test="postcode2 != null"> AND postcode2 = #{postcode2} </if>             
                  <if test="mobile2 != null"> AND mobile2 = #{mobile2} </if>             
                  <if test="email2 != null"> AND email2 = #{email2} </if>             
                  <if test="knowhowbuy != null"> AND knowhowbuy = #{knowhowbuy} </if>             
                  <if test="subknow != null"> AND subknow = #{subknow} </if>             
                  <if test="subknowtype != null"> AND subknowtype = #{subknowtype} </if>             
                  <if test="buyingprod != null"> AND buyingprod = #{buyingprod} </if>             
                  <if test="buyedprod != null"> AND buyedprod = #{buyedprod} </if>             
                  <if test="freeprod != null"> AND freeprod = #{freeprod} </if>             
                  <if test="specialflag != null"> AND specialflag = #{specialflag} </if>             
                  <if test="dlvymode != null"> AND dlvymode = #{dlvymode} </if>             
                  <if test="remark != null"> AND remark = #{remark} </if>             
                  <if test="regdt != null"> AND regdt = #{regdt} </if>             
                  <if test="uddt != null"> AND uddt = #{uddt} </if>             
                  <if test="pririsklevel != null"> AND pririsklevel = #{pririsklevel} </if>             
                  <if test="linkman != null"> AND linkman = #{linkman} </if>             
                  <if test="linktel != null"> AND linktel = #{linktel} </if>             
                  <if test="linkmobile != null"> AND linkmobile = #{linkmobile} </if>             
                  <if test="linkemail != null"> AND linkemail = #{linkemail} </if>             
                  <if test="linkpostcode != null"> AND linkpostcode = #{linkpostcode} </if>             
                  <if test="linkaddr != null"> AND linkaddr = #{linkaddr} </if>             
                  <if test="capacity != null"> AND capacity = #{capacity} </if>             
                  <if test="activityno != null"> AND activityno = #{activityno} </if>             
                  <if test="partnerno != null"> AND partnerno = #{partnerno} </if>             
                  <if test="gpsinvestlevel != null"> AND gpsinvestlevel = #{gpsinvestlevel} </if>             
                  <if test="gpsrisklevel != null"> AND gpsrisklevel = #{gpsrisklevel} </if>             
                  <if test="isboss != null"> AND isboss = #{isboss} </if>             
                  <if test="financeneed != null"> AND financeneed = #{financeneed} </if>             
                  <if test="isjoinclub != null"> AND isjoinclub = #{isjoinclub} </if>             
                  <if test="tmpBeforeinvest != null"> AND tmp_beforeinvest = #{tmpBeforeinvest} </if>             
                  <if test="tmpOtherinvest != null"> AND tmp_otherinvest = #{tmpOtherinvest} </if>             
                  <if test="isrisktip != null"> AND isrisktip = #{isrisktip} </if>             
                  <if test="tempemailflag != null"> AND tempemailflag = #{tempemailflag} </if>             
                  <if test="custsourceremark != null"> AND custsourceremark = #{custsourceremark} </if>  
                  <if test="invsttype != null"> AND invsttype = #{invsttype} </if>          
                  <if test="hbvipUsername != null"> AND hbvipUsername = #{hbvipUsername} </if> 
                  <if test="newsourceno != null"> AND newsourceno = #{newsourceno} </if>  
        	  </select>
	  
	  
</mapper>



