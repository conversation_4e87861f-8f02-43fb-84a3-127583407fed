package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类CsCcUser.java
 * @version 1.0
 */
public class CsAgent implements Serializable {

private static final long serialVersionUID = 1L;

	private String agentid;
	
	private String agentname;
	
	private String groupid;
	
	private String sex;
	
	private String password;
	
	private String role;
	
	private String ip;
	
	public String getAgentid() {
		return this.agentid;
	}

	public void setAgentid(String agentid) {
		this.agentid = agentid;
	}
	
	public String getAgentname() {
		return this.agentname;
	}

	public void setAgentname(String agentname) {
		this.agentname = agentname;
	}
	
	public String getGroupid() {
		return this.groupid;
	}

	public void setGroupid(String groupid) {
		this.groupid = groupid;
	}
	
	public String getSex() {
		return this.sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}
	
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	
	public String getRole() {
		return this.role;
	}

	public void setRole(String role) {
		this.role = role;
	}
	
	public String getIp() {
		return this.ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
