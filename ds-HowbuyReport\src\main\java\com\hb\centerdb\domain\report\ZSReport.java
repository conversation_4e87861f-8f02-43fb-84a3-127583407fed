package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类ZSReport.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class ZSReport implements Serializable {

	private static final long serialVersionUID = 1L;

	private String orgName; //部门名称

    private String outletcode; //部门code

    private String u2name; //区域

	private String consName; //客户名称
	
	private String regDt; //入职日期
	
	private Integer allCust; //总客户数
	
	private Integer tradeCust; //总成交客户数
	
	private Integer howBuy400;//好买资源分配人数
	
	private Integer hz;//合作资源分配人数
	
	private Integer rs2Gd;//RS转高端分配人数
	
	private Integer gd20w;//公募20万转高端
	
	private Integer cj2s0;//二手成交0存量客户
	
	private Integer cj2sf0;//二手成交存量客户

    private Integer zsNum;//折算人数

    private String sDate;//开始计算时间

    private Integer hBRsTotal;//好买资源总成交人数

    private BigDecimal hBRsRate;//好买资源成交率

    private Integer hZRsTotal;//合作资源总成交人数

    private BigDecimal hZRsRate;//合作资源成交率

    private Integer rS2GdTotal;//公募转高端总成交人数

    private BigDecimal rS2GdRate;//公募转高端成交率

    private Integer rS20wTotal;//公募20万总成交人数

    private BigDecimal rS20wRate;//公募20万成交率

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getRegDt() {
		return regDt;
	}

	public void setRegDt(String regDt) {
		this.regDt = regDt;
	}

	public Integer getAllCust() {
		return allCust;
	}

	public void setAllCust(Integer allCust) {
		this.allCust = allCust;
	}

	public Integer getTradeCust() {
		return tradeCust;
	}

	public void setTradeCust(Integer tradeCust) {
		this.tradeCust = tradeCust;
	}

	public Integer getHowBuy400() {
		return howBuy400;
	}

	public void setHowBuy400(Integer howBuy400) {
		this.howBuy400 = howBuy400;
	}

	public Integer getHz() {
		return hz;
	}

	public void setHz(Integer hz) {
		this.hz = hz;
	}

	public Integer getRs2Gd() {
		return rs2Gd;
	}

	public void setRs2Gd(Integer rs2Gd) {
		this.rs2Gd = rs2Gd;
	}

	public Integer getGd20w() {
		return gd20w;
	}

	public void setGd20w(Integer gd20w) {
		this.gd20w = gd20w;
	}

	public Integer getCj2s0() {
		return cj2s0;
	}

	public void setCj2s0(Integer cj2s0) {
		this.cj2s0 = cj2s0;
	}

	public Integer getCj2sf0() {
		return cj2sf0;
	}

	public void setCj2sf0(Integer cj2sf0) {
		this.cj2sf0 = cj2sf0;
	}

    public Integer getZsNum() {
        return zsNum;
    }

    public void setZsNum(Integer zsNum) {
        this.zsNum = zsNum;
    }

    public Integer gethBRsTotal() {
        return hBRsTotal;
    }

    public void sethBRsTotal(Integer hBRsTotal) {
        this.hBRsTotal = hBRsTotal;
    }

    public BigDecimal gethBRsRate() {
        return hBRsRate;
    }

    public void sethBRsRate(BigDecimal hBRsRate) {
        this.hBRsRate = hBRsRate;
    }

    public Integer gethZRsTotal() {
        return hZRsTotal;
    }

    public void sethZRsTotal(Integer hZRsTotal) {
        this.hZRsTotal = hZRsTotal;
    }

    public BigDecimal gethZRsRate() {
        return hZRsRate;
    }

    public void sethZRsRate(BigDecimal hZRsRate) {
        this.hZRsRate = hZRsRate;
    }

    public Integer getrS2GdTotal() {
        return rS2GdTotal;
    }

    public void setrS2GdTotal(Integer rS2GdTotal) {
        this.rS2GdTotal = rS2GdTotal;
    }

    public BigDecimal getrS2GdRate() {
        return rS2GdRate;
    }

    public void setrS2GdRate(BigDecimal rS2GdRate) {
        this.rS2GdRate = rS2GdRate;
    }

    public Integer getrS20wTotal() {
        return rS20wTotal;
    }

    public void setrS20wTotal(Integer rS20wTotal) {
        this.rS20wTotal = rS20wTotal;
    }

    public BigDecimal getrS20wRate() {
        return rS20wRate;
    }

    public void setrS20wRate(BigDecimal rS20wRate) {
        this.rS20wRate = rS20wRate;
    }

    public String getsDate() {
        return sDate;
    }

    public void setsDate(String sDate) {
        this.sDate = sDate;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
