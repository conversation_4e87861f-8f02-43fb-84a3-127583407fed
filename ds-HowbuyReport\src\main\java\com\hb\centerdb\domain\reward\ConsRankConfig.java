package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: gang.zou
 * @Description:配置-投顾职级
 */

public class ConsRankConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键id */
    private Long id;

    /** 投顾层级 */
    private String consLevel;
    /** 投顾层级 名称*/
    private String consLevelName;
    /** 投顾职级 */
    private String consRank;
    /** 投顾职级名称 */
    private String consRankName;
    /** 试用3M */
    private BigDecimal threeMonth;
    /** 试用6M */
    private BigDecimal sixMonth;
    /** 跟踪12M */
    private BigDecimal twelveMonth;
    /** 第2年 */
    private BigDecimal twoYear;
    /** 第3年 */
    private BigDecimal threeYear;
    /** 第4年 */
    private BigDecimal fourYear;
    /** 第5年 */
    private BigDecimal fiveYear;
    /** 第6年 */
    private BigDecimal sixYear;
    /** 第7年 */
    private BigDecimal sevenYear;
    /** 第8年 以及以上*/
    private BigDecimal eightYear;

    //人力资源
    /** 试用3M */
    private BigDecimal threeMonthResource;
    /** 试用6M */
    private BigDecimal sixMonthResource;
    /** 跟踪12M */
    private BigDecimal twelveMonthResource;
    /** 第2年 */
    private BigDecimal twoYearResource;
    /** 第3年 */
    private BigDecimal threeYearResource;
    /** 第4年 */
    private BigDecimal fourYearResource;
    /** 第5年 */
    private BigDecimal fiveYearResource;
    /** 第6年 */
    private BigDecimal sixYearResource;
    /** 第7年 */
    private BigDecimal sevenYearResource;
    /** 第8年 以及以上*/
    private BigDecimal eightYearResource;

    private String creator;
    private String createTime;
    private String modor;
    private String updateTime;

    private BigDecimal performanceD;//存续D

    private String performanceMonth;//司龄月
    private String performanceDate;//当前考核节点
    private String currentFollow;//正式跟踪
    private String formalPerformanceDate;//正式考核节点
    private String currentFormal;//正式-存续d
    private String nextPerformanceDate;//下一考核周期 考核节点
    private String nextCurrentFollow;//下一考核周期 正式跟踪
    private String nextFormalPerformanceDate;//下一周期 正式考核节点
    private String nextCurrentFormal;//下一周期  正式
    private String performanceResource;//合格人力 正式

    private String monthNum;//查询月

    private String userid;//投顾code
    private String regDt;//入职日期

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConsLevel() {
        return consLevel;
    }

    public void setConsLevel(String consLevel) {
        this.consLevel = consLevel;
    }

    public String getConsRank() {
        return consRank;
    }

    public void setConsRank(String consRank) {
        this.consRank = consRank;
    }

    public BigDecimal getThreeMonth() {
        return threeMonth;
    }

    public void setThreeMonth(BigDecimal threeMonth) {
        this.threeMonth = threeMonth;
    }

    public BigDecimal getSixMonth() {
        return sixMonth;
    }

    public void setSixMonth(BigDecimal sixMonth) {
        this.sixMonth = sixMonth;
    }

    public BigDecimal getTwelveMonth() {
        return twelveMonth;
    }

    public void setTwelveMonth(BigDecimal twelveMonth) {
        this.twelveMonth = twelveMonth;
    }

    public BigDecimal getTwoYear() {
        return twoYear;
    }

    public void setTwoYear(BigDecimal twoYear) {
        this.twoYear = twoYear;
    }

    public BigDecimal getThreeYear() {
        return threeYear;
    }

    public void setThreeYear(BigDecimal threeYear) {
        this.threeYear = threeYear;
    }

    public BigDecimal getFourYear() {
        return fourYear;
    }

    public void setFourYear(BigDecimal fourYear) {
        this.fourYear = fourYear;
    }

    public BigDecimal getFiveYear() {
        return fiveYear;
    }

    public void setFiveYear(BigDecimal fiveYear) {
        this.fiveYear = fiveYear;
    }

    public BigDecimal getSixYear() {
        return sixYear;
    }

    public void setSixYear(BigDecimal sixYear) {
        this.sixYear = sixYear;
    }

    public BigDecimal getSevenYear() {
        return sevenYear;
    }

    public void setSevenYear(BigDecimal sevenYear) {
        this.sevenYear = sevenYear;
    }

    public BigDecimal getEightYear() {
        return eightYear;
    }

    public void setEightYear(BigDecimal eightYear) {
        this.eightYear = eightYear;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getConsLevelName() {
        return consLevelName;
    }

    public void setConsLevelName(String consLevelName) {
        this.consLevelName = consLevelName;
    }

    public String getConsRankName() {
        return consRankName;
    }

    public void setConsRankName(String consRankName) {
        this.consRankName = consRankName;
    }

    public String getPerformanceMonth() {
        return performanceMonth;
    }

    public void setPerformanceMonth(String performanceMonth) {
        this.performanceMonth = performanceMonth;
    }

    public BigDecimal getThreeMonthResource() {
        return threeMonthResource;
    }

    public void setThreeMonthResource(BigDecimal threeMonthResource) {
        this.threeMonthResource = threeMonthResource;
    }

    public BigDecimal getSixMonthResource() {
        return sixMonthResource;
    }

    public void setSixMonthResource(BigDecimal sixMonthResource) {
        this.sixMonthResource = sixMonthResource;
    }

    public BigDecimal getTwelveMonthResource() {
        return twelveMonthResource;
    }

    public void setTwelveMonthResource(BigDecimal twelveMonthResource) {
        this.twelveMonthResource = twelveMonthResource;
    }

    public BigDecimal getTwoYearResource() {
        return twoYearResource;
    }

    public void setTwoYearResource(BigDecimal twoYearResource) {
        this.twoYearResource = twoYearResource;
    }

    public BigDecimal getThreeYearResource() {
        return threeYearResource;
    }

    public void setThreeYearResource(BigDecimal threeYearResource) {
        this.threeYearResource = threeYearResource;
    }

    public BigDecimal getFourYearResource() {
        return fourYearResource;
    }

    public void setFourYearResource(BigDecimal fourYearResource) {
        this.fourYearResource = fourYearResource;
    }

    public BigDecimal getFiveYearResource() {
        return fiveYearResource;
    }

    public void setFiveYearResource(BigDecimal fiveYearResource) {
        this.fiveYearResource = fiveYearResource;
    }

    public BigDecimal getSixYearResource() {
        return sixYearResource;
    }

    public void setSixYearResource(BigDecimal sixYearResource) {
        this.sixYearResource = sixYearResource;
    }

    public BigDecimal getSevenYearResource() {
        return sevenYearResource;
    }

    public void setSevenYearResource(BigDecimal sevenYearResource) {
        this.sevenYearResource = sevenYearResource;
    }

    public BigDecimal getEightYearResource() {
        return eightYearResource;
    }

    public void setEightYearResource(BigDecimal eightYearResource) {
        this.eightYearResource = eightYearResource;
    }

    public String getPerformanceDate() {
        return performanceDate;
    }

    public void setPerformanceDate(String performanceDate) {
        this.performanceDate = performanceDate;
    }

    public String getCurrentFollow() {
        return currentFollow;
    }

    public void setCurrentFollow(String currentFollow) {
        this.currentFollow = currentFollow;
    }

    public String getFormalPerformanceDate() {
        return formalPerformanceDate;
    }

    public void setFormalPerformanceDate(String formalPerformanceDate) {
        this.formalPerformanceDate = formalPerformanceDate;
    }

    public String getCurrentFormal() {
        return currentFormal;
    }

    public void setCurrentFormal(String currentFormal) {
        this.currentFormal = currentFormal;
    }

    public String getNextPerformanceDate() {
        return nextPerformanceDate;
    }

    public void setNextPerformanceDate(String nextPerformanceDate) {
        this.nextPerformanceDate = nextPerformanceDate;
    }

    public String getNextCurrentFollow() {
        return nextCurrentFollow;
    }

    public void setNextCurrentFollow(String nextCurrentFollow) {
        this.nextCurrentFollow = nextCurrentFollow;
    }

    public String getNextFormalPerformanceDate() {
        return nextFormalPerformanceDate;
    }

    public void setNextFormalPerformanceDate(String nextFormalPerformanceDate) {
        this.nextFormalPerformanceDate = nextFormalPerformanceDate;
    }

    public String getNextCurrentFormal() {
        return nextCurrentFormal;
    }

    public void setNextCurrentFormal(String nextCurrentFormal) {
        this.nextCurrentFormal = nextCurrentFormal;
    }

    public String getPerformanceResource() {
        return performanceResource;
    }

    public void setPerformanceResource(String performanceResource) {
        this.performanceResource = performanceResource;
    }

    public BigDecimal getPerformanceD() {
        return performanceD;
    }

    public void setPerformanceD(BigDecimal performanceD) {
        this.performanceD = performanceD;
    }

    public String getMonthNum() {
        return monthNum;
    }

    public void setMonthNum(String monthNum) {
        this.monthNum = monthNum;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }
}
