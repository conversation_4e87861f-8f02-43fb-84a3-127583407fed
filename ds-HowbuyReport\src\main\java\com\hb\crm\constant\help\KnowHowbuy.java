/**
 * 
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 知道好买.
 */
public enum KnowHowbuy {
	/**
	 * 媒体看到.
	 */
	Media("1"),
	/**
	 * 搜索引擎.
	 */
	Search("2"),
	/**
	 * 其他网站链接.
	 */
	Link("3"),
	/**
	 * 好买活动.
	 */
	Activity("4"),
	/**
	 * 朋友介绍.
	 */
	Introduction("5"),
	/**
	 * 其他.
	 */
	Other("6")
	;
	private String value;

    public String getValue() {
        return value;
    }

    KnowHowbuy(String value) {
        this.value = value;
    }
public final static Map<KnowHowbuy, String> knowHowbuyEnumMAP;
    
    static{

    	knowHowbuyEnumMAP = new EnumMap<KnowHowbuy, String>(KnowHowbuy.class);
    	
    	knowHowbuyEnumMAP.put(KnowHowbuy.Media, "媒体看到");
    	knowHowbuyEnumMAP.put(KnowHowbuy.Search, "搜索引擎");
    	knowHowbuyEnumMAP.put(KnowHowbuy.Link, "其他网站链接");
    	knowHowbuyEnumMAP.put(KnowHowbuy.Activity, "好买活动");
    	knowHowbuyEnumMAP.put(KnowHowbuy.Introduction, "朋友介绍");
    	knowHowbuyEnumMAP.put(KnowHowbuy.Other, "其他");
    	
    }
    /**
     * 跟据value返回枚举对应的key
     * 
     * @param value
     * @return NotifyErrorCode
     */
    public static KnowHowbuy getEnumMAPKey(String value) {
    	KnowHowbuy tmpKey = null;
        for (KnowHowbuy tmpEnum : KnowHowbuy.values()) {
            if (tmpEnum.value.equals(value)) {
                tmpKey = tmpEnum;
                break;
            }
        }
        return tmpKey;
    }
    /**
     * 返回对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final String value) {
        return KnowHowbuy.knowHowbuyEnumMAP.get( KnowHowbuy.getEnumMAPKey(value));
    }
    public static String getEnumDesc(final KnowHowbuy value) {
        return KnowHowbuy.knowHowbuyEnumMAP.get( value);
    }
}
