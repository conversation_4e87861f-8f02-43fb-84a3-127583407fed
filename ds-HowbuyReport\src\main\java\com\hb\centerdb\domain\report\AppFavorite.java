package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class AppFavorite implements Serializable {

	private static final long serialVersionUID = 1L;

	private String hbonNo; // 一账通号

	private String custNo; // 网站账号

	private String custTradeNo; // 交易账号

	private String consCustNo; // 投顾客户号

	private String consCustName; // 客户姓名

	private String consCode; // 投顾账号

	private String consName; // 投顾姓名

	private String outletName; // 一级部门

	private String orgName; // 二级部门

    private String u1Name; // 一级部门(新)

    private String u2Name; // 二级部门(新)

	private String favoriteObject;

    private String jjdm; // 基金代码

	private String jjjc; // 基金简称

	private String favoriteDate; // 收藏日期

	private String cpfl; // 基金类型

	private String favoriteInfo; // 自选情况

	private int pv30; // 最近30天访问量

	private int pv14; // 最近14天访问量

    private int pv60; // 近60天访问量

    private int pv90; // 近90天访问量

	private String lastFavoriteDay;// 最近一次访问日期

    private String isHold;// 是否持有

    private String fundType;// 基金类型

    private String hmcpx;// 好买产品线

    private String navDt;// 最新净值日期

    private Double nav;// 最新净值

    private Double hb1y;// 近一月业绩
    private Double hb3y;// 近三月业绩
    private Double hb6y;// 近半年业绩
    private Double hb1n;// 近一年业绩

    private String maxDt;// 最新访问日期
    private String addDate;// 添加自选日期

	public String getHbonNo() {
		return hbonNo;
	}

	public void setHbonNo(String hbonNo) {
		this.hbonNo = hbonNo;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getCustTradeNo() {
		return custTradeNo;
	}

	public void setCustTradeNo(String custTradeNo) {
		this.custTradeNo = custTradeNo;
	}

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getConsCustName() {
		return consCustName;
	}

	public void setConsCustName(String consCustName) {
		this.consCustName = consCustName;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getOutletName() {
		return outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getFavoriteObject() {
		return favoriteObject;
	}

	public void setFavoriteObject(String favoriteObject) {
		this.favoriteObject = favoriteObject;
	}

	public String getJjjc() {
		return jjjc;
	}

	public void setJjjc(String jjjc) {
		this.jjjc = jjjc;
	}

	public String getFavoriteDate() {
		return favoriteDate;
	}

	public void setFavoriteDate(String favoriteDate) {
		this.favoriteDate = favoriteDate;
	}

	public String getCpfl() {
		return cpfl;
	}

	public void setCpfl(String cpfl) {
		this.cpfl = cpfl;
	}

	public String getFavoriteInfo() {
		return favoriteInfo;
	}

	public void setFavoriteInfo(String favoriteInfo) {
		this.favoriteInfo = favoriteInfo;
	}

	public int getPv30() {
		return pv30;
	}

	public void setPv30(int pv30) {
		this.pv30 = pv30;
	}

	public int getPv14() {
		return pv14;
	}

	public void setPv14(int pv14) {
		this.pv14 = pv14;
	}

	public String getLastFavoriteDay() {
		return lastFavoriteDay;
	}

	public void setLastFavoriteDay(String lastFavoriteDay) {
		this.lastFavoriteDay = lastFavoriteDay;
	}

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getJjdm() {
        return jjdm;
    }

    public void setJjdm(String jjdm) {
        this.jjdm = jjdm;
    }

    public int getPv60() {
        return pv60;
    }

    public void setPv60(int pv60) {
        this.pv60 = pv60;
    }

    public int getPv90() {
        return pv90;
    }

    public void setPv90(int pv90) {
        this.pv90 = pv90;
    }

    public String getIsHold() {
        return isHold;
    }

    public void setIsHold(String isHold) {
        this.isHold = isHold;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getHmcpx() {
        return hmcpx;
    }

    public void setHmcpx(String hmcpx) {
        this.hmcpx = hmcpx;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public Double getNav() {
        return nav;
    }

    public void setNav(Double nav) {
        this.nav = nav;
    }

    public Double getHb1y() {
        return hb1y;
    }

    public void setHb1y(Double hb1y) {
        this.hb1y = hb1y;
    }

    public Double getHb3y() {
        return hb3y;
    }

    public void setHb3y(Double hb3y) {
        this.hb3y = hb3y;
    }

    public Double getHb6y() {
        return hb6y;
    }

    public void setHb6y(Double hb6y) {
        this.hb6y = hb6y;
    }

    public Double getHb1n() {
        return hb1n;
    }

    public void setHb1n(Double hb1n) {
        this.hb1n = hb1n;
    }

    public String getMaxDt() {
        return maxDt;
    }

    public void setMaxDt(String maxDt) {
        this.maxDt = maxDt;
    }

    public String getAddDate() {
        return addDate;
    }

    public void setAddDate(String addDate) {
        this.addDate = addDate;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
