package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类CmConscust.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CmConscust implements Serializable {

	private static final long serialVersionUID = 1L;

	private String conscustno;

	private String conscustlvl;

	private Integer conscustgrade;

	private String conscuststatus;

	private String idtype;

	private String idno;

	private String custname;

	private String provcode;

	private String citycode;

	private String edulevel;

	private String vocation;

	private String inclevel;

	private String birthday;

	private String gender;

	private String married;

	private String pincome;

	private String fincome;

	private String decisionflag;

	private String interests;

	private String familycondition;

	private String contacttime;

	private String contactmethod;

	private String sendinfoflag;

	private String recvtelflag;

	private String recvemailflag;

	private String recvmsgflag;

	private String company;

	private String risklevel;

	private String selfrisklevel;

	private String addr;

	private String postcode;

	private String mobile;

	private String telno;

	private String fax;

	private String email;

	private String hometelno;

	private String officetelno;

	private String actcode;

	private String intrcustno;

	private String source;

	private String knowchan;

	private String otherchan;

	private String otherinvest;

	private String salon;

	private String beforeinvest;

	private String im;

	private String msn;

	private String ainvestamt;

	private String ainvestfamt;

	private String selfdefflag;

	private String visitfqcy;

	private String devdirection;

	private String saledirection;

	private String subsource;

	private String subsourcetype;

	private String saleprocess;

	private String mergedconscust;

	private String addr2;

	private String postcode2;

	private String mobile2;

	private String email2;

	private String knowhowbuy;

	private String subknow;

	private String subknowtype;

	private String buyingprod;

	private String buyedprod;

	private String freeprod;

	private String specialflag;

	private String dlvymode;

	private String remark;

	private String regdt;

	private String uddt;

	private String pririsklevel;

	private String linkman;

	private String linktel;

	private String linkmobile;

	private String linkemail;

	private String linkpostcode;

	private String linkaddr;

	private String capacity;

	private String activityno;

	private String partnerno;

	private String gpsinvestlevel;

	private String gpsrisklevel;

	private String isboss;

	private String financeneed;

	private String isjoinclub;

	private String tmpBeforeinvest;

	private String tmpOtherinvest;

	private String isrisktip;

	private Integer tempemailflag;

	private String custsourceremark;

	private String iswritebook;

	private String conscode;

	private String creator;

	private String invsttype;
	
	private String hbvipUsername;
	
	private String newsourceno;
	
	private String hboneno;//3.5.8一账通账号 add by yu.zhang
	
	public String getHboneno() {
		return hboneno;
	}

	public void setHboneno(String hboneno) {
		this.hboneno = hboneno;
	}

	public String getInvsttype() {
		return invsttype;
	}

	public void setInvsttype(String invsttype) {
		this.invsttype = invsttype;
	}

	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getConscustlvl() {
		return this.conscustlvl;
	}

	public void setConscustlvl(String conscustlvl) {
		this.conscustlvl = conscustlvl;
	}

	public Integer getConscustgrade() {
		return this.conscustgrade;
	}

	public void setConscustgrade(Integer conscustgrade) {
		this.conscustgrade = conscustgrade;
	}

	public String getConscuststatus() {
		return this.conscuststatus;
	}

	public void setConscuststatus(String conscuststatus) {
		this.conscuststatus = conscuststatus;
	}

	public String getIdtype() {
		return this.idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}

	public String getIdno() {
		return this.idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getCustname() {
		return this.custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getProvcode() {
		return this.provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return this.citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getEdulevel() {
		return this.edulevel;
	}

	public void setEdulevel(String edulevel) {
		this.edulevel = edulevel;
	}

	public String getVocation() {
		return this.vocation;
	}

	public void setVocation(String vocation) {
		this.vocation = vocation;
	}

	public String getInclevel() {
		return this.inclevel;
	}

	public void setInclevel(String inclevel) {
		this.inclevel = inclevel;
	}

	public String getBirthday() {
		return this.birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getMarried() {
		return this.married;
	}

	public void setMarried(String married) {
		this.married = married;
	}

	public String getPincome() {
		return this.pincome;
	}

	public void setPincome(String pincome) {
		this.pincome = pincome;
	}

	public String getFincome() {
		return this.fincome;
	}

	public void setFincome(String fincome) {
		this.fincome = fincome;
	}

	public String getDecisionflag() {
		return this.decisionflag;
	}

	public void setDecisionflag(String decisionflag) {
		this.decisionflag = decisionflag;
	}

	public String getInterests() {
		return this.interests;
	}

	public void setInterests(String interests) {
		this.interests = interests;
	}

	public String getFamilycondition() {
		return this.familycondition;
	}

	public void setFamilycondition(String familycondition) {
		this.familycondition = familycondition;
	}

	public String getContacttime() {
		return this.contacttime;
	}

	public void setContacttime(String contacttime) {
		this.contacttime = contacttime;
	}

	public String getContactmethod() {
		return this.contactmethod;
	}

	public void setContactmethod(String contactmethod) {
		this.contactmethod = contactmethod;
	}

	public String getSendinfoflag() {
		return this.sendinfoflag;
	}

	public void setSendinfoflag(String sendinfoflag) {
		this.sendinfoflag = sendinfoflag;
	}

	public String getRecvtelflag() {
		return this.recvtelflag;
	}

	public void setRecvtelflag(String recvtelflag) {
		this.recvtelflag = recvtelflag;
	}

	public String getRecvemailflag() {
		return this.recvemailflag;
	}

	public void setRecvemailflag(String recvemailflag) {
		this.recvemailflag = recvemailflag;
	}

	public String getRecvmsgflag() {
		return this.recvmsgflag;
	}

	public void setRecvmsgflag(String recvmsgflag) {
		this.recvmsgflag = recvmsgflag;
	}

	public String getCompany() {
		return this.company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getRisklevel() {
		return this.risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}

	public String getSelfrisklevel() {
		return this.selfrisklevel;
	}

	public void setSelfrisklevel(String selfrisklevel) {
		this.selfrisklevel = selfrisklevel;
	}

	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getPostcode() {
		return this.postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}

	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}

	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getHometelno() {
		return this.hometelno;
	}

	public void setHometelno(String hometelno) {
		this.hometelno = hometelno;
	}

	public String getOfficetelno() {
		return this.officetelno;
	}

	public void setOfficetelno(String officetelno) {
		this.officetelno = officetelno;
	}

	public String getActcode() {
		return this.actcode;
	}

	public void setActcode(String actcode) {
		this.actcode = actcode;
	}

	public String getIntrcustno() {
		return this.intrcustno;
	}

	public void setIntrcustno(String intrcustno) {
		this.intrcustno = intrcustno;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getKnowchan() {
		return this.knowchan;
	}

	public void setKnowchan(String knowchan) {
		this.knowchan = knowchan;
	}

	public String getOtherchan() {
		return this.otherchan;
	}

	public void setOtherchan(String otherchan) {
		this.otherchan = otherchan;
	}

	public String getOtherinvest() {
		return this.otherinvest;
	}

	public void setOtherinvest(String otherinvest) {
		this.otherinvest = otherinvest;
	}

	public String getSalon() {
		return this.salon;
	}

	public void setSalon(String salon) {
		this.salon = salon;
	}

	public String getBeforeinvest() {
		return this.beforeinvest;
	}

	public void setBeforeinvest(String beforeinvest) {
		this.beforeinvest = beforeinvest;
	}

	public String getIm() {
		return this.im;
	}

	public void setIm(String im) {
		this.im = im;
	}

	public String getMsn() {
		return this.msn;
	}

	public void setMsn(String msn) {
		this.msn = msn;
	}

	public String getAinvestamt() {
		return this.ainvestamt;
	}

	public void setAinvestamt(String ainvestamt) {
		this.ainvestamt = ainvestamt;
	}

	public String getAinvestfamt() {
		return this.ainvestfamt;
	}

	public void setAinvestfamt(String ainvestfamt) {
		this.ainvestfamt = ainvestfamt;
	}

	public String getSelfdefflag() {
		return this.selfdefflag;
	}

	public void setSelfdefflag(String selfdefflag) {
		this.selfdefflag = selfdefflag;
	}

	public String getVisitfqcy() {
		return this.visitfqcy;
	}

	public void setVisitfqcy(String visitfqcy) {
		this.visitfqcy = visitfqcy;
	}

	public String getDevdirection() {
		return this.devdirection;
	}

	public void setDevdirection(String devdirection) {
		this.devdirection = devdirection;
	}

	public String getSaledirection() {
		return this.saledirection;
	}

	public void setSaledirection(String saledirection) {
		this.saledirection = saledirection;
	}

	public String getSubsource() {
		return this.subsource;
	}

	public void setSubsource(String subsource) {
		this.subsource = subsource;
	}

	public String getSubsourcetype() {
		return this.subsourcetype;
	}

	public void setSubsourcetype(String subsourcetype) {
		this.subsourcetype = subsourcetype;
	}

	public String getSaleprocess() {
		return this.saleprocess;
	}

	public void setSaleprocess(String saleprocess) {
		this.saleprocess = saleprocess;
	}

	public String getMergedconscust() {
		return this.mergedconscust;
	}

	public void setMergedconscust(String mergedconscust) {
		this.mergedconscust = mergedconscust;
	}

	public String getAddr2() {
		return this.addr2;
	}

	public void setAddr2(String addr2) {
		this.addr2 = addr2;
	}

	public String getPostcode2() {
		return this.postcode2;
	}

	public void setPostcode2(String postcode2) {
		this.postcode2 = postcode2;
	}

	public String getMobile2() {
		return this.mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}

	public String getEmail2() {
		return this.email2;
	}

	public void setEmail2(String email2) {
		this.email2 = email2;
	}

	public String getKnowhowbuy() {
		return this.knowhowbuy;
	}

	public void setKnowhowbuy(String knowhowbuy) {
		this.knowhowbuy = knowhowbuy;
	}

	public String getSubknow() {
		return this.subknow;
	}

	public void setSubknow(String subknow) {
		this.subknow = subknow;
	}

	public String getSubknowtype() {
		return this.subknowtype;
	}

	public void setSubknowtype(String subknowtype) {
		this.subknowtype = subknowtype;
	}

	public String getBuyingprod() {
		return this.buyingprod;
	}

	public void setBuyingprod(String buyingprod) {
		this.buyingprod = buyingprod;
	}

	public String getBuyedprod() {
		return this.buyedprod;
	}

	public void setBuyedprod(String buyedprod) {
		this.buyedprod = buyedprod;
	}

	public String getFreeprod() {
		return this.freeprod;
	}

	public void setFreeprod(String freeprod) {
		this.freeprod = freeprod;
	}

	public String getSpecialflag() {
		return this.specialflag;
	}

	public void setSpecialflag(String specialflag) {
		this.specialflag = specialflag;
	}

	public String getDlvymode() {
		return this.dlvymode;
	}

	public void setDlvymode(String dlvymode) {
		this.dlvymode = dlvymode;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRegdt() {
		return this.regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}

	public String getUddt() {
		return this.uddt;
	}

	public void setUddt(String uddt) {
		this.uddt = uddt;
	}

	public String getPririsklevel() {
		return this.pririsklevel;
	}

	public void setPririsklevel(String pririsklevel) {
		this.pririsklevel = pririsklevel;
	}

	public String getLinkman() {
		return this.linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	public String getLinktel() {
		return this.linktel;
	}

	public void setLinktel(String linktel) {
		this.linktel = linktel;
	}

	public String getLinkmobile() {
		return this.linkmobile;
	}

	public void setLinkmobile(String linkmobile) {
		this.linkmobile = linkmobile;
	}

	public String getLinkemail() {
		return this.linkemail;
	}

	public void setLinkemail(String linkemail) {
		this.linkemail = linkemail;
	}

	public String getLinkpostcode() {
		return this.linkpostcode;
	}

	public void setLinkpostcode(String linkpostcode) {
		this.linkpostcode = linkpostcode;
	}

	public String getLinkaddr() {
		return this.linkaddr;
	}

	public void setLinkaddr(String linkaddr) {
		this.linkaddr = linkaddr;
	}

	public String getCapacity() {
		return this.capacity;
	}

	public void setCapacity(String capacity) {
		this.capacity = capacity;
	}

	public String getActivityno() {
		return this.activityno;
	}

	public void setActivityno(String activityno) {
		this.activityno = activityno;
	}

	public String getPartnerno() {
		return this.partnerno;
	}

	public void setPartnerno(String partnerno) {
		this.partnerno = partnerno;
	}

	public String getGpsinvestlevel() {
		return this.gpsinvestlevel;
	}

	public void setGpsinvestlevel(String gpsinvestlevel) {
		this.gpsinvestlevel = gpsinvestlevel;
	}

	public String getGpsrisklevel() {
		return this.gpsrisklevel;
	}

	public void setGpsrisklevel(String gpsrisklevel) {
		this.gpsrisklevel = gpsrisklevel;
	}

	public String getIsboss() {
		return this.isboss;
	}

	public void setIsboss(String isboss) {
		this.isboss = isboss;
	}

	public String getFinanceneed() {
		return this.financeneed;
	}

	public void setFinanceneed(String financeneed) {
		this.financeneed = financeneed;
	}

	public String getIsjoinclub() {
		return this.isjoinclub;
	}

	public void setIsjoinclub(String isjoinclub) {
		this.isjoinclub = isjoinclub;
	}

	public String getTmpBeforeinvest() {
		return this.tmpBeforeinvest;
	}

	public void setTmpBeforeinvest(String tmpBeforeinvest) {
		this.tmpBeforeinvest = tmpBeforeinvest;
	}

	public String getTmpOtherinvest() {
		return this.tmpOtherinvest;
	}

	public void setTmpOtherinvest(String tmpOtherinvest) {
		this.tmpOtherinvest = tmpOtherinvest;
	}

	public String getIsrisktip() {
		return this.isrisktip;
	}

	public void setIsrisktip(String isrisktip) {
		this.isrisktip = isrisktip;
	}

	public Integer getTempemailflag() {
		return this.tempemailflag;
	}

	public void setTempemailflag(Integer tempemailflag) {
		this.tempemailflag = tempemailflag;
	}

	public String getCustsourceremark() {
		return this.custsourceremark;
	}

	public void setCustsourceremark(String custsourceremark) {
		this.custsourceremark = custsourceremark;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getIswritebook() {
		return iswritebook;
	}

	public void setIswritebook(String iswritebook) {
		this.iswritebook = iswritebook;
	}

	public String getHbvipUsername() {
		return hbvipUsername;
	}

	public void setHbvipUsername(String hbvipUsername) {
		this.hbvipUsername = hbvipUsername;
	}

	public String getNewsourceno() {
		return newsourceno;
	}

	public void setNewsourceno(String newsourceno) {
		this.newsourceno = newsourceno;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
