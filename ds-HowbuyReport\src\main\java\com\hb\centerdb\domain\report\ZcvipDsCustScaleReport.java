/**
 * 臻财VIP客户绑定率报表
 * */
package com.hb.centerdb.domain.report;

import java.math.BigDecimal;

public class ZcvipDsCustScaleReport {
	// 投顾姓名
	private String consname;
	// 投顾所属部门
	private String orgname;
	// 总人数（含0存量）
	private Long personCount;
	// 臻财绑定数（含0存量）
	private Long person;
	// 绑定率（含0存量）
	private BigDecimal scale;
	// 总人数（不含0存量）
	private Long personCountA;
	// 臻财绑定数（不含0存量）
	private Long personA;
	// 绑定率（不含0存量）
	private BigDecimal scaleA;
	// 净绑定人数
	private Long personCountB;
	// 净臻财绑定数
	private Long personB;
	// 净绑定率
	private BigDecimal scaleB;

	
	public Long getPersonCountB() {
		return personCountB;
	}

	public void setPersonCountB(Long personCountB) {
		this.personCountB = personCountB;
	}

	public Long getPersonB() {
		return personB;
	}

	public void setPersonB(Long personB) {
		this.personB = personB;
	}

	public BigDecimal getScaleB() {
		return scaleB;
	}

	public void setScaleB(BigDecimal scaleB) {
		this.scaleB = scaleB;
	}

	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}

	public Long getPersonCount() {
		return personCount;
	}

	public void setPersonCount(Long personCount) {
		this.personCount = personCount;
	}

	public Long getPerson() {
		return person;
	}

	public void setPerson(Long person) {
		this.person = person;
	}

	public BigDecimal getScale() {
		return scale;
	}

	public void setScale(BigDecimal scale) {
		this.scale = scale;
	}

	public Long getPersonCountA() {
		return personCountA;
	}

	public void setPersonCountA(Long personCountA) {
		this.personCountA = personCountA;
	}

	public Long getPersonA() {
		return personA;
	}

	public void setPersonA(Long personA) {
		this.personA = personA;
	}

	public BigDecimal getScaleA() {
		return scaleA;
	}

	public void setScaleA(BigDecimal scaleA) {
		this.scaleA = scaleA;
	}

}
