package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类BankSupervise.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class BankSupervise implements Serializable {

private static final long serialVersionUID = 1L;

	
	private String beginDate;// 统计开始日期
	
	private String endDate ;//统计结束日期
	
	private Double NonHBIncome; //非货币类基金入款
	
	private Double NonHBFee; //非货币类基金入款费用
	
	private Double HBIncome; //货币类基金入款
	
	private Double HBFee; //货币类基金入款费用
	
    private Double StockIncome; //股票型基金入款
	
	private Double StockFee; //股票型类基金入款费用
	
	private Integer AuthorizeNum; //人工授权转账笔数
	
	private Double AuthorizeFee; //人工授权转账单笔费用
	
    private Integer FixNum; //固定费用笔数
	
	private Double  FixFee; //固定费用单笔费用

    public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}




	public String getEndDate() {
		return endDate;
	}




	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}




	public Double getNonHBIncome() {
		return NonHBIncome;
	}


	public void setNonHBIncome(Double nonHBIncome) {
		NonHBIncome = nonHBIncome;
	}


	public Double getHBIncome() {
		return HBIncome;
	}




	public void setHBIncome(Double hBIncome) {
		HBIncome = hBIncome;
	}





	public Double getStockIncome() {
		return StockIncome;
	}




	public void setStockIncome(Double stockIncome) {
		StockIncome = stockIncome;
	}





	public Double getNonHBFee() {
		return NonHBFee;
	}




	public void setNonHBFee(Double nonHBFee) {
		NonHBFee = nonHBFee;
	}




	public Double getHBFee() {
		return HBFee;
	}




	public void setHBFee(Double hBFee) {
		HBFee = hBFee;
	}




	public Double getStockFee() {
		return StockFee;
	}




	public void setStockFee(Double stockFee) {
		StockFee = stockFee;
	}




	public Integer getAuthorizeNum() {
		return AuthorizeNum;
	}




	public void setAuthorizeNum(Integer authorizeNum) {
		AuthorizeNum = authorizeNum;
	}




	public Double getAuthorizeFee() {
		return AuthorizeFee;
	}




	public void setAuthorizeFee(Double authorizeFee) {
		AuthorizeFee = authorizeFee;
	}




	public Integer getFixNum() {
		return FixNum;
	}




	public void setFixNum(Integer fixNum) {
		FixNum = fixNum;
	}




	public Double getFixFee() {
		return FixFee;
	}




	public void setFixFee(Double fixFee) {
		FixFee = fixFee;
	}




public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
