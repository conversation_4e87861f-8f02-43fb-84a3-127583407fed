package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: gang.zou
 * @Description:针对存续D对象
 */

public class ConsPerformance implements Serializable {

    private static final long serialVersionUID = 7205883838489525077L;
    /** 主键id */
    private String id;
    /** 投顾层级 */
    private String consLevel;
    /** 投顾层级 名称*/
    private String consLevelName;
    /** 投顾职级 */
    private String consRank;
    /** 投顾职级名称 */
    private String consRankName;

    private String u1name;//中心
    private String u2name;//区域
    private String u3name;//部门

    private String creator;
    private String createTime;
    private String modor;
    private String updateTime;

    private String conscode;//投顾code
    private String consname;//投顾
    private String empNo;//员工编码
    private String regDt;//入职日期
    private String regularDt;//转正日期
    private String workState;//在职状态
    private String periodExplain;//周期说明
    private String periodExplainName;//周期说明
    private String exaimneNode;//考核节点
    private String performanceMonth;//司龄月
    private Integer exaimneMonth;//考核节点司龄月
    private String title;//职位

    private String remark;//备注

    private String startDt;//开始时间
    private String endDt;//结束时间，第13个月开始的时间

    private BigDecimal targetAsset;//目标存量
    private BigDecimal nextTargetAsset;//下一级目标存量
    private BigDecimal resultAsset;//存量结果
    private BigDecimal gapAsset;//目标差距
    private BigDecimal qualifiedResource;//合格人力数
    private BigDecimal qualifiedCompany;//合格分公司数
    private String exaimneResult;//预计考核结果
    private String newRank;//预计新职级
    private BigDecimal preGapAsset;//上一档差距
    private BigDecimal oneTargetAsset;//1级目标存量

    private BigDecimal newNextTargetAsset;//新职级对应的下一级目标存量
    private BigDecimal newIncreaseSuggest;//匀速新增建议

    private String dueDt;//截至时间

    /** 修改职级 */
    private String modRank;
    /** 修改标志 */
    private String modFlag;
    /** 修改结果 */
    private String modResult;

    /** 计入人力 */
    private String calcStaff;
    /** 计入净新增人力 */
    private String calcNewStaff;
    /** 人力组织架构 */
    private String orgCode;
    /** 人力架构到期日 */
    private String orgDueDt;
    /** 是否考核 */
    private String isCalc;
    /** 是否TP */
    private String isTp;
    /** 最终考核结果 */
    private String exaimneEndResult;
    /** 最终新职级 */
    private String newEndRank;
    /** 预计考核结果 备注 */
    private String exaimneRemark;
    /** 非观察人力 */
    private String qualifiedStaff;
    /** 中心(人力) */
    private String u1Staff;
    /** 区域(人力) */
    private String u2Staff;
    /** 分公司(人力) */
    private String u3Staff;
    /** 最终考核修改标志 */
    private String modEndFlag;
    /** 最终考核修改职级 */
    private String modEndRank;
    /** 最终考核修改结果 */
    private String modEndResult;
    /** 非观察人力修改结果 */
    private String modstaffFlag;

    /** 合格人力 */
    private String competentStaff;
    /** 合格人力修改结果 */
    private String modCompetentStaffFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConsLevel() {
        return consLevel;
    }

    public void setConsLevel(String consLevel) {
        this.consLevel = consLevel;
    }

    public String getConsLevelName() {
        return consLevelName;
    }

    public void setConsLevelName(String consLevelName) {
        this.consLevelName = consLevelName;
    }

    public String getConsRank() {
        return consRank;
    }

    public void setConsRank(String consRank) {
        this.consRank = consRank;
    }

    public String getConsRankName() {
        return consRankName;
    }

    public void setConsRankName(String consRankName) {
        this.consRankName = consRankName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }

    public String getRegularDt() {
        return regularDt;
    }

    public void setRegularDt(String regularDt) {
        this.regularDt = regularDt;
    }

    public String getWorkState() {
        return workState;
    }

    public void setWorkState(String workState) {
        this.workState = workState;
    }

    public String getPeriodExplain() {
        return periodExplain;
    }

    public void setPeriodExplain(String periodExplain) {
        this.periodExplain = periodExplain;
    }

    public String getExaimneNode() {
        return exaimneNode;
    }

    public void setExaimneNode(String exaimneNode) {
        this.exaimneNode = exaimneNode;
    }

    public String getPerformanceMonth() {
        return performanceMonth;
    }

    public void setPerformanceMonth(String performanceMonth) {
        this.performanceMonth = performanceMonth;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public BigDecimal getTargetAsset() {
        return targetAsset;
    }

    public void setTargetAsset(BigDecimal targetAsset) {
        this.targetAsset = targetAsset;
    }

    public BigDecimal getResultAsset() {
        return resultAsset;
    }

    public void setResultAsset(BigDecimal resultAsset) {
        this.resultAsset = resultAsset;
    }

    public BigDecimal getGapAsset() {
        return gapAsset;
    }

    public void setGapAsset(BigDecimal gapAsset) {
        this.gapAsset = gapAsset;
    }

    public BigDecimal getQualifiedResource() {
        return qualifiedResource;
    }

    public void setQualifiedResource(BigDecimal qualifiedResource) {
        this.qualifiedResource = qualifiedResource;
    }

    public BigDecimal getQualifiedCompany() {
        return qualifiedCompany;
    }

    public void setQualifiedCompany(BigDecimal qualifiedCompany) {
        this.qualifiedCompany = qualifiedCompany;
    }

    public String getExaimneResult() {
        return exaimneResult;
    }

    public void setExaimneResult(String exaimneResult) {
        this.exaimneResult = exaimneResult;
    }

    public String getNewRank() {
        return newRank;
    }

    public void setNewRank(String newRank) {
        this.newRank = newRank;
    }

    public BigDecimal getPreGapAsset() {
        return preGapAsset;
    }

    public void setPreGapAsset(BigDecimal preGapAsset) {
        this.preGapAsset = preGapAsset;
    }

    public Integer getExaimneMonth() {
        return exaimneMonth;
    }

    public void setExaimneMonth(Integer exaimneMonth) {
        this.exaimneMonth = exaimneMonth;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BigDecimal getNextTargetAsset() {
        return nextTargetAsset;
    }

    public void setNextTargetAsset(BigDecimal nextTargetAsset) {
        this.nextTargetAsset = nextTargetAsset;
    }

    public BigDecimal getNewNextTargetAsset() {
        return newNextTargetAsset;
    }

    public void setNewNextTargetAsset(BigDecimal newNextTargetAsset) {
        this.newNextTargetAsset = newNextTargetAsset;
    }

    public String getPeriodExplainName() {
        return periodExplainName;
    }

    public void setPeriodExplainName(String periodExplainName) {
        this.periodExplainName = periodExplainName;
    }

    public BigDecimal getOneTargetAsset() {
        return oneTargetAsset;
    }

    public void setOneTargetAsset(BigDecimal oneTargetAsset) {
        this.oneTargetAsset = oneTargetAsset;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public BigDecimal getNewIncreaseSuggest() {
        return newIncreaseSuggest;
    }

    public void setNewIncreaseSuggest(BigDecimal newIncreaseSuggest) {
        this.newIncreaseSuggest = newIncreaseSuggest;
    }

    public String getModRank() {
        return modRank;
    }

    public void setModRank(String modRank) {
        this.modRank = modRank;
    }

    public String getModFlag() {
        return modFlag;
    }

    public void setModFlag(String modFlag) {
        this.modFlag = modFlag;
    }

    public String getModResult() {
        return modResult;
    }

    public void setModResult(String modResult) {
        this.modResult = modResult;
    }

    public String getDueDt() {
        return dueDt;
    }

    public void setDueDt(String dueDt) {
        this.dueDt = dueDt;
    }

    public String getCalcStaff() {
        return calcStaff;
    }

    public void setCalcStaff(String calcStaff) {
        this.calcStaff = calcStaff;
    }

    public String getCalcNewStaff() {
        return calcNewStaff;
    }

    public void setCalcNewStaff(String calcNewStaff) {
        this.calcNewStaff = calcNewStaff;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgDueDt() {
        return orgDueDt;
    }

    public void setOrgDueDt(String orgDueDt) {
        this.orgDueDt = orgDueDt;
    }

    public String getIsCalc() {
        return isCalc;
    }

    public void setIsCalc(String isCalc) {
        this.isCalc = isCalc;
    }

    public String getIsTp() {
        return isTp;
    }

    public void setIsTp(String isTp) {
        this.isTp = isTp;
    }

    public String getExaimneEndResult() {
        return exaimneEndResult;
    }

    public void setExaimneEndResult(String exaimneEndResult) {
        this.exaimneEndResult = exaimneEndResult;
    }

    public String getNewEndRank() {
        return newEndRank;
    }

    public void setNewEndRank(String newEndRank) {
        this.newEndRank = newEndRank;
    }

    public String getExaimneRemark() {
        return exaimneRemark;
    }

    public void setExaimneRemark(String exaimneRemark) {
        this.exaimneRemark = exaimneRemark;
    }

    public String getQualifiedStaff() {
        return qualifiedStaff;
    }

    public void setQualifiedStaff(String qualifiedStaff) {
        this.qualifiedStaff = qualifiedStaff;
    }

    public String getU1Staff() {
        return u1Staff;
    }

    public void setU1Staff(String u1Staff) {
        this.u1Staff = u1Staff;
    }

    public String getU2Staff() {
        return u2Staff;
    }

    public void setU2Staff(String u2Staff) {
        this.u2Staff = u2Staff;
    }

    public String getU3Staff() {
        return u3Staff;
    }

    public void setU3Staff(String u3Staff) {
        this.u3Staff = u3Staff;
    }

    public String getModEndFlag() {
        return modEndFlag;
    }

    public void setModEndFlag(String modEndFlag) {
        this.modEndFlag = modEndFlag;
    }

    public String getModEndRank() {
        return modEndRank;
    }

    public void setModEndRank(String modEndRank) {
        this.modEndRank = modEndRank;
    }

    public String getModEndResult() {
        return modEndResult;
    }

    public void setModEndResult(String modEndResult) {
        this.modEndResult = modEndResult;
    }

    public String getModstaffFlag() {
        return modstaffFlag;
    }

    public void setModstaffFlag(String modstaffFlag) {
        this.modstaffFlag = modstaffFlag;
    }

    public String getCompetentStaff() {
        return competentStaff;
    }

    public void setCompetentStaff(String competentStaff) {
        this.competentStaff = competentStaff;
    }

    public String getModCompetentStaffFlag() {
        return modCompetentStaffFlag;
    }

    public void setModCompetentStaffFlag(String modCompetentStaffFlag) {
        this.modCompetentStaffFlag = modCompetentStaffFlag;
    }
}
