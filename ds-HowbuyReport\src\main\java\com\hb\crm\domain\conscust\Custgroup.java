package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类Custgroup.java
 * @version 1.0
 */
public class Custgroup implements Serializable {

	private static final long serialVersionUID = 1L;

	private String groupid;

	private String groupname;

	private String grouplevel;

	private String memo;

	private String orgcode;
	
	private String orgcodez;

	private String delflag;

	private String creator;
	
	private String loginuser;

	private String modifier;

	private String credt;

	private String moddt;

	private String custcount;
	
	private String isic;

	private String isauth;

	private String publictype;
	
	private String isopdb;



	public String getGroupid() {
		return this.groupid;
	}

	public void setGroupid(String groupid) {
		this.groupid = groupid;
	}

	public String getGroupname() {
		return this.groupname;
	}

	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}

	public String getGrouplevel() {
		return this.grouplevel;
	}

	public void setGrouplevel(String grouplevel) {
		this.grouplevel = grouplevel;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getOrgcode() {
		return this.orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public String getDelflag() {
		return this.delflag;
	}

	public void setDelflag(String delflag) {
		this.delflag = delflag;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getLoginuser() {
		return loginuser;
	}

	public void setLoginuser(String loginuser) {
		this.loginuser = loginuser;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getCustcount() {
		return custcount;
	}

	public void setCustcount(String custcount) {
		this.custcount = custcount;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getIsic() {
		return isic;
	}

	public void setIsic(String isic) {
		this.isic = isic;
	}

	public String getOrgcodez() {
		return orgcodez;
	}

	public void setOrgcodez(String orgcodez) {
		this.orgcodez = orgcodez;
	}
	public String getPublictype() {
		return publictype;
	}

	public void setPublictype(String publictype) {
		this.publictype = publictype;
	}

	public String getIsauth() {
		return isauth;
	}

	public void setIsauth(String isauth) {
		this.isauth = isauth;
	}

	public String getIsopdb() {
		return isopdb;
	}

	public void setIsopdb(String isopdb) {
		this.isopdb = isopdb;
	}
}
