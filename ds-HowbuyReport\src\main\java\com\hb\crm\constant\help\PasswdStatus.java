/**
 * 
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 密码状态.
 */
public enum PasswdStatus {
	/**
	 * 正常.
	 */
	Normal("0"),
	/**
	 * 未设置.
	 */
	NoSet("1"),
	/**
	 * 初始.
	 */
	Init("2"),
	/**
	 *挂失.
	 */
	Lost("3"),
	/**
	 * 重置.
	 */
	Reset("4"),
	;
	private String value;

    public String getValue() {
        return value;
    }

    PasswdStatus(String value) {
        this.value = value;
    }

}
