package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类PaymentOrg.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class PaymentOrg implements Serializable {

	private static final long serialVersionUID = 1L;
	
    private String beginDate;// 统计开始日期
	
	private String endDate ;//统计结束日期
	
	private String orgCode;//支付机构

	private String bankCode;//银行编码
	
	private String bankName;//银行名称
	
	private Double NonHBAmt; //非货币类基金交易金额
	
	private Integer NonHBNum; //非货币类基金交易笔数

	private Double NonHBRate; //非货币类基金费率
	
	private Double NonHBFee; //非货币类基金交易费用
	
	private Double HBAmt; //货币类基金交易金额

	private Double HBRate; //货币类基金费率
	
	private Double HBFee; //货币类基金交易费用

	private Integer HBNum;//货币类基金交易笔数
	
    private Double HBRateByNum; //货币类基金按笔费率
	
	private Double HBFeeByNum; //货币类基金交易费用
	
	private Double HBHoldAmt; //货币类基金保有量
	
	private Double HBHoldRate; //货币类基金保有量费率
	
	private Double HBHoldFee; //货币类基金保有量费用
	
	private Double HBBsAmt; //货币类基金保有量费用
	

	public Double getHBBsAmt() {
		return HBBsAmt;
	}

	public void setHBBsAmt(Double hBBsAmt) {
		HBBsAmt = hBBsAmt;
	}

	public Double getNonHBAmt() {
		return NonHBAmt;
	}

	public void setNonHBAmt(Double nonHBAmt) {
		NonHBAmt = nonHBAmt;
	}

	public Double getNonHBRate() {
		return NonHBRate;
	}

	public void setNonHBRate(Double nonHBRate) {
		NonHBRate = nonHBRate;
	}

	public Double getNonHBFee() {
		return NonHBFee;
	}

	public void setNonHBFee(Double nonHBFee) {
		NonHBFee = nonHBFee;
	}

	public Double getHBAmt() {
		return HBAmt;
	}

	public void setHBAmt(Double hBAmt) {
		HBAmt = hBAmt;
	}

	public Double getHBRate() {
		return HBRate;
	}

	public void setHBRate(Double hBRate) {
		HBRate = hBRate;
	}

	public Double getHBFee() {
		return HBFee;
	}

	public void setHBFee(Double hBFee) {
		HBFee = hBFee;
	}

	public Integer getHBNum() {
		return HBNum;
	}

	public void setHBNum(Integer hBNum) {
		HBNum = hBNum;
	}

	public Double getHBRateByNum() {
		return HBRateByNum;
	}

	public void setHBRateByNum(Double hBRateByNum) {
		HBRateByNum = hBRateByNum;
	}

	public Double getHBFeeByNum() {
		return HBFeeByNum;
	}

	public void setHBFeeByNum(Double hBFeeByNum) {
		HBFeeByNum = hBFeeByNum;
	}

	public Double getHBHoldAmt() {
		return HBHoldAmt;
	}

	public void setHBHoldAmt(Double hBHoldAmt) {
		HBHoldAmt = hBHoldAmt;
	}

	public Double getHBHoldRate() {
		return HBHoldRate;
	}

	public void setHBHoldRate(Double hBHoldRate) {
		HBHoldRate = hBHoldRate;
	}

	public Double getHBHoldFee() {
		return HBHoldFee;
	}

	public void setHBHoldFee(Double hBHoldFee) {
		HBHoldFee = hBHoldFee;
	}

public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public Integer getNonHBNum() {
		return NonHBNum;
	}

	public void setNonHBNum(Integer nonHBNum) {
		NonHBNum = nonHBNum;
	}

	
	

}
