package com.hb.crm.domain.report.fundnavreport;

import java.io.Serializable;
import java.math.BigDecimal;

public class SaleProgressReport implements Serializable {
	private String departCode;
	private BigDecimal yearTarget;
	private BigDecimal ytd;
	private String ytdRate;
	private BigDecimal monthTarget;
	private BigDecimal monthOrderTarget;
	private BigDecimal mtd;
	private String mtdRate;
	private int peopleResource;// 人力
	private BigDecimal perPeopleTarget;// 人均产能
	private int activeResource = 0;
	private String activeRate;
	private int firstBuyPeopleNum = 0;
	private int repeatBuyPeopleNum = 0;
	public String getDepartCode() {
		return departCode;
	}
	public void setDepartCode(String departCode) {
		this.departCode = departCode;
	}
	public BigDecimal getYearTarget() {
		return yearTarget;
	}
	public void setYearTarget(BigDecimal yearTarget) {
		this.yearTarget = yearTarget;
	}
	public BigDecimal getYtd() {
		return ytd;
	}
	public void setYtd(BigDecimal ytd) {
		this.ytd = ytd;
	}
	public String getYtdRate() {
		return ytdRate;
	}
	public void setYtdRate(String ytdRate) {
		this.ytdRate = ytdRate;
	}
	public BigDecimal getMonthTarget() {
		return monthTarget;
	}
	public void setMonthTarget(BigDecimal monthTarget) {
		this.monthTarget = monthTarget;
	}
	public BigDecimal getMonthOrderTarget() {
		return monthOrderTarget;
	}
	public void setMonthOrderTarget(BigDecimal monthOrderTarget) {
		this.monthOrderTarget = monthOrderTarget;
	}
	public BigDecimal getMtd() {
		return mtd;
	}
	public void setMtd(BigDecimal mtd) {
		this.mtd = mtd;
	}
	public String getMtdRate() {
		return mtdRate;
	}
	public void setMtdRate(String mtdRate) {
		this.mtdRate = mtdRate;
	}
	public int getPeopleResource() {
		return peopleResource;
	}
	public void setPeopleResource(int peopleResource) {
		this.peopleResource = peopleResource;
	}
	public BigDecimal getPerPeopleTarget() {
		return perPeopleTarget;
	}
	public void setPerPeopleTarget(BigDecimal perPeopleTarget) {
		this.perPeopleTarget = perPeopleTarget;
	}
	public int getActiveResource() {
		return activeResource;
	}
	public void setActiveResource(int activeResource) {
		this.activeResource = activeResource;
	}
	public String getActiveRate() {
		return activeRate;
	}
	public void setActiveRate(String activeRate) {
		this.activeRate = activeRate;
	}
	public int getFirstBuyPeopleNum() {
		return firstBuyPeopleNum;
	}
	public void setFirstBuyPeopleNum(int firstBuyPeopleNum) {
		this.firstBuyPeopleNum = firstBuyPeopleNum;
	}
	public int getRepeatBuyPeopleNum() {
		return repeatBuyPeopleNum;
	}
	public void setRepeatBuyPeopleNum(int repeatBuyPeopleNum) {
		this.repeatBuyPeopleNum = repeatBuyPeopleNum;
	}
}
