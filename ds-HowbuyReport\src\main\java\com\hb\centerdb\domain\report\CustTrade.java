package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

public class CustTrade implements Serializable {

    private static final long serialVersionUID = -813910193990180039L;

    private String hboneNo;//一账通号
    private String conscustNo;//投顾客户号
    private String conscustName;//投顾客户姓名
    private String mBusiCode;//中台业务代码
    private String mBusiName;//中台业务代码
    private String tradeType;//交易类型
    private String fundCode; //基金code
    private String fundName; //基金名称
    private String managerMan; //基金管理人
    private String orderStatus; //订单状态
    private String ackDt; //确认日期
    private String appDt; //申请日期
    private String navDt; //净值日期

    private BigDecimal nav =new BigDecimal(0); //净值

    private BigDecimal ackAmt =new BigDecimal(0); //确认金额
    private BigDecimal ackVol =new BigDecimal(0); //确认份额

    private BigDecimal appVol =new BigDecimal(0); //申请份额
    private BigDecimal appAmt =new BigDecimal(0); //申请金额(元)
    private BigDecimal avgAmt =new BigDecimal(0); //客均金额(元)
    private BigDecimal custNum =new BigDecimal(0); //投顾名下发生交易的客户数

    private String productClass1;//一级分类
    private String productClass2;//二级分类
    private String productClass3;//三级分类
    private String bqxlmc; //一级策略

    private String consCode;
    private String consName;
    private String u1code;
    private String u1name;
    private String u2code;
    private String u2name;
    private String u3code;
    private String u3name;

    private BigDecimal reduceAmtRmb =new BigDecimal(0); //划出市值  或者赎回量
    private BigDecimal increaseAmtRmb=new BigDecimal(0); //划入市值  或者申购量
    private BigDecimal netIncreaseAmtRmb=new BigDecimal(0); //净划入市值 或者 净申购量

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getConscustName() {
        return conscustName;
    }

    public void setConscustName(String conscustName) {
        this.conscustName = conscustName;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getManagerMan() {
        return managerMan;
    }

    public void setManagerMan(String managerMan) {
        this.managerMan = managerMan;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public BigDecimal getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }

    public BigDecimal getAckVol() {
        return ackVol;
    }

    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }

    public String getProductClass1() {
        return productClass1;
    }

    public void setProductClass1(String productClass1) {
        this.productClass1 = productClass1;
    }

    public String getProductClass2() {
        return productClass2;
    }

    public void setProductClass2(String productClass2) {
        this.productClass2 = productClass2;
    }

    public String getProductClass3() {
        return productClass3;
    }

    public void setProductClass3(String productClass3) {
        this.productClass3 = productClass3;
    }

    public String getBqxlmc() {
        return bqxlmc;
    }

    public void setBqxlmc(String bqxlmc) {
        this.bqxlmc = bqxlmc;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getU1code() {
        return u1code;
    }

    public void setU1code(String u1code) {
        this.u1code = u1code;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2code() {
        return u2code;
    }

    public void setU2code(String u2code) {
        this.u2code = u2code;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3code() {
        return u3code;
    }

    public void setU3code(String u3code) {
        this.u3code = u3code;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public BigDecimal getReduceAmtRmb() {
        return reduceAmtRmb;
    }

    public void setReduceAmtRmb(BigDecimal reduceAmtRmb) {
        this.reduceAmtRmb = reduceAmtRmb;
    }

    public BigDecimal getIncreaseAmtRmb() {
        return increaseAmtRmb;
    }

    public void setIncreaseAmtRmb(BigDecimal increaseAmtRmb) {
        this.increaseAmtRmb = increaseAmtRmb;
    }

    public BigDecimal getNetIncreaseAmtRmb() {
        return netIncreaseAmtRmb;
    }

    public void setNetIncreaseAmtRmb(BigDecimal netIncreaseAmtRmb) {
        this.netIncreaseAmtRmb = netIncreaseAmtRmb;
    }

    public String getmBusiName() {
        return mBusiName;
    }

    public void setmBusiName(String mBusiName) {
        this.mBusiName = mBusiName;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAvgAmt() {
        return avgAmt;
    }

    public void setAvgAmt(BigDecimal avgAmt) {
        this.avgAmt = avgAmt;
    }

    public BigDecimal getCustNum() {
        return custNum;
    }

    public void setCustNum(BigDecimal custNum) {
        this.custNum = custNum;
    }
}
