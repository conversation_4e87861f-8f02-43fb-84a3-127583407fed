package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class OrgBalDtl implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custNo; // 客户号

	private String custName; // 客户名称

	private String idNo; // 证件号码

	private String fundTxAcctNo; // 基金账号

	private String fundCode; // 基金代码

	private String fundType; // 基金类型

	private String fundAttr; // 基金简称

	private String dfltDivMode; // 分红方式

	private Double balanceVol = 0d; // 总持有份额

	private String tradeDt; // 统计日期

	private Double Nav = 0d; // 基金市值

	private Double marketAmt = 0d; // 基金市值

	private Double income = 0d; // 预计未结转收益

	private String fundManName; // 基金管理人

	private String navDt; // 净值日期

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getFundTxAcctNo() {
		return fundTxAcctNo;
	}

	public void setFundTxAcctNo(String fundTxAcctNo) {
		this.fundTxAcctNo = fundTxAcctNo;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundType() {
		return fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}

	public String getFundAttr() {
		return fundAttr;
	}

	public void setFundAttr(String fundAttr) {
		this.fundAttr = fundAttr;
	}

	public String getDfltDivMode() {
		return dfltDivMode;
	}

	public void setDfltDivMode(String dfltDivMode) {
		this.dfltDivMode = dfltDivMode;
	}

	public Double getBalanceVol() {
		return balanceVol;
	}

	public void setBalanceVol(Double balanceVol) {
		this.balanceVol = balanceVol;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public Double getNav() {
		return Nav;
	}

	public void setNav(Double nav) {
		Nav = nav;
	}

	public Double getMarketAmt() {
		return marketAmt;
	}

	public void setMarketAmt(Double marketAmt) {
		this.marketAmt = marketAmt;
	}

	public Double getIncome() {
		return income;
	}

	public void setIncome(Double income) {
		this.income = income;
	}

	public String getFundManName() {
		return fundManName;
	}

	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}

	public String getNavDt() {
		return navDt;
	}

	public void setNavDt(String navDt) {
		this.navDt = navDt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
