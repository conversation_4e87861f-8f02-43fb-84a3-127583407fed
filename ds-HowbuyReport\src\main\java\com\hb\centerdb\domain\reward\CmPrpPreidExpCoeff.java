package com.hb.centerdb.domain.reward;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: 预约各项系数调整
 * @reason:
 * @Date: 2020/9/18 18:12
 */
public class CmPrpPreidExpCoeff implements Serializable {

    private static final long serialVersionUID = 1L;

    private String preId;
    private String accountDt;
    private BigDecimal foldCoeff;
    private BigDecimal commissionRate;
    private BigDecimal annuallySetAward;
    private BigDecimal secondStockCoeff;
    private BigDecimal stockFeeA;
    private BigDecimal stockFeeB;
    private BigDecimal operFoldCoeff;
    /** 添加客户来源系数相关修改项 */
    private BigDecimal sourceCoeff;
    private BigDecimal zbCoeff;
    private BigDecimal manageCoeff;

    /**
     * 极差系数
     */
    private BigDecimal unqualifiedCoeff;
    /**
     * 折扣金额
     */
    private BigDecimal beforetaxamt;

    private String cxa;
    private String cxb;

    private String creator;
    private String createTime;
    private String modor;
    private String updateTime;

    public String getPreId() {
        return preId;
    }

    public void setPreId(String preId) {
        this.preId = preId;
    }

    public String getAccountDt() {
        return accountDt;
    }

    public void setAccountDt(String accountDt) {
        this.accountDt = accountDt;
    }

    public BigDecimal getFoldCoeff() {
        return foldCoeff;
    }

    public void setFoldCoeff(BigDecimal foldCoeff) {
        this.foldCoeff = foldCoeff;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getAnnuallySetAward() {
        return annuallySetAward;
    }

    public void setAnnuallySetAward(BigDecimal annuallySetAward) {
        this.annuallySetAward = annuallySetAward;
    }

    public BigDecimal getSecondStockCoeff() {
        return secondStockCoeff;
    }

    public void setSecondStockCoeff(BigDecimal secondStockCoeff) {
        this.secondStockCoeff = secondStockCoeff;
    }

    public BigDecimal getStockFeeA() {
        return stockFeeA;
    }

    public void setStockFeeA(BigDecimal stockFeeA) {
        this.stockFeeA = stockFeeA;
    }

    public BigDecimal getStockFeeB() {
        return stockFeeB;
    }

    public void setStockFeeB(BigDecimal stockFeeB) {
        this.stockFeeB = stockFeeB;
    }

    public BigDecimal getOperFoldCoeff() {
        return operFoldCoeff;
    }

    public void setOperFoldCoeff(BigDecimal operFoldCoeff) {
        this.operFoldCoeff = operFoldCoeff;
    }

    public BigDecimal getSourceCoeff() {
        return sourceCoeff;
    }

    public void setSourceCoeff(BigDecimal sourceCoeff) {
        this.sourceCoeff = sourceCoeff;
    }

    public BigDecimal getZbCoeff() {
        return zbCoeff;
    }

    public void setZbCoeff(BigDecimal zbCoeff) {
        this.zbCoeff = zbCoeff;
    }

    public BigDecimal getManageCoeff() {
        return manageCoeff;
    }

    public void setManageCoeff(BigDecimal manageCoeff) {
        this.manageCoeff = manageCoeff;
    }

    public BigDecimal getUnqualifiedCoeff() {
        return unqualifiedCoeff;
    }

    public void setUnqualifiedCoeff(BigDecimal unqualifiedCoeff) {
        this.unqualifiedCoeff = unqualifiedCoeff;
    }

    public BigDecimal getBeforetaxamt() {
        return beforetaxamt;
    }

    public void setBeforetaxamt(BigDecimal beforetaxamt) {
        this.beforetaxamt = beforetaxamt;
    }

    public String getCxa() {
        return cxa;
    }

    public void setCxa(String cxa) {
        this.cxa = cxa;
    }

    public String getCxb() {
        return cxb;
    }

    public void setCxb(String cxb) {
        this.cxb = cxb;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
