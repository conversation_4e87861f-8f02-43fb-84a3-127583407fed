package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;


public class AssetVaild implements Serializable{


    private static final long serialVersionUID = 3646038530780217445L;

    private String u1name;//一级部门

    private String u2name;//二级部门

    private String u3name;//三级部门

    private String outletcode;//部门

    private String conscode;//投顾code

    private String consname;//投顾

    private String conscustno;//投顾客户号

    private String custName;//客户姓名

    private String startdt;//分配时间

    private String firstAckDt;//首次交易时间

    private BigDecimal assignBuy=new BigDecimal(0);//分配后的购买量

    private BigDecimal assignRedem=new BigDecimal(0);//分配后的赎回量

    private BigDecimal assignBuyDiscount=new BigDecimal(0);//分配后的购买折标量

    private BigDecimal marketCapAssign=new BigDecimal(0);//分配时的存量

    private BigDecimal marketCapAssign2=new BigDecimal(0);//分配时存量的20%

    private BigDecimal activeDiffer=new BigDecimal(0);//激活差额

    private BigDecimal buyDiscountDiffer=new BigDecimal(0);//折标购买量差额

    private String isActive;//是否激活

    private String fundCode;//产品code

    private String fundName;//产品明细

    private String fundType;//产品类型

    private String busiCode;//业务类型code

    private String busiCodeName;//业务类型

    private String ackDt;//购买日期

    private String currency;//币种

    private BigDecimal ackVol=new BigDecimal(0);//购买份额

    private BigDecimal ackVolSum=new BigDecimal(0);//当前份额

    private BigDecimal nav=new BigDecimal(0);//基金净值

    private String navDt;//净值日期

    private BigDecimal marketCapSum=new BigDecimal(0);//当前市值

    private BigDecimal assignYear=new BigDecimal(0);//分配年限

    private BigDecimal buyYear=new BigDecimal(0);//购买年限

    private String custState;//客户状态

    private String isYear;//是否满一年

    private BigDecimal secondStockCoeff=new BigDecimal(0);//二级存量折标
    private BigDecimal accureAsset=new BigDecimal(0);//二级存量
    private BigDecimal valiedAsset=new BigDecimal(0);//二级有效存量

    private BigDecimal valiedAsset1=new BigDecimal(0);//自有 -激活-满12个月
    private BigDecimal valiedAsset2=new BigDecimal(0);//自有 -激活-未满12个月
    private BigDecimal valiedAsset3=new BigDecimal(0);//划转 -激活-满12个月
    private BigDecimal valiedAsset4=new BigDecimal(0);//划转 -激活-未满12个月
    private BigDecimal valiedAsset5=new BigDecimal(0);//划转 -未激活-满12个月
    private BigDecimal valiedAsset6=new BigDecimal(0);//划转 -未激活-未满12个月


    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getStartdt() {
        return startdt;
    }

    public void setStartdt(String startdt) {
        this.startdt = startdt;
    }

    public String getFirstAckDt() {
        return firstAckDt;
    }

    public void setFirstAckDt(String firstAckDt) {
        this.firstAckDt = firstAckDt;
    }

    public BigDecimal getAssignBuy() {
        return assignBuy;
    }

    public void setAssignBuy(BigDecimal assignBuy) {
        this.assignBuy = assignBuy;
    }

    public BigDecimal getAssignRedem() {
        return assignRedem;
    }

    public void setAssignRedem(BigDecimal assignRedem) {
        this.assignRedem = assignRedem;
    }

    public BigDecimal getAssignBuyDiscount() {
        return assignBuyDiscount;
    }

    public void setAssignBuyDiscount(BigDecimal assignBuyDiscount) {
        this.assignBuyDiscount = assignBuyDiscount;
    }

    public BigDecimal getMarketCapAssign() {
        return marketCapAssign;
    }

    public void setMarketCapAssign(BigDecimal marketCapAssign) {
        this.marketCapAssign = marketCapAssign;
    }

    public BigDecimal getMarketCapAssign2() {
        return marketCapAssign2;
    }

    public void setMarketCapAssign2(BigDecimal marketCapAssign2) {
        this.marketCapAssign2 = marketCapAssign2;
    }

    public BigDecimal getActiveDiffer() {
        return activeDiffer;
    }

    public void setActiveDiffer(BigDecimal activeDiffer) {
        this.activeDiffer = activeDiffer;
    }

    public BigDecimal getBuyDiscountDiffer() {
        return buyDiscountDiffer;
    }

    public void setBuyDiscountDiffer(BigDecimal buyDiscountDiffer) {
        this.buyDiscountDiffer = buyDiscountDiffer;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getBusiCodeName() {
        return busiCodeName;
    }

    public void setBusiCodeName(String busiCodeName) {
        this.busiCodeName = busiCodeName;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getAckVol() {
        return ackVol;
    }

    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }

    public BigDecimal getAckVolSum() {
        return ackVolSum;
    }

    public void setAckVolSum(BigDecimal ackVolSum) {
        this.ackVolSum = ackVolSum;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public BigDecimal getMarketCapSum() {
        return marketCapSum;
    }

    public void setMarketCapSum(BigDecimal marketCapSum) {
        this.marketCapSum = marketCapSum;
    }

    public BigDecimal getAssignYear() {
        return assignYear;
    }

    public void setAssignYear(BigDecimal assignYear) {
        this.assignYear = assignYear;
    }

    public BigDecimal getBuyYear() {
        return buyYear;
    }

    public void setBuyYear(BigDecimal buyYear) {
        this.buyYear = buyYear;
    }

    public String getCustState() {
        return custState;
    }

    public void setCustState(String custState) {
        this.custState = custState;
    }

    public String getIsYear() {
        return isYear;
    }

    public void setIsYear(String isYear) {
        this.isYear = isYear;
    }

    public BigDecimal getSecondStockCoeff() {
        return secondStockCoeff;
    }

    public void setSecondStockCoeff(BigDecimal secondStockCoeff) {
        this.secondStockCoeff = secondStockCoeff;
    }

    public BigDecimal getAccureAsset() {
        return accureAsset;
    }

    public void setAccureAsset(BigDecimal accureAsset) {
        this.accureAsset = accureAsset;
    }

    public BigDecimal getValiedAsset() {
        return valiedAsset;
    }

    public void setValiedAsset(BigDecimal valiedAsset) {
        this.valiedAsset = valiedAsset;
    }

    public BigDecimal getValiedAsset1() {
        return valiedAsset1;
    }

    public void setValiedAsset1(BigDecimal valiedAsset1) {
        this.valiedAsset1 = valiedAsset1;
    }

    public BigDecimal getValiedAsset2() {
        return valiedAsset2;
    }

    public void setValiedAsset2(BigDecimal valiedAsset2) {
        this.valiedAsset2 = valiedAsset2;
    }

    public BigDecimal getValiedAsset3() {
        return valiedAsset3;
    }

    public void setValiedAsset3(BigDecimal valiedAsset3) {
        this.valiedAsset3 = valiedAsset3;
    }

    public BigDecimal getValiedAsset4() {
        return valiedAsset4;
    }

    public void setValiedAsset4(BigDecimal valiedAsset4) {
        this.valiedAsset4 = valiedAsset4;
    }

    public BigDecimal getValiedAsset5() {
        return valiedAsset5;
    }

    public void setValiedAsset5(BigDecimal valiedAsset5) {
        this.valiedAsset5 = valiedAsset5;
    }

    public BigDecimal getValiedAsset6() {
        return valiedAsset6;
    }

    public void setValiedAsset6(BigDecimal valiedAsset6) {
        this.valiedAsset6 = valiedAsset6;
    }

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }
}
