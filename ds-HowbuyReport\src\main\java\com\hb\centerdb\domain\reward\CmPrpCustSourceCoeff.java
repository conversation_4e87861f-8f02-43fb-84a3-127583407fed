package com.hb.centerdb.domain.reward;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: shuai.zhang
 * @Date: 2021/9/1
 * @Description:配置-客户来源系数
 */

public class CmPrpCustSourceCoeff implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键id */
    private Long id;
    /** 来源类型 */
    private String sourceType;
    /** 来源系数起点(0.2、1) */
    private String startPoint;
    /** 客户折标系数 */
    private BigDecimal zbCoeff;
    /** 管理系数 */
    private BigDecimal manageCoeff;
    /** 存续A 可选项有：公司资源、投顾资源 */
    private String cxa;
    /** 存续B 可选项有：公司资源、投顾资源 */
    private String cxb;
    /**
     * 起始日期
     */
    private String startDt;
    /**
     * 结束日期
     */
    private String endDt;

    private String creator;

    private Date createTime;

    private String modor;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public BigDecimal getZbCoeff() {
        return zbCoeff;
    }

    public void setZbCoeff(BigDecimal zbCoeff) {
        this.zbCoeff = zbCoeff;
    }

    public BigDecimal getManageCoeff() {
        return manageCoeff;
    }

    public void setManageCoeff(BigDecimal manageCoeff) {
        this.manageCoeff = manageCoeff;
    }

    public String getCxa() {
        return cxa;
    }

    public void setCxa(String cxa) {
        this.cxa = cxa;
    }

    public String getCxb() {
        return cxb;
    }

    public void setCxb(String cxb) {
        this.cxb = cxb;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
