package com.hb.crm.domain.agent;

public class TaskDisposeVO {

	private int taskType;
	private String taskTypeName;
	private int unDistribute_unDispose;  //未分配未处理
	private int unDistribute_aDispose;  //未分配再处理
	private int distributed_unDispose;  //已分配未处理
	private int distributed_aDispose;  //已分配再处理
	
	private int myDistributed_unDispose;  //我的任务 未处理数
	private int myDistributed_aDispose;   //我的任务 再处理数
	private int myDistributed_disposed;    //我的任务 已处理数

	public TaskDisposeVO(){
		this.unDistribute_unDispose = 0;
		this.unDistribute_aDispose = 0;
		this.distributed_unDispose = 0;
		this.distributed_aDispose = 0;
		
		this.myDistributed_aDispose = 0;
		this.myDistributed_disposed = 0;
		this.myDistributed_unDispose = 0;
	}
	
	public int getTaskType() {
		return taskType;
	}
	public void setTaskType(int taskType) {
		this.taskType = taskType;
	}
	public int getUnDistribute_unDispose() {
		return unDistribute_unDispose;
	}
	public void setUnDistribute_unDispose(int unDistribute_unDispose) {
		this.unDistribute_unDispose = unDistribute_unDispose;
	}
	public int getUnDistribute_aDispose() {
		return unDistribute_aDispose;
	}
	public void setUnDistribute_aDispose(int unDistribute_aDispose) {
		this.unDistribute_aDispose = unDistribute_aDispose;
	}
	public int getDistributed_unDispose() {
		return distributed_unDispose;
	}
	public void setDistributed_unDispose(int distributed_unDispose) {
		this.distributed_unDispose = distributed_unDispose;
	}
	public int getDistributed_aDispose() {
		return distributed_aDispose;
	}
	public void setDistributed_aDispose(int distributed_aDispose) {
		this.distributed_aDispose = distributed_aDispose;
	}

	public String getTaskTypeName() {
		return taskTypeName;
	}

	public void setTaskTypeName(String taskTypeName) {
		this.taskTypeName = taskTypeName;
	}

	public int getMyDistributed_unDispose() {
		return myDistributed_unDispose;
	}

	public void setMyDistributed_unDispose(int myDistributed_unDispose) {
		this.myDistributed_unDispose = myDistributed_unDispose;
	}

	public int getMyDistributed_aDispose() {
		return myDistributed_aDispose;
	}

	public void setMyDistributed_aDispose(int myDistributed_aDispose) {
		this.myDistributed_aDispose = myDistributed_aDispose;
	}

	public int getMyDistributed_disposed() {
		return myDistributed_disposed;
	}

	public void setMyDistributed_disposed(int myDistributed_disposed) {
		this.myDistributed_disposed = myDistributed_disposed;
	}
	
	
	
	
}
