package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class DoubleTrade implements Serializable {

	private static final long serialVersionUID = 1L;

	private String editType;// 是否被修改标识
	private String outletName;// 所属部门
	private String consCode;// 所属投顾
	private String consName;// 所属投顾名称
	private String consCustNo;// 客户姓名
	private String custName;// 客户姓名
	private String invstType;// 投资者类型
	private String invstName;// 投资者类型
	private String contractNo;// 交易单号
	private String appDt;// 交易日期
	private String systemFlag;// 系统来源
	private String fundCode;// 产品代码
	private String fundName;// 产品名称
	private Double appAmt;// 购买金额
	private String pmtDt;// 打款确认日期
	private String calmendDt;// 冷静期截止时间
	private String taTradeDt;// 上报截止日期
	private String returnVisitStatus;//
	private String doubleEntryStatus;//
	private String dStat;// 双录/回访
	private String returnVisitreQuire;// 回访要求
	private String doubleReturnStatus;// 双录回访状态
	private String doubleReturnStName;// 双录回访状态
	private String remarkInfo;// 备注

	public String getEditType() {
		return editType;
	}

	public void setEditType(String editType) {
		this.editType = editType;
	}

	public String getOutletName() {
		return outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getInvstType() {
		return invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getInvstName() {
		return invstName;
	}

	public void setInvstName(String invstName) {
		this.invstName = invstName;
	}

	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public String getAppDt() {
		return appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getSystemFlag() {
		return systemFlag;
	}

	public void setSystemFlag(String systemFlag) {
		this.systemFlag = systemFlag;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public Double getAppAmt() {
		return appAmt;
	}

	public void setAppAmt(Double appAmt) {
		this.appAmt = appAmt;
	}

	public String getPmtDt() {
		return pmtDt;
	}

	public void setPmtDt(String pmtDt) {
		this.pmtDt = pmtDt;
	}

	public String getCalmendDt() {
		return calmendDt;
	}

	public void setCalmendDt(String calmendDt) {
		this.calmendDt = calmendDt;
	}

	public String getTaTradeDt() {
		return taTradeDt;
	}

	public void setTaTradeDt(String taTradeDt) {
		this.taTradeDt = taTradeDt;
	}

	public String getReturnVisitStatus() {
		return returnVisitStatus;
	}

	public void setReturnVisitStatus(String returnVisitStatus) {
		this.returnVisitStatus = returnVisitStatus;
	}

	public String getDoubleEntryStatus() {
		return doubleEntryStatus;
	}

	public void setDoubleEntryStatus(String doubleEntryStatus) {
		this.doubleEntryStatus = doubleEntryStatus;
	}

	public String getdStat() {
		return dStat;
	}

	public void setdStat(String dStat) {
		this.dStat = dStat;
	}

	public String getReturnVisitreQuire() {
		return returnVisitreQuire;
	}

	public void setReturnVisitreQuire(String returnVisitreQuire) {
		this.returnVisitreQuire = returnVisitreQuire;
	}

	public String getDoubleReturnStatus() {
		return doubleReturnStatus;
	}

	public void setDoubleReturnStatus(String doubleReturnStatus) {
		this.doubleReturnStatus = doubleReturnStatus;
	}

	public String getDoubleReturnStName() {
		return doubleReturnStName;
	}

	public void setDoubleReturnStName(String doubleReturnStName) {
		this.doubleReturnStName = doubleReturnStName;
	}

	public String getRemarkInfo() {
		return remarkInfo;
	}

	public void setRemarkInfo(String remarkInfo) {
		this.remarkInfo = remarkInfo;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
