package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustIndexSale implements Serializable {

	private static final long serialVersionUID = 1L;

	private String sysMonth;

	private Double saleAmt;

	private int saleCustNum;

	private Double saleAvg;

	public String getSysMonth() {
		return sysMonth;
	}

	public void setSysMonth(String sysMonth) {
		this.sysMonth = sysMonth;
	}

	public Double getSaleAmt() {
		return saleAmt;
	}

	public void setSaleAmt(Double saleAmt) {
		this.saleAmt = saleAmt;
	}

	public int getSaleCustNum() {
		return saleCustNum;
	}

	public void setSaleCustNum(int saleCustNum) {
		this.saleCustNum = saleCustNum;
	}

	public Double getSaleAvg() {
		return saleAvg;
	}

	public void setSaleAvg(Double saleAvg) {
		this.saleAvg = saleAvg;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
