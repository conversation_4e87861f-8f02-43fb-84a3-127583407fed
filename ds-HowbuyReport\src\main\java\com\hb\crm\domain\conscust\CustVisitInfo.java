package com.hb.crm.domain.conscust;

import java.io.Serializable;

public class CustVisitInfo implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
    //投顾客户号
	private String consCustNo;
	//客户名
	private String custName;
    //省份code
	private String provCode;
	//城市code
	private String cityCode;
	//公募客户号
	private String pubcustNo;
	
	
	//拜访摘要
	private String visitsummary;
	//最近拜访日期
	private String visittime;
	//首次分配日期
	private String firstAssignDt;
	//所属投顾
	private String consCode;
	//所属投顾名
	private String consName;
	//客户来源
	private String source;
	public String getConsCustNo() {
		return consCustNo;
	}
	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getProvCode() {
		return provCode;
	}
	public void setProvCode(String provCode) {
		this.provCode = provCode;
	}
	public String getCityCode() {
		return cityCode;
	}
	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}
	public String getVisitsummary() {
		return visitsummary;
	}
	public void setVisitsummary(String visitsummary) {
		this.visitsummary = visitsummary;
	}
	public String getVisittime() {
		return visittime;
	}
	public void setVisittime(String visittime) {
		this.visittime = visittime;
	}
	public String getFirstAssignDt() {
		return firstAssignDt;
	}
	public void setFirstAssignDt(String firstAssignDt) {
		this.firstAssignDt = firstAssignDt;
	}
	public String getConsCode() {
		return consCode;
	}
	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	public CustVisitInfo() {
		
	}
	public String getConsName() {
		return consName;
	}
	public void setConsName(String consName) {
		this.consName = consName;
	}
	public String getPubcustNo() {
		return pubcustNo;
	}
	public void setPubcustNo(String pubcustNo) {
		this.pubcustNo = pubcustNo;
	}
}
