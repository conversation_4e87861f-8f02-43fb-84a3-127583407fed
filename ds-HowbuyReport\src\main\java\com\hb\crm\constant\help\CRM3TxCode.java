/**
 * 2010-08-16 chris.song	产品申请登记的交易码
 * 2010-09-14 chris.song	开通体验卡
 * 2010-09-17 chris.song	增加422005-登记免费产品
 * 2010-12-23 chris.song	增加422006-私募风险调查问卷提交
 * 2010-12-24 chris.song	增加422007,422008-私募调查问卷通知
 * 2010-12-28 chris.song	增加422009-满意度调查问卷通知
 * 2011-03-07 chris.song	贵宾中心421015-421017
 */
package com.hb.crm.constant.help;

/**
 * <AUTHOR>
 * crm2.0 客户的TxCode.
 */
public enum CRM3TxCode {
	
	/**
	 * 产品申请登记.
	 */
	RegProductApp("420001"),
	
	/**
	 * 产品申请取消登记.
	 */
	CancelProductApp("420011"),
	
	/**
	 * 投顾客户新增.
	 */
	ConsCustAdd("421010"),
	
	/**
	 * 投顾客户修改.
	 */
	ConsCustEdit("421011"),
	
	/**
	 * 投顾客户删除.
	 */
	ConsCustDel("421012"),
	
	/**
	 * 投顾客户特殊.
	 */
	ConsCustSpecial("421013"),
	
	/**
	 * 投顾客户特殊取消.
	 */
	ConsCustSpecialCancel("421014"),
	
	/**
	 * 贵宾中心验证码生成、发送和激活
	 */
	VipVerifyCodeCreated("421015"),
	VipVerifyCodeSent("421016"),
	VipActivated("421017"),
	
	/**
	 * 分配投顾.
	 */
	AssignCons("402004"),
	/**
	 * 网站客户关联
	 */
	RelateWebCust("422001"),
	/**
	 * 取消网站客户关联
	 */
	UnRelateWebCust("422002"),
	/**
	 * 取消网站客户对应
	 */
	UnRWebCust("422003"),
	/**
	 * 开通体验卡
	 */
	RegXPCard("422004"),
	/**
	 * 登记免费产品
	 */
	RegFreeProd("422005"),
	/**
	 * 私募风险调查问卷提交
	 */
	SubmitPriSurvey("422006"),
	/**
	 * email通知私募调查问卷
	 */
	SurveySentByEmail("422007"),
	/**
	 * 短信通知私募调查问卷
	 */
	SurveySentBySMS("422008"),
	/**
	 * 满意度调查问卷提交
	 */
	SubmitSatisfSurvey("422009")
	;
	private String value;

    public String getValue() {
        return value;
    }

    CRM3TxCode(String value) {
        this.value = value;
    }
}
