package com.hb.crm.domain.conscust;

// Generated 2012-8-21 15:45:42 by Hibernate Tools 3.2.0.b9

import java.util.Date;

/**
 * CmVisitrecNewest generated by hbm2java
 */
public class VisitRecNewest implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	private String appSerialNo;
	private String tradeDt;
	private String appCode;
	private String txCode;
	private String txAppFlag;
	private String txChkFlag;
	private String tradeChan;
	private String regionCode;
	private String outletCode;
	private String appDt;
	private String appTm;
	private String custNo;
	private String pCustID;
	private String contactStaff;
	private String howLong;
	private String visitRs;
	private String visitCust;
	private String visitTime;
	private String visitType;
	// 拜访分类
	private String visitClassify;
	private String visitSummary;
	private Double visitFee;
	private String feePurpose;
	private String servStaff;
	private String memo;
	private String retCode;
	private String retMsg;
	private String creator;
	private String checker;
	private String consCustNo;
	private String nextDt;
	private String nextSummary;
	private String consBookingID;
	private Date STimeStamp;

	private String callintime;
	private String nextStartTime; // 新增下次拜访开始时段
	private String nextEndTime; // 下次拜访结束时段
	private String nextVisitType; // 下次预约拜访方式
	private String bookingStatus; // 预约状态

	public VisitRecNewest() {
		super();
	}

	public VisitRecNewest(String appSerialNo, String tradeDt, String appCode,
			String txCode, String txAppFlag, String txChkFlag,
			String tradeChan, String regionCode, String outletCode,
			String appDt, String appTm, String custNo, String custID,
			String contactStaff, String howLong, String visitRs,
			String visitCust, String visitTime, String visitType,
			String visitSummary, Double visitFee, String feePurpose,
			String servStaff, String memo, String retCode, String retMsg,
			String creator, String checker, String consCustNo, String nextDt,
			String nextSummary, String consBookingID, Date timeStamp,
			String callintime, String nextStartTime, String nextEndTime,
			String nextVisitType,String bookingStatus) {
		super();
		this.appSerialNo = appSerialNo;
		this.tradeDt = tradeDt;
		this.appCode = appCode;
		this.txCode = txCode;
		this.txAppFlag = txAppFlag;
		this.txChkFlag = txChkFlag;
		this.tradeChan = tradeChan;
		this.regionCode = regionCode;
		this.outletCode = outletCode;
		this.appDt = appDt;
		this.appTm = appTm;
		this.custNo = custNo;
		pCustID = custID;
		this.contactStaff = contactStaff;
		this.howLong = howLong;
		this.visitRs = visitRs;
		this.visitCust = visitCust;
		this.visitTime = visitTime;
		this.visitType = visitType;
		this.visitSummary = visitSummary;
		this.visitFee = visitFee;
		this.feePurpose = feePurpose;
		this.servStaff = servStaff;
		this.memo = memo;
		this.retCode = retCode;
		this.retMsg = retMsg;
		this.creator = creator;
		this.checker = checker;
		this.consCustNo = consCustNo;
		this.nextDt = nextDt;
		this.nextSummary = nextSummary;
		this.consBookingID = consBookingID;
		this.STimeStamp = timeStamp;
		this.callintime = callintime;
		this.nextStartTime = nextStartTime;
		this.nextEndTime = nextEndTime;
		this.nextVisitType = nextVisitType;
		this.bookingStatus = bookingStatus;
	}

	public String getAppSerialNo() {
		return appSerialNo;
	}

	public void setAppSerialNo(String appSerialNo) {
		this.appSerialNo = appSerialNo;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getAppCode() {
		return appCode;
	}

	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}

	public String getTxCode() {
		return txCode;
	}

	public void setTxCode(String txCode) {
		this.txCode = txCode;
	}

	public String getTxAppFlag() {
		return txAppFlag;
	}

	public void setTxAppFlag(String txAppFlag) {
		this.txAppFlag = txAppFlag;
	}

	public String getTxChkFlag() {
		return txChkFlag;
	}

	public void setTxChkFlag(String txChkFlag) {
		this.txChkFlag = txChkFlag;
	}

	public String getTradeChan() {
		return tradeChan;
	}

	public void setTradeChan(String tradeChan) {
		this.tradeChan = tradeChan;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getOutletCode() {
		return outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getAppDt() {
		return appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getAppTm() {
		return appTm;
	}

	public void setAppTm(String appTm) {
		this.appTm = appTm;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getPCustID() {
		return pCustID;
	}

	public void setPCustID(String custID) {
		pCustID = custID;
	}

	public String getContactStaff() {
		return contactStaff;
	}

	public void setContactStaff(String contactStaff) {
		this.contactStaff = contactStaff;
	}

	public String getHowLong() {
		return howLong;
	}

	public void setHowLong(String howLong) {
		this.howLong = howLong;
	}

	public String getVisitRs() {
		return visitRs;
	}

	public void setVisitRs(String visitRs) {
		this.visitRs = visitRs;
	}

	public String getVisitCust() {
		return visitCust;
	}

	public void setVisitCust(String visitCust) {
		this.visitCust = visitCust;
	}

	public String getVisitTime() {
		return visitTime;
	}

	public void setVisitTime(String visitTime) {
		this.visitTime = visitTime;
	}

	public String getVisitType() {
		return visitType;
	}

	public void setVisitType(String visitType) {
		this.visitType = visitType;
	}

	public String getVisitSummary() {
		return visitSummary;
	}

	public void setVisitSummary(String visitSummary) {
		this.visitSummary = visitSummary;
	}

	public Double getVisitFee() {
		return visitFee;
	}

	public void setVisitFee(Double visitFee) {
		this.visitFee = visitFee;
	}

	public String getFeePurpose() {
		return feePurpose;
	}

	public void setFeePurpose(String feePurpose) {
		this.feePurpose = feePurpose;
	}

	public String getServStaff() {
		return servStaff;
	}

	public void setServStaff(String servStaff) {
		this.servStaff = servStaff;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRetCode() {
		return retCode;
	}

	public void setRetCode(String retCode) {
		this.retCode = retCode;
	}

	public String getRetMsg() {
		return retMsg;
	}

	public void setRetMsg(String retMsg) {
		this.retMsg = retMsg;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getChecker() {
		return checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getNextDt() {
		return nextDt;
	}

	public void setNextDt(String nextDt) {
		this.nextDt = nextDt;
	}

	public String getNextSummary() {
		return nextSummary;
	}

	public void setNextSummary(String nextSummary) {
		this.nextSummary = nextSummary;
	}

	public String getConsBookingID() {
		return consBookingID;
	}

	public void setConsBookingID(String consBookingID) {
		this.consBookingID = consBookingID;
	}

	public Date getSTimeStamp() {
		return STimeStamp;
	}

	public void setSTimeStamp(Date timeStamp) {
		STimeStamp = timeStamp;
	}

	public String getCallintime() {
		return callintime;
	}

	public void setCallintime(String callintime) {
		this.callintime = callintime;
	}

	public String getVisitClassify() {
		return visitClassify;
	}

	public void setVisitClassify(String visitClassify) {
		this.visitClassify = visitClassify;
	}

	public String getpCustID() {
		return pCustID;
	}

	public void setpCustID(String pCustID) {
		this.pCustID = pCustID;
	}

	public String getNextStartTime() {
		return nextStartTime;
	}

	public void setNextStartTime(String nextStartTime) {
		this.nextStartTime = nextStartTime;
	}

	public String getNextEndTime() {
		return nextEndTime;
	}

	public void setNextEndTime(String nextEndTime) {
		this.nextEndTime = nextEndTime;
	}

	public String getNextVisitType() {
		return nextVisitType;
	}

	public void setNextVisitType(String nextVisitType) {
		this.nextVisitType = nextVisitType;
	}

	public String getBookingStatus() {
		return bookingStatus;
	}

	public void setBookingStatus(String bookingStatus) {
		this.bookingStatus = bookingStatus;
	}

}
