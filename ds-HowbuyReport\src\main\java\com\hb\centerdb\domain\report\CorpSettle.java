package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类CorpSettle.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CorpSettle implements Serializable {

private static final long serialVersionUID = 1L;

	private String beginDate;// 统计开始日期

	private String endDate ;//统计结束日期
	
	private Double commission; //手续费收入
	
	private Double serviceFee;//销售服务费收入
	
	private Double tailCommision;//尾佣收入
	
	private Double bankSupervise;//民生监管成本
	
	private Double pettyFee;//小额打款成本
	
	private Double gztVerify;//国政通验证成本
	
	private Double paymentOrgFee;//支付机构成本
	
	private Double salesShare;//销售收入分成
	
	private Double costShare;//成本分成
	
	private Double tailShare;//尾佣分成
	
	private Double sumShare;//分成合计
	
	private Double lctBackFee;//分成合计


	public Double getLctBackFee() {
		return lctBackFee;
	}

	public void setLctBackFee(Double lctBackFee) {
		this.lctBackFee = lctBackFee;
	}

	public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Double getCommission() {
		return commission;
	}

	public void setCommission(Double commission) {
		this.commission = commission;
	}


	public Double getServiceFee() {
		return serviceFee;
	}




	public void setServiceFee(Double serviceFee) {
		this.serviceFee = serviceFee;
	}




	public Double getTailCommision() {
		return tailCommision;
	}




	public void setTailCommision(Double tailCommision) {
		this.tailCommision = tailCommision;
	}




	public Double getBankSupervise() {
		return bankSupervise;
	}




	public void setBankSupervise(Double bankSupervise) {
		this.bankSupervise = bankSupervise;
	}




	public Double getPettyFee() {
		return pettyFee;
	}




	public void setPettyFee(Double pettyFee) {
		this.pettyFee = pettyFee;
	}




	public Double getGztVerify() {
		return gztVerify;
	}




	public void setGztVerify(Double gztVerify) {
		this.gztVerify = gztVerify;
	}




	public Double getPaymentOrgFee() {
		return paymentOrgFee;
	}




	public void setPaymentOrgFee(Double paymentOrgFee) {
		this.paymentOrgFee = paymentOrgFee;
	}

public Double getSalesShare() {
		return salesShare;
	}

	public void setSalesShare(Double salesShare) {
		this.salesShare = salesShare;
	}

	public Double getCostShare() {
		return costShare;
	}

	public void setCostShare(Double costShare) {
		this.costShare = costShare;
	}

	public Double getTailShare() {
		return tailShare;
	}

	public void setTailShare(Double tailShare) {
		this.tailShare = tailShare;
	}

	public Double getSumShare() {
		return sumShare;
	}

	public void setSumShare(Double sumShare) {
		this.sumShare = sumShare;
	}

public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
