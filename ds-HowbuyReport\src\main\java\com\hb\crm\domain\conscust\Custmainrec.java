package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Custmainrec.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Custmainrec implements Serializable {

private static final long serialVersionUID = 1L;

	private String appserialno;
	
	private String contractno;
	
	private String tradedt;
	
	private String appcode;
	
	private String custno;
	
	private String txcode;
	
	private String txappflag;
	
	private String txchkflag;
	
	private String tradechan;
	
	private String regioncode;
	
	private String outletcode;
	
	private String appdt;
	
	private String apptm;
	
	private String orappserialno;
	
	private String vrfymode;
	
	private String retcode;
	
	private String retmsg;
	
	private String creator;
	
	private String checker;
	
	private Date stimestamp;
	
	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}
	
	public String getContractno() {
		return this.contractno;
	}

	public void setContractno(String contractno) {
		this.contractno = contractno;
	}
	
	public String getTradedt() {
		return this.tradedt;
	}

	public void setTradedt(String tradedt) {
		this.tradedt = tradedt;
	}
	
	public String getAppcode() {
		return this.appcode;
	}

	public void setAppcode(String appcode) {
		this.appcode = appcode;
	}
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getTxcode() {
		return this.txcode;
	}

	public void setTxcode(String txcode) {
		this.txcode = txcode;
	}
	
	public String getTxappflag() {
		return this.txappflag;
	}

	public void setTxappflag(String txappflag) {
		this.txappflag = txappflag;
	}
	
	public String getTxchkflag() {
		return this.txchkflag;
	}

	public void setTxchkflag(String txchkflag) {
		this.txchkflag = txchkflag;
	}
	
	public String getTradechan() {
		return this.tradechan;
	}

	public void setTradechan(String tradechan) {
		this.tradechan = tradechan;
	}
	
	public String getRegioncode() {
		return this.regioncode;
	}

	public void setRegioncode(String regioncode) {
		this.regioncode = regioncode;
	}
	
	public String getOutletcode() {
		return this.outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}
	
	public String getAppdt() {
		return this.appdt;
	}

	public void setAppdt(String appdt) {
		this.appdt = appdt;
	}
	
	public String getApptm() {
		return this.apptm;
	}

	public void setApptm(String apptm) {
		this.apptm = apptm;
	}
	
	public String getOrappserialno() {
		return this.orappserialno;
	}

	public void setOrappserialno(String orappserialno) {
		this.orappserialno = orappserialno;
	}
	
	public String getVrfymode() {
		return this.vrfymode;
	}

	public void setVrfymode(String vrfymode) {
		this.vrfymode = vrfymode;
	}
	
	public String getRetcode() {
		return this.retcode;
	}

	public void setRetcode(String retcode) {
		this.retcode = retcode;
	}
	
	public String getRetmsg() {
		return this.retmsg;
	}

	public void setRetmsg(String retmsg) {
		this.retmsg = retmsg;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
