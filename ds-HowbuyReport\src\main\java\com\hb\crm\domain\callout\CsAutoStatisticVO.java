package com.hb.crm.domain.callout;

import java.util.HashSet;
import java.util.Set;

import com.hb.crm.util.VirtualAdvisor;

public class CsAutoStatisticVO {
	private String orgName;
	private String orgCode;
	private String teamCode;
	private String teamName;
	private String proviceNames;
	private int howbuyTargetNum;
	private int howbuyCurrTargetNum;
	private int unHowbuyTargetNum;
	private int unHowbuyCurrTargetNum;
	private int allTypeTargetNum;
	private Set<String> proviceNameSet;
	
	private int allTargetNum;
	
	public CsAutoStatisticVO(){
		howbuyTargetNum = 0;
		howbuyCurrTargetNum = 0;
		unHowbuyTargetNum = 0;
		unHowbuyCurrTargetNum = 0;
		proviceNameSet = new HashSet<String>();
	}
	
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getTeamCode() {
		return teamCode;
	}

	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}

	public String getTeamName() {
		return teamName;
	}

	public void setTeamName(String teamName) {
		this.teamName = teamName;
	}

	public String getProviceNames() {
		return proviceNames;
	}

	public void setProviceNames(String proviceNames) {
		this.proviceNames = proviceNames;
	}

	public int getHowbuyTargetNum() {
		return howbuyTargetNum;
	}
	public void setHowbuyTargetNum(int howbuyTargetNum) {
		this.howbuyTargetNum = howbuyTargetNum;
	}
	public int getHowbuyCurrTargetNum() {
		return howbuyCurrTargetNum;
	}
	public void setHowbuyCurrTargetNum(int howbuyCurrTargetNum) {
		this.howbuyCurrTargetNum = howbuyCurrTargetNum;
	}
	public int getUnHowbuyTargetNum() {
		return unHowbuyTargetNum;
	}
	public void setUnHowbuyTargetNum(int unHowbuyTargetNum) {
		this.unHowbuyTargetNum = unHowbuyTargetNum;
	}
	public int getUnHowbuyCurrTargetNum() {
		return unHowbuyCurrTargetNum;
	}
	public void setUnHowbuyCurrTargetNum(int unHowbuyCurrTargetNum) {
		this.unHowbuyCurrTargetNum = unHowbuyCurrTargetNum;
	}

	public Set<String> getProviceNameSet() {
		return proviceNameSet;
	}

	public void setProviceNameSet(Set<String> proviceNameSet) {
		this.proviceNameSet = proviceNameSet;
	}
	
	public void mergeProviceNames(){
		String tempProviceNames = "";
		for(String tempProviceName : proviceNameSet){
			tempProviceNames += tempProviceName + " ";
		}
		
		this.setProviceNames(tempProviceNames);
	}

	public int getAllTargetNum() {
		return this.unHowbuyTargetNum + this.howbuyTargetNum;
	}

	public int getAllTypeTargetNum() {
		return allTypeTargetNum;
	}

	public void setAllTypeTargetNum(int allTypeTargetNum) {
		this.allTypeTargetNum = allTypeTargetNum;
	}

	
}
