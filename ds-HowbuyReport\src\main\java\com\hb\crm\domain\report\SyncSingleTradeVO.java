package com.hb.crm.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类SyncSingleTradeVO.java
 * @version 1.0
 * @created 
 */
public class SyncSingleTradeVO implements Serializable {

private static final long serialVersionUID = 1L;

	private String sdate;	
	
	private String tradedt;	// 日期
	
	private String consname;	// 所属投顾
	
	private String pubcustname;	//公募客户姓名 
	
	private String fundnameattrhb;	//基金名称
	
	private String fundtype;	// 基金类型
	
	private String fundsubtype;	// 基金二级类型
	
	private String fundmanabbr;	// 基金公司名称
	
	private String busicode;	// 交易类型
	
	private BigDecimal ackamt = new BigDecimal(0); 	// 交易金额
	
	private BigDecimal fee = new BigDecimal(0); 		// 总费用
	
	private BigDecimal agencyfee = new BigDecimal(0); 	// 公司分成
	
	private BigDecimal standardfee = new BigDecimal(0); 	// 基准费用
	
	private String source ; 		// 来源(投顾客户一级来源)
	
	private BigDecimal sourcecoefficient = new BigDecimal(0); 	// 该笔交易来源系数
	
	private BigDecimal consfee = new BigDecimal(0); // 投顾分成 = agencyfee*sourcecoefficient
	
	private String outletclassifyname;	// 投顾部门
	
	private String outletname;	// 投顾分公司
	
	// 用于查询用字段
	private String retcode;
	
	private String recstat;
	
	private String pubcustno;
	
	private String conscode;
	
	// 用于列合计字段
	private String totalAckAmt;	// 统计交易金额合计
	
	private String totalFee;	// 统计总费用合计
	
	private String totalAgencyFee;	// 统计公司分成合计
	
	private String totalConsFee;	// 统计投顾分成合计
	
	public String getSdate() {
		return this.sdate;
	}

	public void setSdate(String sdate) {
		this.sdate = sdate;
	}
	
	public String getTradedt() {
		return this.tradedt;
	}

	public void setTradedt(String tradedt) {
		this.tradedt = tradedt;
	}
	
	public String getConsname() {
		return this.consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}
	
	public String getPubcustname() {
		return this.pubcustname;
	}

	public void setPubcustname(String pubcustname) {
		this.pubcustname = pubcustname;
	}
	
	public String getFundnameattrhb() {
		return this.fundnameattrhb;
	}

	public void setFundnameattrhb(String fundnameattrhb) {
		this.fundnameattrhb = fundnameattrhb;
	}
	
	public String getFundtype() {
		return this.fundtype;
	}

	public void setFundtype(String fundtype) {
		this.fundtype = fundtype;
	}
	
	public String getFundsubtype() {
		return fundsubtype;
	}

	public void setFundsubtype(String fundsubtype) {
		this.fundsubtype = fundsubtype;
	}

	public String getFundmanabbr() {
		return this.fundmanabbr;
	}

	public void setFundmanabbr(String fundmanabbr) {
		this.fundmanabbr = fundmanabbr;
	}
	
	public String getBusicode() {
		return this.busicode;
	}

	public void setBusicode(String busicode) {
		this.busicode = busicode;
	}
	
	public BigDecimal getAckamt() {
		return this.ackamt;
	}

	public void setAckamt(BigDecimal ackamt) {
		this.ackamt = ackamt;
	}
	
	public BigDecimal getFee() {
		return this.fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}
	
	public BigDecimal getAgencyfee() {
		return this.agencyfee;
	}

	public void setAgencyfee(BigDecimal agencyfee) {
		this.agencyfee = agencyfee;
	}
	
	public BigDecimal getStandardfee() {
		return standardfee;
	}

	public void setStandardfee(BigDecimal standardfee) {
		this.standardfee = standardfee;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}
	
	public BigDecimal getSourcecoefficient() {
		return this.sourcecoefficient;
	}

	public void setSourcecoefficient(BigDecimal sourcecoefficient) {
		this.sourcecoefficient = sourcecoefficient;
	}
	
	public BigDecimal getConsfee() {
		return this.consfee;
	}

	public void setConsfee(BigDecimal consfee) {
		this.consfee = consfee;
	}
	
	public String getOutletclassifyname() {
		return this.outletclassifyname;
	}

	public void setOutletclassifyname(String outletclassifyname) {
		this.outletclassifyname = outletclassifyname;
	}
	
	public String getOutletname() {
		return this.outletname;
	}

	public void setOutletname(String outletname) {
		this.outletname = outletname;
	}
	
    public String getRetcode() {
		return retcode;
	}

	public void setRetcode(String retcode) {
		this.retcode = retcode;
	}

	public String getRecstat() {
		return recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}

	public String getPubcustno() {
		return pubcustno;
	}

	public void setPubcustno(String pubcustno) {
		this.pubcustno = pubcustno;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	
	public String getTotalAckAmt() {
		return totalAckAmt;
	}

	public void setTotalAckAmt(String totalAckAmt) {
		this.totalAckAmt = totalAckAmt;
	}

	public String getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(String totalFee) {
		this.totalFee = totalFee;
	}

	public String getTotalAgencyFee() {
		return totalAgencyFee;
	}

	public void setTotalAgencyFee(String totalAgencyFee) {
		this.totalAgencyFee = totalAgencyFee;
	}

	public String getTotalConsFee() {
		return totalConsFee;
	}

	public void setTotalConsFee(String totalConsFee) {
		this.totalConsFee = totalConsFee;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
