package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.math.BigDecimal;

public class FundTradeInfo implements Serializable {

	 private static final long serialVersionUID = 6736523734903219221L;
     
     /**
      * 交易日期
      */
     private String tradeDt;
     /*
      * 基金代码
      */
     private String fundCode;
     /*
      * 基金名称
      */
     private String fundAttr;
     
     private String tradeType;

     /**
      * 交易账号
      */
     private String custno;
     /**
      * 基金简称
      */
     private String jjjc;
     /**
      * 交易状态
      */
     private String tradeStatus;
     /**
      * 交易类型
      */
     private String busiType;
     /**
      * 交易净值
      */
     private BigDecimal tradeNav = new BigDecimal(0);
     
     private String tradeNavStr;
     
     /**
      * 基金份额
      */
     private BigDecimal tradeVol = new BigDecimal(0);
     
     private String tradeVolStr;
     /**
      * 手续费
      */
     private BigDecimal fee = new BigDecimal(0);
     
     private String feeStr;
     /**
      * 交易金额
      */
     private BigDecimal tradeAmt = new BigDecimal(0);
     
     private String tradeAmtStr;
     /**
      * 银行名称
      */
     private String bankRegionName;
     /**
      * 银行卡号
      */
     private String bankAcct;
     
     private String bankName;
     
    
     
     /**
      * 基金代码
      */
     private String jjdm;
     /**
      * 基金类型
      */
     private String jjlx;
     /**
      * bigType
      */
     private String bigType;
     /**
      * 主基金代码
      */
     private String mainFundcode;
     /**
      * 交易类型
      */
     private String busiCode;
     /**
      * 基金份额
      */
     private BigDecimal appVol = new BigDecimal(0);
     
     private String appVolStr;
     /**
      * 基金金额
      */
     private BigDecimal appAmt = new BigDecimal(0);
     
     private String appAmtStr;
     /**
      * 基金类型
      */
     private String fundType ;
      /**
       * 好买简称
       */
     private String fundAttrHb;
     
     private BigDecimal ackVol= new BigDecimal(0);
     
     private String ackVolStr;
     
     private  BigDecimal  ackAmt= new BigDecimal(0);
     
     private String ackAmtStr;
     
     private String dealType;
     
     public String getTradeDt() {
         return tradeDt;
     }
     public void setTradeDt(String tradeDt) {
         this.tradeDt = tradeDt;
     }
     
     public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
	public BigDecimal getAppVol() {
		return appVol;
	}
	public void setAppVol(BigDecimal appVol) {
		this.appVol = appVol;
	}
	public BigDecimal getAppAmt() {
		return appAmt;
	}
	public void setAppAmt(BigDecimal appAmt) {
		this.appAmt = appAmt;
	}
	public String getFundType() {
		return fundType;
	}
	public void setFundType(String fundType) {
		this.fundType = fundType;
	}
	public String getFundAttrHb() {
		return fundAttrHb;
	}
	public void setFundAttrHb(String fundAttrHb) {
		this.fundAttrHb = fundAttrHb;
	}
	public BigDecimal getAckVol() {
		return ackVol;
	}
	public void setAckVol(BigDecimal ackVol) {
		this.ackVol = ackVol;
	}
	public BigDecimal getAckAmt() {
		return ackAmt;
	}
	public void setAckAmt(BigDecimal ackAmt) {
		this.ackAmt = ackAmt;
	}
	public String getDealType() {
		return dealType;
	}
	public void setDealType(String dealType) {
		this.dealType = dealType;
	}
	public String getCustno()
     {
         return custno;
     }
     public void setCustno(String custno)
     {
         this.custno = custno;
     }
     public String getTradeStatus()
     {
         return tradeStatus;
     }
     public void setTradeStatus(String tradeStatus)
     {
         this.tradeStatus = tradeStatus;
     }
    
     public BigDecimal getTradeNav() {
         return tradeNav;
     }
     public void setTradeNav(BigDecimal tradeNav) {
         this.tradeNav = tradeNav;
     }
     public BigDecimal getTradeVol() {
         return tradeVol;
     }
     public void setTradeVol(BigDecimal tradeVol) {
         this.tradeVol = tradeVol;
     }
     public BigDecimal getFee() {
         return fee;
     }
     public void setFee(BigDecimal fee) {
         this.fee = fee;
     }
     public BigDecimal getTradeAmt() {
         return tradeAmt;
     }
     public void setTradeAmt(BigDecimal tradeAmt) {
         this.tradeAmt = tradeAmt;
     }
     public String getJjjc()
     {
         return jjjc;
     }
     public void setJjjc(String jjjc)
     {
         this.jjjc = jjjc;
     }
     
     public String getFundAttr() {
		return fundAttr;
	}
	public void setFundAttr(String fundAttr) {
		this.fundAttr = fundAttr;
	}
	public String getBusiType() {
		return busiType;
	}
	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}
	public String getBankRegionName() {
		return bankRegionName;
	}
	public void setBankRegionName(String bankRegionName) {
		this.bankRegionName = bankRegionName;
	}
	public String getBankAcct()
     {
         return bankAcct;
     }
     public void setBankAcct(String bankAcct)
     {
         this.bankAcct = bankAcct;
     }
     public String getJjdm()
     {
         return jjdm;
     }
     public void setJjdm(String jjdm)
     {
         this.jjdm = jjdm;
     }
     public String getJjlx()
     {
         return jjlx;
     }
     public void setJjlx(String jjlx)
     {
         this.jjlx = jjlx;
     }
     public String getBigType()
     {
         return bigType;
     }
     public void setBigType(String bigType)
     {
         this.bigType = bigType;
     }
     public String getMainFundcode()
     {
         return mainFundcode;
     }
     public void setMainFundcode(String mainFundcode)
     {
         this.mainFundcode = mainFundcode;
     }
     
	public String getTradeNavStr() {
		return tradeNavStr;
	}
	public void setTradeNavStr(String tradeNavStr) {
		this.tradeNavStr = tradeNavStr;
	}
	public String getFeeStr() {
		return feeStr;
	}
	public void setFeeStr(String feeStr) {
		this.feeStr = feeStr;
	}
	public String getAppVolStr() {
		return appVolStr;
	}
	public void setAppVolStr(String appVolStr) {
		this.appVolStr = appVolStr;
	}
	public String getAppAmtStr() {
		return appAmtStr;
	}
	public void setAppAmtStr(String appAmtStr) {
		this.appAmtStr = appAmtStr;
	}
	public String getAckVolStr() {
		return ackVolStr;
	}
	public void setAckVolStr(String ackVolStr) {
		this.ackVolStr = ackVolStr;
	}
	public String getAckAmtStr() {
		return ackAmtStr;
	}
	public void setAckAmtStr(String ackAmtStr) {
		this.ackAmtStr = ackAmtStr;
	}
	public String getTradeAmtStr() {
		return tradeAmtStr;
	}
	public void setTradeAmtStr(String tradeAmtStr) {
		this.tradeAmtStr = tradeAmtStr;
	}
	public String getTradeVolStr() {
		return tradeVolStr;
	}
	public void setTradeVolStr(String tradeVolStr) {
		this.tradeVolStr = tradeVolStr;
	}
	
	
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getFundCode() {
		return fundCode;
	}
	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}
	
	
	public String getTradeType() {
		return tradeType;
	}
	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}
	@Override
	public String toString() {
		return "FundTradeInfo [tradeDt=" + tradeDt + ", fundCode=" + fundCode
				+ ", fundAttr=" + fundAttr + ", custno=" + custno + ", jjjc="
				+ jjjc + ", tradeStatus=" + tradeStatus + ", busiType="
				+ busiType + ", tradeNav=" + tradeNav + ", tradeNavStr="
				+ tradeNavStr + ", tradeVol=" + tradeVol + ", tradeVolStr="
				+ tradeVolStr + ", fee=" + fee + ", feeStr=" + feeStr
				+ ", tradeAmt=" + tradeAmt + ", tradeAmtStr=" + tradeAmtStr
				+ ", bankRegionName=" + bankRegionName + ", bankAcct="
				+ bankAcct + ", jjdm=" + jjdm + ", jjlx=" + jjlx + ", bigType="
				+ bigType + ", mainFundcode=" + mainFundcode + ", busiCode="
				+ busiCode + ", appVol=" + appVol + ", appVolStr=" + appVolStr
				+ ", appAmt=" + appAmt + ", appAmtStr=" + appAmtStr
				+ ", fundType=" + fundType + ", fundAttrHb=" + fundAttrHb
				+ ", ackVol=" + ackVol + ", ackVolStr=" + ackVolStr
				+ ", ackAmt=" + ackAmt + ", ackAmtStr=" + ackAmtStr
				+ ", dealType=" + dealType + "]";
	}
	
	
	
}
