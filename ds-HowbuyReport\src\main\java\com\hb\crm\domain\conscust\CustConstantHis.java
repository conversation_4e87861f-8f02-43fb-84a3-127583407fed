package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;

public class CustConstantHis implements Serializable {
	private static final long serialVersionUID = 1L;

	private String custConsHisID;
	
	private String custconshisid;

	private String custno;

	private String conscode;

	private String startdt;

	private String enddt;

	private String memo;

	private String recstat;

	private String checkflag;

	private String creator;

	private String modifier;

	private String checker;

	private String credt;

	private String moddt;

	private Date binddate;

	private Date unbunddate;
	
	private String pcustid;

	public String getCustno() {
		return custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}

	public String getCustconshisid() {
		return custconshisid;
	}

	public void setCustconshisid(String custconshisid) {
		this.custconshisid = custconshisid;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getStartdt() {
		return startdt;
	}

	public void setStartdt(String startdt) {
		this.startdt = startdt;
	}

	public String getEnddt() {
		return enddt;
	}

	public void setEnddt(String enddt) {
		this.enddt = enddt;
	}

	public String getRecstat() {
		return recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}

	public String getCheckflag() {
		return checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}

	public String getCredt() {
		return credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getCustConsHisID() {
		return this.custConsHisID;
	}

	public void setCustConsHisID(String custConsHisID) {
		this.custConsHisID = custConsHisID;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public Date getBinddate() {
		return binddate;
	}

	public void setBinddate(Date binddate) {
		this.binddate = binddate;
	}

	public Date getUnbunddate() {
		return unbunddate;
	}

	public void setUnbunddate(Date unbunddate) {
		this.unbunddate = unbunddate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getPcustid() {
		return pcustid;
	}

	public void setPcustid(String pcustid) {
		this.pcustid = pcustid;
	}
}