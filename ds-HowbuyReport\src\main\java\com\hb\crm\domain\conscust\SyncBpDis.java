package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类SyncBpDis.java
 * @version 1.0
 */
public class SyncBpDis implements Serializable {

private static final long serialVersionUID = 1L;

	private String disCode;
	
	private String disName;
	
	private String disType;
	
	private String channelSeq;
	
	private String recStat;
	
	private String checkFlag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String creDt;
	
	private String modDt;
	
	private String syncDate;
	
	private String disAttr;
	
	public String getDisCode() {
		return this.disCode;
	}

	public void setDisCode(String disCode) {
		this.disCode = disCode;
	}
	
	public String getDisName() {
		return this.disName;
	}

	public void setDisName(String disName) {
		this.disName = disName;
	}
	
	public String getDisType() {
		return this.disType;
	}

	public void setDisType(String disType) {
		this.disType = disType;
	}
	
	public String getChannelSeq() {
		return this.channelSeq;
	}

	public void setChannelSeq(String channelSeq) {
		this.channelSeq = channelSeq;
	}
	
	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}
	
	public String getCheckFlag() {
		return this.checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}
	
	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}
	
	public String getSyncDate() {
		return this.syncDate;
	}

	public void setSyncDate(String syncDate) {
		this.syncDate = syncDate;
	}
	
	public String getDisAttr() {
		return this.disAttr;
	}

	public void setDisAttr(String disAttr) {
		this.disAttr = disAttr;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
