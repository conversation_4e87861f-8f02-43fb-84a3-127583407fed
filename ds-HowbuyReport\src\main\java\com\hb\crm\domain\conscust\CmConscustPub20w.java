package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 实体类CtfxCustDetail.java
 * @version 1.0
 */
public class CmConscustPub20w implements Serializable {

	private static final long serialVersionUID = 1L;

	private String consCustNo;

	private String cust_No;

	private String reg_Dt;

	private String reg_Dis_Code;

	private String reg_Dis_Name;

	private String f1_App_Dt;

	private String f1_Fund_Type;

	private String f1_Fund_Type_Name;

	private String trade_Dt_20w;

	private String trade_Dt_Max;

	private String max_MarketAmt;

	private String custType_Ab;

	private String custType;
	
	private String f1_Ack_Dt;
	
	private String f2_Ack_Dt;

	private String f3_Ack_Dt;
	
	private Date updateTime;

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getCust_No() {
		return cust_No;
	}

	public void setCust_No(String cust_No) {
		this.cust_No = cust_No;
	}

	public String getReg_Dt() {
		return reg_Dt;
	}

	public void setReg_Dt(String reg_Dt) {
		this.reg_Dt = reg_Dt;
	}

	public String getReg_Dis_Code() {
		return reg_Dis_Code;
	}

	public void setReg_Dis_Code(String reg_Dis_Code) {
		this.reg_Dis_Code = reg_Dis_Code;
	}

	public String getReg_Dis_Name() {
		return reg_Dis_Name;
	}

	public void setReg_Dis_Name(String reg_Dis_Name) {
		this.reg_Dis_Name = reg_Dis_Name;
	}

	public String getF1_App_Dt() {
		return f1_App_Dt;
	}

	public void setF1_App_Dt(String f1_App_Dt) {
		this.f1_App_Dt = f1_App_Dt;
	}

	public String getF1_Fund_Type() {
		return f1_Fund_Type;
	}

	public void setF1_Fund_Type(String f1_Fund_Type) {
		this.f1_Fund_Type = f1_Fund_Type;
	}

	public String getF1_Fund_Type_Name() {
		return f1_Fund_Type_Name;
	}

	public void setF1_Fund_Type_Name(String f1_Fund_Type_Name) {
		this.f1_Fund_Type_Name = f1_Fund_Type_Name;
	}

	public String getTrade_Dt_20w() {
		return trade_Dt_20w;
	}

	public void setTrade_Dt_20w(String trade_Dt_20w) {
		this.trade_Dt_20w = trade_Dt_20w;
	}

	public String getTrade_Dt_Max() {
		return trade_Dt_Max;
	}

	public void setTrade_Dt_Max(String trade_Dt_Max) {
		this.trade_Dt_Max = trade_Dt_Max;
	}

	public String getMax_MarketAmt() {
		return max_MarketAmt;
	}

	public void setMax_MarketAmt(String max_MarketAmt) {
		this.max_MarketAmt = max_MarketAmt;
	}

	public String getCustType_Ab() {
		return custType_Ab;
	}

	public void setCustType_Ab(String custType_Ab) {
		this.custType_Ab = custType_Ab;
	}

	public String getCustType() {
		return custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getF1_Ack_Dt() {
		return f1_Ack_Dt;
	}

	public void setF1_Ack_Dt(String f1_Ack_Dt) {
		this.f1_Ack_Dt = f1_Ack_Dt;
	}

	public String getF2_Ack_Dt() {
		return f2_Ack_Dt;
	}

	public void setF2_Ack_Dt(String f2_Ack_Dt) {
		this.f2_Ack_Dt = f2_Ack_Dt;
	}

	public String getF3_Ack_Dt() {
		return f3_Ack_Dt;
	}

	public void setF3_Ack_Dt(String f3_Ack_Dt) {
		this.f3_Ack_Dt = f3_Ack_Dt;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
