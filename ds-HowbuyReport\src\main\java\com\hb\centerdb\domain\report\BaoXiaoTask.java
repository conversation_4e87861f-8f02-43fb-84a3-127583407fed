package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class BaoXiaoTask implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * @Fields taskId : taskid
     */
    private String taskId;

    /**
     * @Fields taskType : 任务类型
     */
    private String taskType;

    /**
     * @Fields taskDate : 任务时间
     */
    private String taskDate;

    /**
     * @Fields baoXiaoName : 包销名称
     */
    private String baoXiaoName;

    /**
     * @Fields realPayAmtStartDt : 实际打款起始时间
     */
    private String realPayAmtStartDt;

    /**
     * @Fields realPayAmtEndDt : 实际打款结束时间
     */
    private String realPayAmtEndDt;

    /**
     * @Fields expectTradeStartDt : 预计交易起始时间
     */
    private String expectTradeStartDt;

    /**
     * @Fields expectTradeEndDt : 预计交易结束时间
     */
    private String expectTradeEndDt;

    /**
     * @Fields contentId : 关连编号根据任务类型确定
     */
    private String contentId;

    /**
     * @Fields isPre :是否当期预约
     */
    private String isPre;

    private BigDecimal taskAmt = new BigDecimal(0);// 任务量(万)

    private BigDecimal expectAmt = new BigDecimal(0);// 预约量(万)

    private BigDecimal payAmt = new BigDecimal(0);// 打款量(万)

    private String expectFinishRate;//预约完成率(%)

    private String payFinishRate;//打款完成率(%)

    private String outletcode;//部门

    private BigDecimal disExpectAmt = new BigDecimal(0);// 预约折标量(万)

    private BigDecimal disPayAmt = new BigDecimal(0);// 打款折标量(万)

    private String disExpectFinishRate;//预约折标完成率(%)

    private String disPayFinishRate;//打款折标完成率(%)

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTaskDate() {
        return taskDate;
    }

    public void setTaskDate(String taskDate) {
        this.taskDate = taskDate;
    }

    public String getBaoXiaoName() {
        return baoXiaoName;
    }

    public void setBaoXiaoName(String baoXiaoName) {
        this.baoXiaoName = baoXiaoName;
    }

    public String getRealPayAmtStartDt() {
        return realPayAmtStartDt;
    }

    public void setRealPayAmtStartDt(String realPayAmtStartDt) {
        this.realPayAmtStartDt = realPayAmtStartDt;
    }

    public String getRealPayAmtEndDt() {
        return realPayAmtEndDt;
    }

    public void setRealPayAmtEndDt(String realPayAmtEndDt) {
        this.realPayAmtEndDt = realPayAmtEndDt;
    }

    public String getExpectTradeStartDt() {
        return expectTradeStartDt;
    }

    public void setExpectTradeStartDt(String expectTradeStartDt) {
        this.expectTradeStartDt = expectTradeStartDt;
    }

    public String getExpectTradeEndDt() {
        return expectTradeEndDt;
    }

    public void setExpectTradeEndDt(String expectTradeEndDt) {
        this.expectTradeEndDt = expectTradeEndDt;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getIsPre() {
        return isPre;
    }

    public void setIsPre(String isPre) {
        this.isPre = isPre;
    }

    public BigDecimal getTaskAmt() {
        return taskAmt;
    }

    public void setTaskAmt(BigDecimal taskAmt) {
        this.taskAmt = taskAmt;
    }

    public BigDecimal getExpectAmt() {
        return expectAmt;
    }

    public void setExpectAmt(BigDecimal expectAmt) {
        this.expectAmt = expectAmt;
    }

    public BigDecimal getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(BigDecimal payAmt) {
        this.payAmt = payAmt;
    }

    public String getExpectFinishRate() {
        return expectFinishRate;
    }

    public void setExpectFinishRate(String expectFinishRate) {
        this.expectFinishRate = expectFinishRate;
    }

    public String getPayFinishRate() {
        return payFinishRate;
    }

    public void setPayFinishRate(String payFinishRate) {
        this.payFinishRate = payFinishRate;
    }

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public BigDecimal getDisExpectAmt() {
        return disExpectAmt;
    }

    public void setDisExpectAmt(BigDecimal disExpectAmt) {
        this.disExpectAmt = disExpectAmt;
    }

    public BigDecimal getDisPayAmt() {
        return disPayAmt;
    }

    public void setDisPayAmt(BigDecimal disPayAmt) {
        this.disPayAmt = disPayAmt;
    }

    public String getDisExpectFinishRate() {
        return disExpectFinishRate;
    }

    public void setDisExpectFinishRate(String disExpectFinishRate) {
        this.disExpectFinishRate = disExpectFinishRate;
    }

    public String getDisPayFinishRate() {
        return disPayFinishRate;
    }

    public void setDisPayFinishRate(String disPayFinishRate) {
        this.disPayFinishRate = disPayFinishRate;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
