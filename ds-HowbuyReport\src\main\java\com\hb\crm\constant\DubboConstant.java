package com.hb.crm.constant;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

import com.hb.crm.tools.StringUtil;

public class DubboConstant {

	public static final  String ZTDUBBOSUCC="Z0000000";
	
	public static final  String DUBBOSUCC="0000000";
	
	public static final  String RETURNCODE_SUCCESS="0000";//群继系统返回正确编码
	
	public static final String  DISCODE="HB000A001";
	
	public static final String PIGGY_TX_CODE01="319011";  //申购
	
	public static final String PIGGY_TX_CODE02="319013";  //赎回
	
	public static final String PIGGY_TX_CODE03="319015";  //分红
	
	public static final String PIGGY_TX_CODE04="319012";  //快速赎回
	
	public static final String PIGGY_TX_CODE05="319014";  //储蓄罐支付
	
	public static final String PIGGY_TX_CODE06="319016";  //随取
	
	public static final String PIGGY_TX_CODE07="319017";  //随赎
	
	public static final String PIGGY_TX_CODE08="319018";  //随付
	
	//交易申请标志
	public static final Map<String,String> DEALSTAT = new HashMap<String, String>();
	static{
		
		DEALSTAT.put("00", "失败");
		DEALSTAT.put("01",  "成功");
		DEALSTAT.put("02",  "处理中");
	}
	
	//交易确认标志
	public static final Map<String,String> VOLOK = new HashMap<String, String>();
	static{
		
		VOLOK.put("00", "未确认");
		VOLOK.put("01",  "确认");
	}
	
	//交易付款标志
	public static final Map<String,String> TXPMTFLAG = new HashMap<String, String>();
	static{
		TXPMTFLAG.put("00",  "不需付款");
		TXPMTFLAG.put("01", "未付款");
		TXPMTFLAG.put("02",  "付款成功");
		TXPMTFLAG.put("03",  "付款失败");
		TXPMTFLAG.put("04",  "付款中");
		TXPMTFLAG.put("05",  "撤单中");
		TXPMTFLAG.put("06",  "已撤单");
		TXPMTFLAG.put("07",  "撤单失败");
	}
	
	//交易对账状态
	public static final Map<String,String> TxCOMPFLAG = new HashMap<String, String>();
	static{
		TxCOMPFLAG.put("00",  "不需对账");
		TxCOMPFLAG.put("01", "未对账");
		TxCOMPFLAG.put("02",  "匹配");
		TxCOMPFLAG.put("03",  "资金单边");
		TxCOMPFLAG.put("04",  "交易单边");
		TxCOMPFLAG.put("05",  "金额或状态不一致");
	}
	
	//交易渠道
	public static final Map<String,String> TRADECHAN = new HashMap<String, String>();
	static{
		TRADECHAN.put("1",  "柜台");
		TRADECHAN.put("2",  "网站");
		TRADECHAN.put("3",  "电话");
		TRADECHAN.put("4",  "Wap");
		TRADECHAN.put("5",  "app");
	}
	
	
	public static final Map<String,String> PIGGYSTATE  = new HashMap<String, String>();
	static 
	{
		PIGGYSTATE.put("1", "交易失败");
		PIGGYSTATE.put("2",  "等待付款");
		PIGGYSTATE.put("3",  "付款中");
		PIGGYSTATE.put("4",  "付款成功");
		PIGGYSTATE.put("5",  "交易成功");
		PIGGYSTATE.put("6",  "已受理");
		
	}
	
	public static final Map<String,String> PIGGYTYPE = new HashMap<String, String>();
	static{
		PIGGYTYPE.put("1", "存入");
		PIGGYTYPE.put("2",  "取活期(取现、赎回)");
		PIGGYTYPE.put("3",  "存定期");
	}
	

	
	//份额是否可用
	public static final Map<String,String> PIGGYVOLOK  = new HashMap<String, String>();
	
	static{
		PIGGYVOLOK.put("00", "不可用");
		PIGGYVOLOK.put("01", "可用");
	}
	
	public static String getMapValue(String key ,Map<String,String> map){
		String value =null;
		if(StringUtil.isNotNullStr(key)){
			if (map.containsKey(key)) {
				value = map.get(key);
			}
		}
		return value;
	}
	
	
	 public static String yearRateFormate(BigDecimal count){
	    	if(count==null||count.equals(new BigDecimal(0))||count.equals(new BigDecimal("0.00"))){
	    		return "0.0000%";
	    	}
	    	DecimalFormat df = new DecimalFormat("##,##0.0000%");
			return df.format(count);
	    }

}
