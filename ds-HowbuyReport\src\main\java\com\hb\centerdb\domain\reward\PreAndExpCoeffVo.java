package com.hb.centerdb.domain.reward;


import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: 预约与调整系数
 * @reason:
 * @Date: 2020/9/18 18:45
 */
@Data
public class PreAndExpCoeffVo {

    private String preId;
    /**
     * 核算产品类型
     */
    private String accountProductType;
    private String accountDt;
    private BigDecimal foldCoeff;
    private BigDecimal commissionRate;
    private BigDecimal annuallySetAward;
    private BigDecimal secondStockCoeff;
    private BigDecimal stockFeeA;
    private BigDecimal stockFeeB;
    private BigDecimal operFoldCoeff;
    private String accountDtModify;
    private String foldCoeffModify;
    private String commissionRateModify;
    private String annuallySetAwardModify;
    private String secondStockCoeffModify;
    private String stockFeeAModify;
    private String stockFeeBModify;
    private String operFoldCoeffModify;
    private String rowModify;
    private String creator;
    private String createTime;
    private String modor;
    private String updateTime;
    /**
     * 汇率
     */
    private String exchangeRate;

    /** 添加客户来源系数相关 */
    /** 来源系数 */
    private BigDecimal sourceCoeff;
    /** 客户折标系数 */
    private BigDecimal zbCoeff;
    /** 管理系数 */
    private BigDecimal manageCoeff;
    /** 存续A 可选项有：公司资源、投顾资源 */
    private String cxa;
    /** 存续B 可选项有：公司资源、投顾资源 */
    private String cxb;

    /** 是否修改 */
    /** 来源系数 */
    private String sourceCoeffModify;
    /** 客户折标系数 */
    private String zbCoeffModify;
    /** 管理系数 */
    private String manageCoeffModify;
    /** 存续A 可选项有：公司资源、投顾资源 */
    private String cxaModify;
    /** 存续B 可选项有：公司资源、投顾资源 */
    private String cxbModify;
    /**
     * 极差系数
     */
    private String unqualifiedCoeffModify;

    /**
     * 极差系数
     */
    private BigDecimal unqualifiedCoeff;
    /**
     * 折扣金额修改标识
     */
    private String beforeTaxAmtModify;


    private String tradeType;
    private String tradeTypeVal;

    /**
     * 折扣类型 折扣方式值
     */
    private String discountType;
    private String discountWay;
    private String discountWayVal;
    private String discountTypeVal;
    private String sourcetype;
    private String sourcetypeVal;
    /**
     * //币种
     */
    private String currency;
    /**
     * //预计交易日期
     */
    private String expecttradedt;
    /**
     * //实际打款金额
     */
    private BigDecimal realpayamt;
    /**
     * //预约手续费
     */
    private BigDecimal fee;
    /**
     * 税前折扣金额
     */
    private BigDecimal beforetaxamt;



    //预约相关的
    private BigDecimal id;//预约ID
    private String conscustname;//投顾客户号
    private String conscustno;//客户姓名
    private String consname;//投顾姓名
    private String outletName;//所属部门
    private String fundcode;
    private String fundname;
    private String fundtype;
    private String realpayamtdt;//实际打款日期
    private BigDecimal discountRate;//折扣率



    public String getPreId() {
        return preId;
    }

    public void setPreId(String preId) {
        this.preId = preId;
    }

    public String getAccountProductType() {
        return accountProductType;
    }

    public void setAccountProductType(String accountProductType) {
        this.accountProductType = accountProductType;
    }

    public String getAccountDt() {
        return accountDt;
    }

    public void setAccountDt(String accountDt) {
        this.accountDt = accountDt;
    }

    public BigDecimal getFoldCoeff() {
        return foldCoeff;
    }

    public void setFoldCoeff(BigDecimal foldCoeff) {
        this.foldCoeff = foldCoeff;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getAnnuallySetAward() {
        return annuallySetAward;
    }

    public void setAnnuallySetAward(BigDecimal annuallySetAward) {
        this.annuallySetAward = annuallySetAward;
    }

    public BigDecimal getSecondStockCoeff() {
        return secondStockCoeff;
    }

    public void setSecondStockCoeff(BigDecimal secondStockCoeff) {
        this.secondStockCoeff = secondStockCoeff;
    }

    public BigDecimal getStockFeeA() {
        return stockFeeA;
    }

    public void setStockFeeA(BigDecimal stockFeeA) {
        this.stockFeeA = stockFeeA;
    }

    public BigDecimal getStockFeeB() {
        return stockFeeB;
    }

    public void setStockFeeB(BigDecimal stockFeeB) {
        this.stockFeeB = stockFeeB;
    }

    public BigDecimal getOperFoldCoeff() {
        return operFoldCoeff;
    }

    public void setOperFoldCoeff(BigDecimal operFoldCoeff) {
        this.operFoldCoeff = operFoldCoeff;
    }

    public String getAccountDtModify() {
        return accountDtModify;
    }

    public void setAccountDtModify(String accountDtModify) {
        this.accountDtModify = accountDtModify;
    }

    public String getFoldCoeffModify() {
        return foldCoeffModify;
    }

    public void setFoldCoeffModify(String foldCoeffModify) {
        this.foldCoeffModify = foldCoeffModify;
    }

    public String getCommissionRateModify() {
        return commissionRateModify;
    }

    public void setCommissionRateModify(String commissionRateModify) {
        this.commissionRateModify = commissionRateModify;
    }

    public String getAnnuallySetAwardModify() {
        return annuallySetAwardModify;
    }

    public void setAnnuallySetAwardModify(String annuallySetAwardModify) {
        this.annuallySetAwardModify = annuallySetAwardModify;
    }

    public String getSecondStockCoeffModify() {
        return secondStockCoeffModify;
    }

    public void setSecondStockCoeffModify(String secondStockCoeffModify) {
        this.secondStockCoeffModify = secondStockCoeffModify;
    }

    public String getStockFeeAModify() {
        return stockFeeAModify;
    }

    public void setStockFeeAModify(String stockFeeAModify) {
        this.stockFeeAModify = stockFeeAModify;
    }

    public String getStockFeeBModify() {
        return stockFeeBModify;
    }

    public void setStockFeeBModify(String stockFeeBModify) {
        this.stockFeeBModify = stockFeeBModify;
    }

    public String getOperFoldCoeffModify() {
        return operFoldCoeffModify;
    }

    public void setOperFoldCoeffModify(String operFoldCoeffModify) {
        this.operFoldCoeffModify = operFoldCoeffModify;
    }

    public String getRowModify() {
        return rowModify;
    }

    public void setRowModify(String rowModify) {
        this.rowModify = rowModify;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(String exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getSourceCoeff() {
        return sourceCoeff;
    }

    public void setSourceCoeff(BigDecimal sourceCoeff) {
        this.sourceCoeff = sourceCoeff;
    }

    public BigDecimal getZbCoeff() {
        return zbCoeff;
    }

    public void setZbCoeff(BigDecimal zbCoeff) {
        this.zbCoeff = zbCoeff;
    }

    public BigDecimal getManageCoeff() {
        return manageCoeff;
    }

    public void setManageCoeff(BigDecimal manageCoeff) {
        this.manageCoeff = manageCoeff;
    }

    public String getCxa() {
        return cxa;
    }

    public void setCxa(String cxa) {
        this.cxa = cxa;
    }

    public String getCxb() {
        return cxb;
    }

    public void setCxb(String cxb) {
        this.cxb = cxb;
    }

    public String getSourceCoeffModify() {
        return sourceCoeffModify;
    }

    public void setSourceCoeffModify(String sourceCoeffModify) {
        this.sourceCoeffModify = sourceCoeffModify;
    }

    public String getZbCoeffModify() {
        return zbCoeffModify;
    }

    public void setZbCoeffModify(String zbCoeffModify) {
        this.zbCoeffModify = zbCoeffModify;
    }

    public String getManageCoeffModify() {
        return manageCoeffModify;
    }

    public void setManageCoeffModify(String manageCoeffModify) {
        this.manageCoeffModify = manageCoeffModify;
    }

    public String getCxaModify() {
        return cxaModify;
    }

    public void setCxaModify(String cxaModify) {
        this.cxaModify = cxaModify;
    }

    public String getCxbModify() {
        return cxbModify;
    }

    public void setCxbModify(String cxbModify) {
        this.cxbModify = cxbModify;
    }

    public String getUnqualifiedCoeffModify() {
        return unqualifiedCoeffModify;
    }

    public void setUnqualifiedCoeffModify(String unqualifiedCoeffModify) {
        this.unqualifiedCoeffModify = unqualifiedCoeffModify;
    }

    public BigDecimal getUnqualifiedCoeff() {
        return unqualifiedCoeff;
    }

    public void setUnqualifiedCoeff(BigDecimal unqualifiedCoeff) {
        this.unqualifiedCoeff = unqualifiedCoeff;
    }

    public String getBeforeTaxAmtModify() {
        return beforeTaxAmtModify;
    }

    public void setBeforeTaxAmtModify(String beforeTaxAmtModify) {
        this.beforeTaxAmtModify = beforeTaxAmtModify;
    }

    public String getTradeTypeVal() {
        return tradeTypeVal;
    }

    public void setTradeTypeVal(String tradeTypeVal) {
        this.tradeTypeVal = tradeTypeVal;
    }

    public String getDiscountWayVal() {
        return discountWayVal;
    }

    public void setDiscountWayVal(String discountWayVal) {
        this.discountWayVal = discountWayVal;
    }

    public String getDiscountTypeVal() {
        return discountTypeVal;
    }

    public void setDiscountTypeVal(String discountTypeVal) {
        this.discountTypeVal = discountTypeVal;
    }

    public String getSourcetypeVal() {
        return sourcetypeVal;
    }

    public void setSourcetypeVal(String sourcetypeVal) {
        this.sourcetypeVal = sourcetypeVal;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getExpecttradedt() {
        return expecttradedt;
    }

    public void setExpecttradedt(String expecttradedt) {
        this.expecttradedt = expecttradedt;
    }

    public BigDecimal getRealpayamt() {
        return realpayamt;
    }

    public void setRealpayamt(BigDecimal realpayamt) {
        this.realpayamt = realpayamt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getBeforetaxamt() {
        return beforetaxamt;
    }

    public void setBeforetaxamt(BigDecimal beforetaxamt) {
        this.beforetaxamt = beforetaxamt;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public String getDiscountWay() {
        return discountWay;
    }

    public void setDiscountWay(String discountWay) {
        this.discountWay = discountWay;
    }

    public String getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(String sourcetype) {
        this.sourcetype = sourcetype;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getConscustname() {
        return conscustname;
    }

    public void setConscustname(String conscustname) {
        this.conscustname = conscustname;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getOutletName() {
        return outletName;
    }

    public void setOutletName(String outletName) {
        this.outletName = outletName;
    }

    public String getFundcode() {
        return fundcode;
    }

    public void setFundcode(String fundcode) {
        this.fundcode = fundcode;
    }

    public String getFundname() {
        return fundname;
    }

    public void setFundname(String fundname) {
        this.fundname = fundname;
    }

    public String getFundtype() {
        return fundtype;
    }

    public void setFundtype(String fundtype) {
        this.fundtype = fundtype;
    }

    public String getRealpayamtdt() {
        return realpayamtdt;
    }

    public void setRealpayamtdt(String realpayamtdt) {
        this.realpayamtdt = realpayamtdt;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }
}
