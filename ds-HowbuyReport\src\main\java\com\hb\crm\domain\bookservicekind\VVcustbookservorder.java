package com.hb.crm.domain.bookservicekind;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类VVcustbookservorder.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class VVcustbookservorder implements Serializable {

private static final long serialVersionUID = 1L;

	private Integer id;
	
	private String email;
	
	private String mobile;
	
	private Integer bookserviceid;
	
	private String custno;
	
	private String recvemailflag;
	
	private String recvmsgflag;
	
	private String content;
	
	private String custtype;
	
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	public Integer getBookserviceid() {
		return this.bookserviceid;
	}

	public void setBookserviceid(Integer bookserviceid) {
		this.bookserviceid = bookserviceid;
	}
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getRecvemailflag() {
		return this.recvemailflag;
	}

	public void setRecvemailflag(String recvemailflag) {
		this.recvemailflag = recvemailflag;
	}
	
	public String getRecvmsgflag() {
		return this.recvmsgflag;
	}

	public void setRecvmsgflag(String recvmsgflag) {
		this.recvmsgflag = recvmsgflag;
	}
	
	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	public String getCusttype() {
		return this.custtype;
	}

	public void setCusttype(String custtype) {
		this.custtype = custtype;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
