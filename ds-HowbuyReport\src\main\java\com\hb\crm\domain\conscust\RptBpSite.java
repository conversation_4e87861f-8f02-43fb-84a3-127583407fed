package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类RptBpSite.java
 * @version 1.0
 */
public class RptBpSite implements Serializable {

private static final long serialVersionUID = 1L;

	private String outletCode;
	
	private String outletName;
	
	private String regionCode;
	
	private String outletType;
	
	private String startTm;
	
	private String endTm;
	
	private String recStat;
	
	private String checkFlag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String creDt;
	
	private String modDt;
	
	private String addr;
	
	private String telNo;
	
	private String contact;
	
	private String disCode;
	
	private String hzlx;
	
	private String hzlxCode;
	
	public String getOutletCode() {
		return this.outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}
	
	public String getOutletName() {
		return this.outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}
	
	public String getRegionCode() {
		return this.regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}
	
	public String getOutletType() {
		return this.outletType;
	}

	public void setOutletType(String outletType) {
		this.outletType = outletType;
	}
	
	public String getStartTm() {
		return this.startTm;
	}

	public void setStartTm(String startTm) {
		this.startTm = startTm;
	}
	
	public String getEndTm() {
		return this.endTm;
	}

	public void setEndTm(String endTm) {
		this.endTm = endTm;
	}
	
	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}
	
	public String getCheckFlag() {
		return this.checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}
	
	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}
	
	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}
	
	public String getTelNo() {
		return this.telNo;
	}

	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}
	
	public String getContact() {
		return this.contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}
	
	public String getDisCode() {
		return this.disCode;
	}

	public void setDisCode(String disCode) {
		this.disCode = disCode;
	}
	
	public String getHzlx() {
		return this.hzlx;
	}

	public void setHzlx(String hzlx) {
		this.hzlx = hzlx;
	}
	
	public String getHzlxCode() {
		return this.hzlxCode;
	}

	public void setHzlxCode(String hzlxCode) {
		this.hzlxCode = hzlxCode;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
