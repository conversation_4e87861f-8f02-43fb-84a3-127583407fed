package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类SurveyAnswerPtsRec.java
 * @version 1.0
 */
public class SurveyAnswerPtsRec implements Serializable {

	private static final long serialVersionUID = 1L;

	private String appserialno;

	private String tradedt;

	private String surveyserialno;

	private String custno;

	private String pcustid;

	private String surveyid;

	private String setid;

	private String questionid;

	private Double qweight;

	private String acode;

	private String acontent;

	private Double apoint;

	private Double wapoint;

	private String memo;

	private String stimestamp;

	private String conscustno;

	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}

	public String getTradedt() {
		return this.tradedt;
	}

	public void setTradedt(String tradedt) {
		this.tradedt = tradedt;
	}

	public String getSurveyserialno() {
		return this.surveyserialno;
	}

	public void setSurveyserialno(String surveyserialno) {
		this.surveyserialno = surveyserialno;
	}

	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}

	public String getPcustid() {
		return this.pcustid;
	}

	public void setPcustid(String pcustid) {
		this.pcustid = pcustid;
	}

	public String getSurveyid() {
		return this.surveyid;
	}

	public void setSurveyid(String surveyid) {
		this.surveyid = surveyid;
	}

	public String getSetid() {
		return this.setid;
	}

	public void setSetid(String setid) {
		this.setid = setid;
	}

	public String getQuestionid() {
		return this.questionid;
	}

	public void setQuestionid(String questionid) {
		this.questionid = questionid;
	}

	public Double getQweight() {
		return this.qweight;
	}

	public void setQweight(Double qweight) {
		this.qweight = qweight;
	}

	public String getAcode() {
		return this.acode;
	}

	public void setAcode(String acode) {
		this.acode = acode;
	}

	public String getAcontent() {
		return this.acontent;
	}

	public void setAcontent(String acontent) {
		this.acontent = acontent;
	}

	public Double getApoint() {
		return this.apoint;
	}

	public void setApoint(Double apoint) {
		this.apoint = apoint;
	}

	public Double getWapoint() {
		return this.wapoint;
	}

	public void setWapoint(Double wapoint) {
		this.wapoint = wapoint;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(String stimestamp) {
		this.stimestamp = stimestamp;
	}

	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
