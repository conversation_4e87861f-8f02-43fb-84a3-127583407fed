package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class PrivFundBal implements Serializable {

	private static final long serialVersionUID = 1L;

	private String tradeDt; // 日期

	private String glrm; //

	private String jgmc; //

	private String fundCode; // 基金code
	private String jjmc; // 基金名称

	private String productCode; // 产品code
	private String productName; // 产品名称

    private String u1name;//中心
    private String u2name;//区域(二级部门)
	private String u3name;//部门

	private String outletCode; // 部门

	private String teamCode; // 团队

	private String orgName; // 部门名称

	private String consName; // 投顾名称

	private String consCode; // 投顾code

	private String custName; // 客户名称

	private String consCustno; // 客户号

	private String invstTypename; // 证件号码

	private Double buyRmb = 0d; // 总持有份额

	private Double balVol = 0d; // 总持有份额

	private Double marketCap = 0d; // 总持有份额

	private String invstType; // 投资者类型
	private String custType; // 客户类型

	private String crsStat; //

    private String productType;//产品类型

    private String fundTypeInner;//产品类型(对内)

	private String productClass1;//一级分类
	private String productClass2;//二级分类
	private String productClass3;//三级分类

	private String bqxlmc; //一级策略
	private String sftzhw; //是否投资海外
	private Double hwzczbxx = 0d;//海外资产占比下限（%）
	private Double hwzczbsx = 0d;//海外资产占比上限（%）
	private Double hwzczb = 0d;//海外资产占比

	public String getInvstType() {
		return invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getCrsStat() {
		return crsStat;
	}

	public void setCrsStat(String crsStat) {
		this.crsStat = crsStat;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getGlrm() {
		return glrm;
	}

	public void setGlrm(String glrm) {
		this.glrm = glrm;
	}

	public String getJgmc() {
		return jgmc;
	}

	public void setJgmc(String jgmc) {
		this.jgmc = jgmc;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getJjmc() {
		return jjmc;
	}

	public void setJjmc(String jjmc) {
		this.jjmc = jjmc;
	}

	public String getOutletCode() {
		return outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getTeamCode() {
		return teamCode;
	}

	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getConsCustno() {
		return consCustno;
	}

	public void setConsCustno(String consCustno) {
		this.consCustno = consCustno;
	}

	public String getInvstTypename() {
		return invstTypename;
	}

	public void setInvstTypename(String invstTypename) {
		this.invstTypename = invstTypename;
	}

	public Double getBuyRmb() {
		return buyRmb;
	}

	public void setBuyRmb(Double buyRmb) {
		this.buyRmb = buyRmb;
	}

	public Double getBalVol() {
		return balVol;
	}

	public void setBalVol(Double balVol) {
		this.balVol = balVol;
	}

	public Double getMarketCap() {
		return marketCap;
	}

	public void setMarketCap(Double marketCap) {
		this.marketCap = marketCap;
	}

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getFundTypeInner() {
        return fundTypeInner;
    }

    public void setFundTypeInner(String fundTypeInner) {
        this.fundTypeInner = fundTypeInner;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

	public String getBqxlmc() {
		return bqxlmc;
	}

	public void setBqxlmc(String bqxlmc) {
		this.bqxlmc = bqxlmc;
	}

	public String getSftzhw() {
		return sftzhw;
	}

	public void setSftzhw(String sftzhw) {
		this.sftzhw = sftzhw;
	}

	public Double getHwzczbxx() {
		return hwzczbxx;
	}

	public void setHwzczbxx(Double hwzczbxx) {
		this.hwzczbxx = hwzczbxx;
	}

	public Double getHwzczbsx() {
		return hwzczbsx;
	}

	public void setHwzczbsx(Double hwzczbsx) {
		this.hwzczbsx = hwzczbsx;
	}

	public Double getHwzczb() {
		return hwzczb;
	}

	public void setHwzczb(Double hwzczb) {
		this.hwzczb = hwzczb;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getU3name() {
		return u3name;
	}

	public void setU3name(String u3name) {
		this.u3name = u3name;
	}

	public String getCustType() {
		return custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getProductClass1() {
		return productClass1;
	}

	public void setProductClass1(String productClass1) {
		this.productClass1 = productClass1;
	}

	public String getProductClass2() {
		return productClass2;
	}

	public void setProductClass2(String productClass2) {
		this.productClass2 = productClass2;
	}

	public String getProductClass3() {
		return productClass3;
	}

	public void setProductClass3(String productClass3) {
		this.productClass3 = productClass3;
	}
}
