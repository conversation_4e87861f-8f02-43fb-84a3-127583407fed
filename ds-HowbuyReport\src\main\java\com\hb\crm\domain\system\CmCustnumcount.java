package com.hb.crm.domain.system;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 实体类CmCustnumcount.java
 * @version 1.0
 * @created
 */
public class CmCustnumcount implements Serializable {
	private static final long serialVersionUID = 1L;
	private String sdate;							//统计日期
	private Integer webcustnum;						//注册会员客户数                                                    
	private Integer webcustyesnum;                  //注册会员已分配客户数                                                 
	private Integer webcustnotnum;                  //注册会员未分配客户数                                                 
	private Integer addwebcustnum;                  //统计当天新增注册会员客户数                                              
	private Integer bookpersonnum;                  //订阅客户数                                                      
	private Integer bookpersonyesnum;               //订阅客户已分配客户数                                                 
	private Integer bookpersonnotnum;               //订阅客户未分配客户数                                                 
	private Integer addbookpersonnum;               //统计当天新增订阅客户数                                                
	private Integer bookingcustnum;                 //预约客户数                                                      
	private Integer bookingcustyesnum;              //预约客户已分配客户数                                                 
	private Integer bookingcustnotnum;              //预约客户未分配客户数                                                 
	private Integer addbookingcustnum;              //统计当天新增预约客户数                                                
	private Integer potentialcustnum;               //公募交易客户数                                                      
	private Integer potentialcustyesnum;            //公募交易客户已分配客户数                                                 
	private Integer potentialcustnotnum;            //公募交易客户未分配客户数                                                 
	private Integer addpotentialcustnum;            //统计当天新增公募交易客户数                                                
	private Integer rscustnum;                      //RS客户数（公募交易客户）                                              
	private Integer addrscustnum;                   //统计当天新增RS客户数                                                
	private Integer rscustmarketcapnum;             //RS客户有存量客户数                                                 
	private Integer yylccustnum;                    //盈盈理财客户数                                       
	private Integer addyylccustnum;                 //统计当天新增盈盈理财客户数                                     
	private Integer yylccustmarketcapnum;           //盈盈理财客户有存量客户数                                    
	private Integer pubothercustnum;                //其他公募客户                                     
	private Integer addpubothercustnum;             //统计当天新增其他公募客户                                     
	private Integer pubothercustmarketcapnum;       //其他公募客户有存量客户数                                    
	private Integer rtcustnum;                      //RT客户数（公募高端客户）          
	private Integer addrtcustnum;                   //统计当天新增RT客户数             
	private Integer rtcustmarketcapnum;             //RT客户有存量客户数             
	private Integer iccustnum;                      //IC客户数（私募客户）            
	private Integer addiccustnum;                   //统计当天新增IC客户数            
	private Integer iccustmarketcapnum;             //IC客户有存量客户数             
	private Integer othercustnum;                   //其他（未在RS/RT/IC及四个未分配库）客户数                                                                                   
	private Integer addothercustnum;                //统计当天新增其他（未在RS/RT/IC及四个未分配库）客户数                                                                                 
	private Integer othercustmarketcapnum;          //其他（未在RS/RT/IC及四个未分配库）客户有存量客户数                                                                                  
	private Integer unallocatedcustnum;             //未分配客户数                                                                                                     
	private Integer addunallocatedcustnum;          //统计当天新增未分配客户数				                                                                                     
	private Integer removeunallocatedcustnum;       //统计当天移出未分配客户数                                                                                                   
	private Integer uncallcustnum;                  //未接通库客户数                                                  
	private Integer adduncallcustnum;               //当天新增未接通库客户数                                                  
	private Integer removeuncallcustnum;            //当天移出未接通库客户数                                                  
	private Integer notacceptcustnum;               //无意向库客户数                                                    
	private Integer addnotacceptcustnum;            //当天新增无意向库客户数                                                  
	private Integer removenotacceptcustnum;         //当天移出无意向库客户数                                                  
	private Integer sameprocustnum;                 //同行库客户数                                         
	private Integer addsameprocustnum;              //当天新增同行库客户数                                         
	private Integer removesameprocustnum;           //当天移出同行库客户数                                         
	private Integer wrongcustnum;                   //错误信息库客户数                                           
	private Integer addwrongcustnum;                //当天新增错误库客户数                                             
	private Integer removewrongcustnum;             //当天移出错误库客户数                                             
	private Integer specustnum;                     //特殊客户数             
	private Integer addspecustnum;                  //当天新增特殊客户数			
	private Integer removespecustnum;               //当天移出特殊客户数         
	private Integer otherunallocatedcustnum;        //其他未分配库客户数         
	private Integer addotherunallocatedcustnum;     //当天新增其他未分配库客户数     
	private Integer removeotherunallocatedcustnum;  //当天移出其他未分配库客户数     
	private Date creattime;							//创建时间
	private Date updatetime;						//更新时间
	private String creator;							//创建者
	private String modifier;						//修改者
	private Integer paranum1;						//当天新增注册已分配客户数    
	private Integer paranum2;                       //当天新增注册未分配客户数    
	private Integer paranum3;                       //当天新增订阅已分配客户数    
	private Integer paranum4;                       //当天新增订阅未分配客户数    
	private Integer paranum5;                       //当天新增预约已分配客户数    
	private Integer paranum6;                       //当天新增预约未分配客户数    
	private Integer paranum7;                       //当天新增公募交易已分配客户数
	private Integer paranum8;                       //当天新增公募交易未分配客户数
	private Integer paranum9;                       
	private Integer paranum10;
	private Integer paranum11;						//呼入客户总客户数
	private Integer paranum12;						//呼入客户当天新增（或减少）客户数
	private Integer paranum13;						//投顾开发总客户数
	private Integer paranum14;						//投顾开发当天新增（或减少）客户数
	private Integer paranum15;						//内部活动总客户数
	private Integer paranum16;						//内部活动当天新增（或减少）客户数
	private Integer paranum17;						//网站留痕总客户数
	private Integer paranum18;						//网站留痕当天新增（或减少）客户数
	private Integer paranum19;						//未知总客户数           
	private Integer paranum20;						//未知当天新增（或减少）客户数   
	private Integer paranum21;						//双响炮总客户数          
	private Integer paranum22;						//双响炮当天新增（或减少）客户数  
	private Integer paranum23;						//MGM总客户数          
	private Integer paranum24;						//MGM当天新增（或减少）客户数  
	private Integer paranum25;						//自主开户总客户数         
	private Integer paranum26;						//自主开户当天新增（或减少）客户数 
	private Integer paranum27;						//名单客户总客户数
	private Integer paranum28;						//名单客户已分配客户数
	private Integer paranum29;						//名单客户未分配客户数
	private Integer paranum30;						//统计当天新增名单客户数
	private Integer paranum31;						//当天新增名单客户已分配客户数
	private Integer paranum32;						//当天新增名单客户未分配客户数
	private Integer paranum33;
	private Integer paranum34;
	private Integer paranum35;
	private Integer paranum36;
	private Integer paranum37;
	private Integer paranum38;
	private Integer paranum39;
	private Integer paranum40;
	
	//统计部分字段 
	private Integer totalLeadsCustNum;				//Leads总客户数
	private Integer totalAddLeadsCustNum;			//Leads新增客户数
	private Integer totalInCustNum;					//流入总客户数
	private Integer totalAddInCustNum;				//流入新增客户数
	private Integer totalOutRsCustNum;				//流出公募总客户数
	private Integer totalAddOutRsCustNum;			//流出公募新增客户数
	private Integer totalRtCustNum;					//流出高净值总客户数
	private Integer totalAddRtCustNum;				//流出高净值新增客户数
	private Integer totalNotConsCustNum;			//流出无投顾总客户数
	private Integer totalAddNotConsCustNum;			//流出无投顾新增客户数
	private Integer totalUnAllocatedCustNum;		//未处理总客户数
	private Integer totalAddUnAllocatedNum;			//未处理新增客户数
	private Integer totalAllocatedVirtualNum;		//虚拟库客户数
	private Integer totalAddAllocatedVirtualNum;	//虚拟库新增客户数
	
	public String getSdate() {
		return this.sdate;
	}

	public void setSdate(String sdate) {
		this.sdate = sdate;
	}

	public Integer getWebcustnum() {
		return this.webcustnum;
	}

	public void setWebcustnum(Integer webcustnum) {
		this.webcustnum = webcustnum;
	}

	public Integer getWebcustyesnum() {
		return this.webcustyesnum;
	}

	public void setWebcustyesnum(Integer webcustyesnum) {
		this.webcustyesnum = webcustyesnum;
	}

	public Integer getWebcustnotnum() {
		return this.webcustnotnum;
	}

	public void setWebcustnotnum(Integer webcustnotnum) {
		this.webcustnotnum = webcustnotnum;
	}

	public Integer getAddwebcustnum() {
		return this.addwebcustnum;
	}

	public void setAddwebcustnum(Integer addwebcustnum) {
		this.addwebcustnum = addwebcustnum;
	}

	public Integer getBookpersonnum() {
		return this.bookpersonnum;
	}

	public void setBookpersonnum(Integer bookpersonnum) {
		this.bookpersonnum = bookpersonnum;
	}

	public Integer getBookpersonyesnum() {
		return this.bookpersonyesnum;
	}

	public void setBookpersonyesnum(Integer bookpersonyesnum) {
		this.bookpersonyesnum = bookpersonyesnum;
	}

	public Integer getBookpersonnotnum() {
		return this.bookpersonnotnum;
	}

	public void setBookpersonnotnum(Integer bookpersonnotnum) {
		this.bookpersonnotnum = bookpersonnotnum;
	}

	public Integer getAddbookpersonnum() {
		return this.addbookpersonnum;
	}

	public void setAddbookpersonnum(Integer addbookpersonnum) {
		this.addbookpersonnum = addbookpersonnum;
	}

	public Integer getBookingcustnum() {
		return this.bookingcustnum;
	}

	public void setBookingcustnum(Integer bookingcustnum) {
		this.bookingcustnum = bookingcustnum;
	}

	public Integer getBookingcustyesnum() {
		return this.bookingcustyesnum;
	}

	public void setBookingcustyesnum(Integer bookingcustyesnum) {
		this.bookingcustyesnum = bookingcustyesnum;
	}

	public Integer getBookingcustnotnum() {
		return this.bookingcustnotnum;
	}

	public void setBookingcustnotnum(Integer bookingcustnotnum) {
		this.bookingcustnotnum = bookingcustnotnum;
	}

	public Integer getAddbookingcustnum() {
		return this.addbookingcustnum;
	}

	public void setAddbookingcustnum(Integer addbookingcustnum) {
		this.addbookingcustnum = addbookingcustnum;
	}

	public Integer getPotentialcustnum() {
		return this.potentialcustnum;
	}

	public void setPotentialcustnum(Integer potentialcustnum) {
		this.potentialcustnum = potentialcustnum;
	}

	public Integer getPotentialcustyesnum() {
		return this.potentialcustyesnum;
	}

	public void setPotentialcustyesnum(Integer potentialcustyesnum) {
		this.potentialcustyesnum = potentialcustyesnum;
	}

	public Integer getPotentialcustnotnum() {
		return this.potentialcustnotnum;
	}

	public void setPotentialcustnotnum(Integer potentialcustnotnum) {
		this.potentialcustnotnum = potentialcustnotnum;
	}

	public Integer getAddpotentialcustnum() {
		return this.addpotentialcustnum;
	}

	public void setAddpotentialcustnum(Integer addpotentialcustnum) {
		this.addpotentialcustnum = addpotentialcustnum;
	}

	public Integer getRscustnum() {
		return this.rscustnum;
	}

	public void setRscustnum(Integer rscustnum) {
		this.rscustnum = rscustnum;
	}

	public Integer getAddrscustnum() {
		return this.addrscustnum;
	}

	public void setAddrscustnum(Integer addrscustnum) {
		this.addrscustnum = addrscustnum;
	}

	public Integer getRscustmarketcapnum() {
		return this.rscustmarketcapnum;
	}

	public void setRscustmarketcapnum(Integer rscustmarketcapnum) {
		this.rscustmarketcapnum = rscustmarketcapnum;
	}

	public Integer getYylccustnum() {
		return this.yylccustnum;
	}

	public void setYylccustnum(Integer yylccustnum) {
		this.yylccustnum = yylccustnum;
	}

	public Integer getAddyylccustnum() {
		return this.addyylccustnum;
	}

	public void setAddyylccustnum(Integer addyylccustnum) {
		this.addyylccustnum = addyylccustnum;
	}

	public Integer getYylccustmarketcapnum() {
		return this.yylccustmarketcapnum;
	}

	public void setYylccustmarketcapnum(Integer yylccustmarketcapnum) {
		this.yylccustmarketcapnum = yylccustmarketcapnum;
	}

	public Integer getPubothercustnum() {
		return this.pubothercustnum;
	}

	public void setPubothercustnum(Integer pubothercustnum) {
		this.pubothercustnum = pubothercustnum;
	}

	public Integer getAddpubothercustnum() {
		return this.addpubothercustnum;
	}

	public void setAddpubothercustnum(Integer addpubothercustnum) {
		this.addpubothercustnum = addpubothercustnum;
	}

	public Integer getPubothercustmarketcapnum() {
		return this.pubothercustmarketcapnum;
	}

	public void setPubothercustmarketcapnum(Integer pubothercustmarketcapnum) {
		this.pubothercustmarketcapnum = pubothercustmarketcapnum;
	}

	public Integer getRtcustnum() {
		return this.rtcustnum;
	}

	public void setRtcustnum(Integer rtcustnum) {
		this.rtcustnum = rtcustnum;
	}

	public Integer getAddrtcustnum() {
		return this.addrtcustnum;
	}

	public void setAddrtcustnum(Integer addrtcustnum) {
		this.addrtcustnum = addrtcustnum;
	}

	public Integer getRtcustmarketcapnum() {
		return this.rtcustmarketcapnum;
	}

	public void setRtcustmarketcapnum(Integer rtcustmarketcapnum) {
		this.rtcustmarketcapnum = rtcustmarketcapnum;
	}

	public Integer getIccustnum() {
		return this.iccustnum;
	}

	public void setIccustnum(Integer iccustnum) {
		this.iccustnum = iccustnum;
	}

	public Integer getAddiccustnum() {
		return this.addiccustnum;
	}

	public void setAddiccustnum(Integer addiccustnum) {
		this.addiccustnum = addiccustnum;
	}

	public Integer getIccustmarketcapnum() {
		return this.iccustmarketcapnum;
	}

	public void setIccustmarketcapnum(Integer iccustmarketcapnum) {
		this.iccustmarketcapnum = iccustmarketcapnum;
	}

	public Integer getOthercustnum() {
		return this.othercustnum;
	}

	public void setOthercustnum(Integer othercustnum) {
		this.othercustnum = othercustnum;
	}

	public Integer getAddothercustnum() {
		return this.addothercustnum;
	}

	public void setAddothercustnum(Integer addothercustnum) {
		this.addothercustnum = addothercustnum;
	}

	public Integer getOthercustmarketcapnum() {
		return this.othercustmarketcapnum;
	}

	public void setOthercustmarketcapnum(Integer othercustmarketcapnum) {
		this.othercustmarketcapnum = othercustmarketcapnum;
	}

	public Integer getUnallocatedcustnum() {
		return this.unallocatedcustnum;
	}

	public void setUnallocatedcustnum(Integer unallocatedcustnum) {
		this.unallocatedcustnum = unallocatedcustnum;
	}

	public Integer getAddunallocatedcustnum() {
		return this.addunallocatedcustnum;
	}

	public void setAddunallocatedcustnum(Integer addunallocatedcustnum) {
		this.addunallocatedcustnum = addunallocatedcustnum;
	}

	public Integer getRemoveunallocatedcustnum() {
		return this.removeunallocatedcustnum;
	}

	public void setRemoveunallocatedcustnum(Integer removeunallocatedcustnum) {
		this.removeunallocatedcustnum = removeunallocatedcustnum;
	}

	public Integer getUncallcustnum() {
		return this.uncallcustnum;
	}

	public void setUncallcustnum(Integer uncallcustnum) {
		this.uncallcustnum = uncallcustnum;
	}

	public Integer getAdduncallcustnum() {
		return this.adduncallcustnum;
	}

	public void setAdduncallcustnum(Integer adduncallcustnum) {
		this.adduncallcustnum = adduncallcustnum;
	}

	public Integer getRemoveuncallcustnum() {
		return this.removeuncallcustnum;
	}

	public void setRemoveuncallcustnum(Integer removeuncallcustnum) {
		this.removeuncallcustnum = removeuncallcustnum;
	}

	public Integer getNotacceptcustnum() {
		return this.notacceptcustnum;
	}

	public void setNotacceptcustnum(Integer notacceptcustnum) {
		this.notacceptcustnum = notacceptcustnum;
	}

	public Integer getAddnotacceptcustnum() {
		return this.addnotacceptcustnum;
	}

	public void setAddnotacceptcustnum(Integer addnotacceptcustnum) {
		this.addnotacceptcustnum = addnotacceptcustnum;
	}

	public Integer getRemovenotacceptcustnum() {
		return this.removenotacceptcustnum;
	}

	public void setRemovenotacceptcustnum(Integer removenotacceptcustnum) {
		this.removenotacceptcustnum = removenotacceptcustnum;
	}

	public Integer getSameprocustnum() {
		return this.sameprocustnum;
	}

	public void setSameprocustnum(Integer sameprocustnum) {
		this.sameprocustnum = sameprocustnum;
	}

	public Integer getAddsameprocustnum() {
		return this.addsameprocustnum;
	}

	public void setAddsameprocustnum(Integer addsameprocustnum) {
		this.addsameprocustnum = addsameprocustnum;
	}

	public Integer getRemovesameprocustnum() {
		return this.removesameprocustnum;
	}

	public void setRemovesameprocustnum(Integer removesameprocustnum) {
		this.removesameprocustnum = removesameprocustnum;
	}

	public Integer getWrongcustnum() {
		return this.wrongcustnum;
	}

	public void setWrongcustnum(Integer wrongcustnum) {
		this.wrongcustnum = wrongcustnum;
	}

	public Integer getAddwrongcustnum() {
		return this.addwrongcustnum;
	}

	public void setAddwrongcustnum(Integer addwrongcustnum) {
		this.addwrongcustnum = addwrongcustnum;
	}

	public Integer getRemovewrongcustnum() {
		return this.removewrongcustnum;
	}

	public void setRemovewrongcustnum(Integer removewrongcustnum) {
		this.removewrongcustnum = removewrongcustnum;
	}

	public Integer getSpecustnum() {
		return this.specustnum;
	}

	public void setSpecustnum(Integer specustnum) {
		this.specustnum = specustnum;
	}

	public Integer getAddspecustnum() {
		return this.addspecustnum;
	}

	public void setAddspecustnum(Integer addspecustnum) {
		this.addspecustnum = addspecustnum;
	}

	public Integer getRemovespecustnum() {
		return this.removespecustnum;
	}

	public void setRemovespecustnum(Integer removespecustnum) {
		this.removespecustnum = removespecustnum;
	}

	public Integer getOtherunallocatedcustnum() {
		return this.otherunallocatedcustnum;
	}

	public void setOtherunallocatedcustnum(Integer otherunallocatedcustnum) {
		this.otherunallocatedcustnum = otherunallocatedcustnum;
	}

	public Integer getAddotherunallocatedcustnum() {
		return this.addotherunallocatedcustnum;
	}

	public void setAddotherunallocatedcustnum(Integer addotherunallocatedcustnum) {
		this.addotherunallocatedcustnum = addotherunallocatedcustnum;
	}

	public Integer getRemoveotherunallocatedcustnum() {
		return this.removeotherunallocatedcustnum;
	}

	public void setRemoveotherunallocatedcustnum(
			Integer removeotherunallocatedcustnum) {
		this.removeotherunallocatedcustnum = removeotherunallocatedcustnum;
	}

	public Integer getParanum1() {
		return this.paranum1;
	}

	public void setParanum1(Integer paranum1) {
		this.paranum1 = paranum1;
	}

	public Integer getParanum2() {
		return this.paranum2;
	}

	public void setParanum2(Integer paranum2) {
		this.paranum2 = paranum2;
	}

	public Integer getParanum3() {
		return this.paranum3;
	}

	public void setParanum3(Integer paranum3) {
		this.paranum3 = paranum3;
	}

	public Integer getParanum4() {
		return this.paranum4;
	}

	public void setParanum4(Integer paranum4) {
		this.paranum4 = paranum4;
	}

	public Integer getParanum5() {
		return this.paranum5;
	}

	public void setParanum5(Integer paranum5) {
		this.paranum5 = paranum5;
	}

	public Integer getParanum6() {
		return this.paranum6;
	}

	public void setParanum6(Integer paranum6) {
		this.paranum6 = paranum6;
	}

	public Integer getParanum7() {
		return this.paranum7;
	}

	public void setParanum7(Integer paranum7) {
		this.paranum7 = paranum7;
	}

	public Integer getParanum8() {
		return this.paranum8;
	}

	public void setParanum8(Integer paranum8) {
		this.paranum8 = paranum8;
	}

	public Integer getParanum9() {
		return this.paranum9;
	}

	public void setParanum9(Integer paranum9) {
		this.paranum9 = paranum9;
	}

	public Integer getParanum10() {
		return this.paranum10;
	}

	public void setParanum10(Integer paranum10) {
		this.paranum10 = paranum10;
	}
	
	public Integer getParanum11() {
		return paranum11;
	}

	public void setParanum11(Integer paranum11) {
		this.paranum11 = paranum11;
	}

	public Integer getParanum12() {
		return paranum12;
	}

	public void setParanum12(Integer paranum12) {
		this.paranum12 = paranum12;
	}

	public Integer getParanum13() {
		return paranum13;
	}

	public void setParanum13(Integer paranum13) {
		this.paranum13 = paranum13;
	}

	public Integer getParanum14() {
		return paranum14;
	}

	public void setParanum14(Integer paranum14) {
		this.paranum14 = paranum14;
	}

	public Integer getParanum15() {
		return paranum15;
	}

	public void setParanum15(Integer paranum15) {
		this.paranum15 = paranum15;
	}

	public Integer getParanum16() {
		return paranum16;
	}

	public void setParanum16(Integer paranum16) {
		this.paranum16 = paranum16;
	}

	public Integer getParanum17() {
		return paranum17;
	}

	public void setParanum17(Integer paranum17) {
		this.paranum17 = paranum17;
	}

	public Integer getParanum18() {
		return paranum18;
	}

	public void setParanum18(Integer paranum18) {
		this.paranum18 = paranum18;
	}

	public Integer getParanum19() {
		return paranum19;
	}

	public void setParanum19(Integer paranum19) {
		this.paranum19 = paranum19;
	}

	public Integer getParanum20() {
		return paranum20;
	}

	public void setParanum20(Integer paranum20) {
		this.paranum20 = paranum20;
	}

	public Integer getParanum21() {
		return paranum21;
	}

	public void setParanum21(Integer paranum21) {
		this.paranum21 = paranum21;
	}

	public Integer getParanum22() {
		return paranum22;
	}

	public void setParanum22(Integer paranum22) {
		this.paranum22 = paranum22;
	}

	public Integer getParanum23() {
		return paranum23;
	}

	public void setParanum23(Integer paranum23) {
		this.paranum23 = paranum23;
	}

	public Integer getParanum24() {
		return paranum24;
	}

	public void setParanum24(Integer paranum24) {
		this.paranum24 = paranum24;
	}

	public Integer getParanum25() {
		return paranum25;
	}

	public void setParanum25(Integer paranum25) {
		this.paranum25 = paranum25;
	}

	public Integer getParanum26() {
		return paranum26;
	}

	public void setParanum26(Integer paranum26) {
		this.paranum26 = paranum26;
	}

	public Integer getParanum27() {
		return paranum27;
	}

	public void setParanum27(Integer paranum27) {
		this.paranum27 = paranum27;
	}

	public Integer getParanum28() {
		return paranum28;
	}

	public void setParanum28(Integer paranum28) {
		this.paranum28 = paranum28;
	}

	public Integer getParanum29() {
		return paranum29;
	}

	public void setParanum29(Integer paranum29) {
		this.paranum29 = paranum29;
	}

	public Integer getParanum30() {
		return paranum30;
	}

	public void setParanum30(Integer paranum30) {
		this.paranum30 = paranum30;
	}

	public Integer getParanum31() {
		return paranum31;
	}

	public void setParanum31(Integer paranum31) {
		this.paranum31 = paranum31;
	}

	public Integer getParanum32() {
		return paranum32;
	}

	public void setParanum32(Integer paranum32) {
		this.paranum32 = paranum32;
	}

	public Integer getParanum33() {
		return paranum33;
	}

	public void setParanum33(Integer paranum33) {
		this.paranum33 = paranum33;
	}

	public Integer getParanum34() {
		return paranum34;
	}

	public void setParanum34(Integer paranum34) {
		this.paranum34 = paranum34;
	}

	public Integer getParanum35() {
		return paranum35;
	}

	public void setParanum35(Integer paranum35) {
		this.paranum35 = paranum35;
	}

	public Integer getParanum36() {
		return paranum36;
	}

	public void setParanum36(Integer paranum36) {
		this.paranum36 = paranum36;
	}

	public Integer getParanum37() {
		return paranum37;
	}

	public void setParanum37(Integer paranum37) {
		this.paranum37 = paranum37;
	}

	public Integer getParanum38() {
		return paranum38;
	}

	public void setParanum38(Integer paranum38) {
		this.paranum38 = paranum38;
	}

	public Integer getParanum39() {
		return paranum39;
	}

	public void setParanum39(Integer paranum39) {
		this.paranum39 = paranum39;
	}

	public Integer getParanum40() {
		return paranum40;
	}

	public void setParanum40(Integer paranum40) {
		this.paranum40 = paranum40;
	}

	public Date getCreattime() {
		return this.creattime;
	}

	public void setCreattime(Date creattime) {
		this.creattime = creattime;
	}

	public Date getUpdatetime() {
		return this.updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Integer getTotalLeadsCustNum() {
		return totalLeadsCustNum;
	}

	public void setTotalLeadsCustNum(Integer totalLeadsCustNum) {
		this.totalLeadsCustNum = totalLeadsCustNum;
	}

	public Integer getTotalAddLeadsCustNum() {
		return totalAddLeadsCustNum;
	}

	public void setTotalAddLeadsCustNum(Integer totalAddLeadsCustNum) {
		this.totalAddLeadsCustNum = totalAddLeadsCustNum;
	}

	public Integer getTotalInCustNum() {
		return totalInCustNum;
	}

	public void setTotalInCustNum(Integer totalInCustNum) {
		this.totalInCustNum = totalInCustNum;
	}

	public Integer getTotalAddInCustNum() {
		return totalAddInCustNum;
	}

	public void setTotalAddInCustNum(Integer totalAddInCustNum) {
		this.totalAddInCustNum = totalAddInCustNum;
	}

	public Integer getTotalOutRsCustNum() {
		return totalOutRsCustNum;
	}

	public void setTotalOutRsCustNum(Integer totalOutRsCustNum) {
		this.totalOutRsCustNum = totalOutRsCustNum;
	}

	public Integer getTotalAddOutRsCustNum() {
		return totalAddOutRsCustNum;
	}

	public void setTotalAddOutRsCustNum(Integer totalAddOutRsCustNum) {
		this.totalAddOutRsCustNum = totalAddOutRsCustNum;
	}

	public Integer getTotalRtCustNum() {
		return totalRtCustNum;
	}

	public void setTotalRtCustNum(Integer totalRtCustNum) {
		this.totalRtCustNum = totalRtCustNum;
	}

	public Integer getTotalAddRtCustNum() {
		return totalAddRtCustNum;
	}

	public void setTotalAddRtCustNum(Integer totalAddRtCustNum) {
		this.totalAddRtCustNum = totalAddRtCustNum;
	}

	public Integer getTotalNotConsCustNum() {
		return totalNotConsCustNum;
	}

	public void setTotalNotConsCustNum(Integer totalNotConsCustNum) {
		this.totalNotConsCustNum = totalNotConsCustNum;
	}

	public Integer getTotalAddNotConsCustNum() {
		return totalAddNotConsCustNum;
	}

	public void setTotalAddNotConsCustNum(Integer totalAddNotConsCustNum) {
		this.totalAddNotConsCustNum = totalAddNotConsCustNum;
	}

	public Integer getTotalUnAllocatedCustNum() {
		return totalUnAllocatedCustNum;
	}

	public void setTotalUnAllocatedCustNum(Integer totalUnAllocatedCustNum) {
		this.totalUnAllocatedCustNum = totalUnAllocatedCustNum;
	}

	public Integer getTotalAddUnAllocatedNum() {
		return totalAddUnAllocatedNum;
	}

	public void setTotalAddUnAllocatedNum(Integer totalAddUnAllocatedNum) {
		this.totalAddUnAllocatedNum = totalAddUnAllocatedNum;
	}

	public Integer getTotalAllocatedVirtualNum() {
		return totalAllocatedVirtualNum;
	}

	public void setTotalAllocatedVirtualNum(Integer totalAllocatedVirtualNum) {
		this.totalAllocatedVirtualNum = totalAllocatedVirtualNum;
	}

	public Integer getTotalAddAllocatedVirtualNum() {
		return totalAddAllocatedVirtualNum;
	}

	public void setTotalAddAllocatedVirtualNum(Integer totalAddAllocatedVirtualNum) {
		this.totalAddAllocatedVirtualNum = totalAddAllocatedVirtualNum;
	}

	
}
