package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class AppPv implements Serializable {

	private static final long serialVersionUID = 1L;

	private String consCustNo; // 投顾客户号

	private String consCustName; // 客户名称

	private String consCode; // 投顾号

	private String consName; // 投顾名称

	private String orgName; // 组织结构名称

	private String fundCode; // 基金代码

	private String fundName; // 基金名称

	private String fundType; // 基金类型

	private int con30; // 近30天

	private int con7; // 近7天

	private Double marketCap = 0d;// 市值

	private Double totalMat = 0d;// 总购买

	private String lastPvDt; // 最近查看日期

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getConsCustName() {
		return consCustName;
	}

	public void setConsCustName(String consCustName) {
		this.consCustName = consCustName;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getFundType() {
		return fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}

	public int getCon30() {
		return con30;
	}

	public void setCon30(int con30) {
		this.con30 = con30;
	}

	public int getCon7() {
		return con7;
	}

	public void setCon7(int con7) {
		this.con7 = con7;
	}

	public Double getMarketCap() {
		return marketCap;
	}

	public void setMarketCap(Double marketCap) {
		this.marketCap = marketCap;
	}

	public Double getTotalMat() {
		return totalMat;
	}

	public void setTotalMat(Double totalMat) {
		this.totalMat = totalMat;
	}

	public String getLastPvDt() {
		return lastPvDt;
	}

	public void setLastPvDt(String lastPvDt) {
		this.lastPvDt = lastPvDt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
