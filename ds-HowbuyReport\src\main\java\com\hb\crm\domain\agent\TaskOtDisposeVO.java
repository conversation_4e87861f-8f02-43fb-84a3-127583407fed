package com.hb.crm.domain.agent;

public class TaskOtDisposeVO {

	private int taskType;
	private String taskTypeName;
	private int unDispose;  // 待处理
	private int aDispose_Medium;  //再处理-非紧急
	private int aDispose_High;  //再处理-紧急
	private int disposed;  //已处理
	private int disposed_total;  //已处理
	
	private int myDistributed_unDispose;  // 我的任务 待处理
	private int myDistributed_aDispose_Medium;  //我的任务 再处理-非紧急
	private int myDistributed_aDispose_High;  //我的任务 再处理-紧急
	private int myDistributed_disposed;  //我的任务 已处理
	private int myDistributed_disposed_total;  //我的任务 已处理

	public TaskOtDisposeVO(){
		this.unDispose = 0;
		this.aDispose_Medium = 0;
		this.aDispose_High = 0;
		this.disposed = 0;
		this.disposed_total = 0;
		
		this.myDistributed_unDispose = 0;
		this.myDistributed_aDispose_Medium = 0;
		this.myDistributed_aDispose_High = 0;
		this.myDistributed_disposed = 0;
		this.myDistributed_disposed_total = 0;
	}

	public int getTaskType() {
		return taskType;
	}

	public void setTaskType(int taskType) {
		this.taskType = taskType;
	}

	public String getTaskTypeName() {
		return taskTypeName;
	}

	public void setTaskTypeName(String taskTypeName) {
		this.taskTypeName = taskTypeName;
	}

	public int getUnDispose() {
		return unDispose;
	}

	public void setUnDispose(int unDispose) {
		this.unDispose = unDispose;
	}

	public int getaDispose_Medium() {
		return aDispose_Medium;
	}

	public void setaDispose_Medium(int aDispose_Medium) {
		this.aDispose_Medium = aDispose_Medium;
	}

	public int getaDispose_High() {
		return aDispose_High;
	}

	public void setaDispose_High(int aDispose_High) {
		this.aDispose_High = aDispose_High;
	}

	public int getDisposed() {
		return disposed;
	}

	public void setDisposed(int disposed) {
		this.disposed = disposed;
	}

	public int getDisposed_total() {
		return disposed_total;
	}

	public void setDisposed_total(int disposed_total) {
		this.disposed_total = disposed_total;
	}

	public int getMyDistributed_unDispose() {
		return myDistributed_unDispose;
	}

	public void setMyDistributed_unDispose(int myDistributed_unDispose) {
		this.myDistributed_unDispose = myDistributed_unDispose;
	}

	public int getMyDistributed_aDispose_Medium() {
		return myDistributed_aDispose_Medium;
	}

	public void setMyDistributed_aDispose_Medium(int myDistributed_aDispose_Medium) {
		this.myDistributed_aDispose_Medium = myDistributed_aDispose_Medium;
	}

	public int getMyDistributed_aDispose_High() {
		return myDistributed_aDispose_High;
	}

	public void setMyDistributed_aDispose_High(int myDistributed_aDispose_High) {
		this.myDistributed_aDispose_High = myDistributed_aDispose_High;
	}

	public int getMyDistributed_disposed() {
		return myDistributed_disposed;
	}

	public void setMyDistributed_disposed(int myDistributed_disposed) {
		this.myDistributed_disposed = myDistributed_disposed;
	}

	public int getMyDistributed_disposed_total() {
		return myDistributed_disposed_total;
	}

	public void setMyDistributed_disposed_total(int myDistributed_disposed_total) {
		this.myDistributed_disposed_total = myDistributed_disposed_total;
	}	
	
}
