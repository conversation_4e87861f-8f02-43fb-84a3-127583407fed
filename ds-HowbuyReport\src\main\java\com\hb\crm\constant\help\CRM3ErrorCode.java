/**
 * 2010-09-17 chris.song	增加4422-网站客户已被关联
 * 2010-12-23 chris.song	增加4437-私募风险调查已做过
 * 2010-12-29 chris.song	增加4438-满意度调查已做过
 * 2011-03-07 chris.song	贵宾中心4439-4442
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR> crm2.0 errorcode
 */
public enum CRM3ErrorCode {

	/**
	 * 成功
	 */
	success("0000"),
	/**
	 * 记录不存在.
	 */
	RecodesNotExists("4410"),
	/**
	 * 參數錯誤.
	 */
	ParamError("4411"),
	/**
	 * 同样的操作记录已经存在.
	 */
	SameOpExists("4412"),
	/**
	 * 存在关联记录.
	 */
	ExistsLinkRecord("4413"),
	/**
	 * 不存在该日期的基金净值.
	 */
	NotExistTradeDtNav("4414"),
	/**
	 * 已建立关联
	 */
	RelatedCust("4415"),
	/**
	 * 未建立关联
	 */
	UnrelatedCust("4416"),
	/**
	 * 已存在主关联
	 */
	AlreadyBePrimary("4417"),

	/*
	 * 该客户已经是特殊客户
	 */

	SpecialCust("4418"),
	/*
	 * 该客户已经不是特殊客户
	 */

	SpecialCustCancel("4419"),
	
	/*
	 * 黑名单重复
	 */
	BlackListRepeat("4420"),
	/*
	 * 不是黑名单, 或已撤销
	 */
	BlackListNotExist("4421"),
	/**
	 * 网站客户已建立关联
	 */
	RelatedWebCust("4422"),
	/**
	 * 交易已做过审核
	 */
	TradeChecked("4423"),
	/**
	 * 交易必须审核通过
	 */
	TradeUncheked("4424"),
	/**
	 * 该理财中心已存在
	 */
	OutletExists("4425"),
	/**
	 * 该理财中心不存在
	 */
	OutletNotExists("4426"),
	/**
	 * 该理财中心状态不正常
	 */
	OutletAbnormal("4427"),
	/**
	 * 该理财中心负责人已存在
	 */
	OutletManagerExists("4428"),
	/**
	 * 该理财中心负责人不存在
	 */
	OutletManagerNotExists("4429"),
	/**
	 * 该理财中心负责人状态不正常
	 */
	OutletManagerAbnormal("4430"),
	/**
	 * 该投顾小组已存在
	 */
	ConsTeamExists("4431"),
	/**
	 * 该投顾小组不存在
	 */
	ConsTeamNotExists("4432"),
	/**
	 * 该投顾小组状态不正常
	 */
	ConsTeamAbnormal("4433"),
	/**
	 * 该投顾已存在
	 */
	ConsultantExists("4434"),
	/**
	 * 该投顾不存在
	 */
	ConsultantNotExists("4435"),
	/**
	 * 该投顾状态不正常
	 */
	ConsultantAbnormal("4436"),
	/**
	 * 私募风险调查已做过
	 */
	PriSurveySubmitted("4437"),
	/**
	 * 满意度调查已做过
	 */
	SatisfSurveySubmitted("4438"),
	/**
	 * 验证码不存在
	 */
	VerifyCodeNotExists("4439"),
	/**
	 * 验证码尚未生效（还没有发送）
	 */
	VerifyCodeInactive("4440"),
	/**
	 * 验证码已发送
	 */
	VerifyCodeSent("4441"),
	/**
	 * 验证码已被使用
	 */
	VerifyCodeUsed("4442");
	
	private String value;

	public String getValue() {
		return value;
	}

	CRM3ErrorCode(String value) {
		this.value = value;
	}
	public final static Map<CRM3ErrorCode, String> CRMErrorCodeEnumMAP;

	static {

		CRMErrorCodeEnumMAP = new EnumMap<CRM3ErrorCode, String>(
				CRM3ErrorCode.class);

		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.SameOpExists, "同样的操作记录已经存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.RecodesNotExists, "记录不存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.ParamError, "参数错误");
		CRMErrorCodeEnumMAP
				.put(CRM3ErrorCode.ExistsLinkRecord, "存在关联记录,操作不能完成");
		CRMErrorCodeEnumMAP
				.put(CRM3ErrorCode.NotExistTradeDtNav, "不存在该日期的基金净值");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.RelatedCust, "投顾客户与网站客户已建立关联");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.UnrelatedCust, "投顾客户与网站客户未建立关联");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.AlreadyBePrimary,
				"该投顾客户已存在主关联的网站客户");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.SpecialCustCancel, "该客户已经不是特殊客户");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.SpecialCust, "该客户已经是特殊客户");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.BlackListRepeat, "黑名单登记重复");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.BlackListNotExist, "黑名单不存在, 或已被解除");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.RelatedWebCust, "网站客户已被关联");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.TradeChecked, "交易已做过审核");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.TradeUncheked, "交易必须审核通过");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.OutletExists, "该理财中心已存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.OutletNotExists, "该理财中心不存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.OutletAbnormal, "该理财中心状态不正常");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.OutletManagerExists, "该理财中心负责人已存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.OutletManagerNotExists, "该理财中心负责人不存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.OutletManagerAbnormal, "该理财中心负责人不正常");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.ConsTeamExists, "该投顾小组已存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.ConsTeamNotExists, "该投顾小组不存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.ConsTeamAbnormal, "该投顾小组状态不正常");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.PriSurveySubmitted, "私募风险调查已做过");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.SatisfSurveySubmitted, "满意度调查已做过");
		
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.VerifyCodeNotExists, "验证码不存在");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.VerifyCodeInactive, "验证码尚未生效");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.VerifyCodeSent, "验证码已发送");
		CRMErrorCodeEnumMAP.put(CRM3ErrorCode.VerifyCodeUsed, "验证码已被使用");
	}
	/**
	 * 跟据value返回枚举对应的key
	 * 
	 * @param value
	 * @return NotifyErrorCode
	 */
	public static CRM3ErrorCode getEnumMAPKey(String value) {
		CRM3ErrorCode tmpKey = null;
		for (CRM3ErrorCode tmpEnum : CRM3ErrorCode.values()) {
			if (tmpEnum.value.equals(value)) {
				tmpKey = tmpEnum;
				break;
			}
		}
		return tmpKey;
	}
	/**
	 * 返回NotifyErrorCode对应的描述.
	 * 
	 * @param value
	 *            int.
	 * @return String
	 */
	public static String getEnumDesc(final String value) {
		return CRM3ErrorCode.CRMErrorCodeEnumMAP.get(CRM3ErrorCode
				.getEnumMAPKey(value));
	}
	public static String getEnumDesc(final CRM3ErrorCode value) {
		return CRM3ErrorCode.CRMErrorCodeEnumMAP.get(value);
	}
	public static void main(String[] args) {

	}
}
