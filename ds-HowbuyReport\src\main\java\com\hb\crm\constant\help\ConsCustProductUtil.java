/**
 * 2010-08-19 chris.song	initConsCustProduct()方法中长度不需要减1
 * 2011-03-07 chris.song	贵宾中心
 */
package com.hb.crm.constant.help;

import java.util.Date;
import java.util.Random;

import com.hb.crm.tools.StringUtil;


/**
 * <AUTHOR>
 * 
 */
public class ConsCustProductUtil {
	
	public static final int CONSCUSTPRODUCT_LEN = 16;//投顾客户产品长度
	public static final char PREFIX_CONSCUSTPROD = '0';//产品的前置字符串，不允许是空
	
	private ConsCustProductUtil() {
		
	}
	/**
	 * 得到产品的初始串，即无持有任何产品.
	 * @return
	 */
	public static String initConsCustProduct() {
		return StringUtil.fillZero(String.valueOf(PREFIX_CONSCUSTPROD), CONSCUSTPRODUCT_LEN);
	}
	/**
	 * 根据产品的位置得到对应的占位符.
	 * @param pos 位置
	 * @return String
	 */
	public static String getSqllikeProd(int pos) {
		StringBuilder sb = new StringBuilder("%1");
		for (int i=1; i<pos; i++) {
			sb.append("_");
		}
		return pos <=0 ? initConsCustProduct() : sb.toString();
	}
	
	public static String generateActiveCode(String custNo) {
		int random = new Random(new Date().getTime()).nextInt(9);
		char[] arr = new char[8];
		
		arr[0] = (char)((int)'A' + 10 - random);
		arr[1] = (char)((int)'A' + (Integer.valueOf(custNo.substring(3, 4)) + random) % 10);
		arr[2] = (char)((int)'A' + (Integer.valueOf(custNo.substring(4, 5)) - random + 10) % 10);
		arr[3] = (char)((int)'A' + (Integer.valueOf(custNo.substring(5, 6)) + random) % 10);
		arr[4] = (char)((int)'0' + (Integer.valueOf(custNo.substring(6, 7)) - random + 10) % 10);
		arr[5] = (char)((int)'0' + (Integer.valueOf(custNo.substring(7, 8)) + random) % 10);
		arr[6] = (char)((int)'0' + (Integer.valueOf(custNo.substring(8, 9)) - random + 10) % 10);
		arr[7] = (char)((int)'0' + (Integer.valueOf(custNo.substring(9, 10)) + random) % 10);
		
		return String.valueOf(arr).toLowerCase();
	}
	
	
	   public static void main(String[] args) {
	       System.out.println(generateActiveCode("1001524260"));
	   }
	
}

