package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类ZSEditDate.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class ZSEditDate implements Serializable {

	private static final long serialVersionUID = 1L;

	private String orgName; //部门名称

    private String consCode; //投顾code

	private String consName; //投顾名称

    private String sDate;//开始计算日期

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getsDate() {
        return sDate;
    }

    public void setsDate(String sDate) {
        this.sDate = sDate;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
