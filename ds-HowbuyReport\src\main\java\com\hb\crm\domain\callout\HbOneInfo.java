package com.hb.crm.domain.callout;

public class HbOneInfo {

	private String returnCode;
	private String description;
	private String hboneNo;
	private String userType;
	private String custName;
	private String username;
	private String idType;
	private String idNo;
	private String mobile;
	private String mobileVerifyStatus;
	private String existPassword;
	private String bindZcVip;
	
	public String getBindZcVip() {
		return bindZcVip;
	}
	public void setBindZcVip(String bindZcVip) {
		this.bindZcVip = bindZcVip;
	}
	public String getReturnCode() {
		return returnCode;
	}
	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getHboneNo() {
		return hboneNo;
	}
	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getIdType() {
		return idType;
	}
	public void setIdType(String idType) {
		this.idType = idType;
	}
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getMobileVerifyStatus() {
		return mobileVerifyStatus;
	}
	public void setMobileVerifyStatus(String mobileVerifyStatus) {
		this.mobileVerifyStatus = mobileVerifyStatus;
	}
	public String getExistPassword() {
		return existPassword;
	}
	public void setExistPassword(String existPassword) {
		this.existPassword = existPassword;
	}
	
}
