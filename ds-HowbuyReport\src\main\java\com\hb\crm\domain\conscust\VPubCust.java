package com.hb.crm.domain.conscust;
import java.util.Date;

/**
 * VVpubcustId generated by hbm2java
 */
public class VPubCust implements java.io.Serializable {

	private String custNo;
	private String fundTxAcctNo ; 
	private String custBankId ; 
	private String invstType;
	private String idType;
	private String idNo;
	private String custName;
	private String custStat;
	private String custAbbr;
	private String corpIdType;
	private String corpIdNo;
	private String corporation;
	private String postCode;
	private String addr;
	private String telNo;
	private String linkMan;
	private String linkIdType;
	private String linkIdNo;
	private String linkMethod;
	private String linkTel;
	private String custType;
	private String pagerNo;
	private String email;
	private String fax;
	private String faxFlag;
	private String homeTelNo;
	private String officeTelNo;
	private String mobile;
	private String eduLevel;
	private String vocation;
	private String incLevel;
	private String birthday;
	private String gender;
	private String fundManDlvyMode;
	private String agentDlvyMode;
	private String confDocNo;
	private String secuShAcctNo;
	private String secuSzAcctNo;
	private String minorFlag;
	private String minorId;
	private String corpCode;
	private String consCode;
	private String regDt;
	private String udDt;
	private String canDt;
	private Date stimestamp;
	private String custRiskLevel;
	private String provCode;
	private String cityCode;
	private String company;
	private String idValidityStart;
	private String idValidityEnd;
	private String idAlwaysValidFlag;
	private String idVrfyStat;
	private String custLoginPasswd;
	private String custTxPasswd;
	private String selfMsg;
	private String smsRemindOption;
	private String emailRemindOption;
	private String activeStat;
	private String nationality;
	private String agentDlvyType;
	private String fundManDlvyType;
	private String marriageStat;
	private String secuInvestExp;
	private String regAddr;
	private String regPostCode;
	private String riskSurveyDt;
	private String lastRiskLevelBySelf;
	private String corpIdAlwaysValidFlag;
	private String corpIdValidityEnd;
	private String regOutletCode;
	private String regTradeChan;
	private String activityno;
	private String partnerno;
	private String conscustno;
	private String isrelated;
	private String relatedt;
	private String conscode;

	private String partnername;
	/**
	 * 开户分销机构代码
	 */
	private String regDisCode;
	/**
	 * 交易申请金额
	 */
	private String ackAmt;
	
	/**
	 * 2014.5 添加是否绑定银行卡 0：未绑定 ，1： 已绑定
	 */
	private String bindCard;

	public String getBindCard() {
		return bindCard;
	}

	public void setBindCard(String bindCard) {
		this.bindCard = bindCard;
	}

	public String getCustNo() {
		return this.custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getInvstType() {
		return this.invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getIdType() {
		return this.idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}

	public String getIdNo() {
		return this.idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getCustName() {
		return this.custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getCustStat() {
		return this.custStat;
	}

	public void setCustStat(String custStat) {
		this.custStat = custStat;
	}

	public String getCustAbbr() {
		return this.custAbbr;
	}

	public void setCustAbbr(String custAbbr) {
		this.custAbbr = custAbbr;
	}

	public String getCorpIdType() {
		return this.corpIdType;
	}

	public void setCorpIdType(String corpIdType) {
		this.corpIdType = corpIdType;
	}

	public String getCorpIdNo() {
		return this.corpIdNo;
	}

	public void setCorpIdNo(String corpIdNo) {
		this.corpIdNo = corpIdNo;
	}

	public String getCorporation() {
		return this.corporation;
	}

	public void setCorporation(String corporation) {
		this.corporation = corporation;
	}

	public String getPostCode() {
		return this.postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getTelNo() {
		return this.telNo;
	}

	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}

	public String getLinkMan() {
		return this.linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}

	public String getLinkIdType() {
		return this.linkIdType;
	}

	public void setLinkIdType(String linkIdType) {
		this.linkIdType = linkIdType;
	}

	public String getLinkIdNo() {
		return this.linkIdNo;
	}

	public void setLinkIdNo(String linkIdNo) {
		this.linkIdNo = linkIdNo;
	}

	public String getLinkMethod() {
		return this.linkMethod;
	}

	public void setLinkMethod(String linkMethod) {
		this.linkMethod = linkMethod;
	}

	public String getLinkTel() {
		return this.linkTel;
	}

	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}

	public String getCustType() {
		return this.custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getPagerNo() {
		return this.pagerNo;
	}

	public void setPagerNo(String pagerNo) {
		this.pagerNo = pagerNo;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getFaxFlag() {
		return this.faxFlag;
	}

	public void setFaxFlag(String faxFlag) {
		this.faxFlag = faxFlag;
	}

	public String getHomeTelNo() {
		return this.homeTelNo;
	}

	public void setHomeTelNo(String homeTelNo) {
		this.homeTelNo = homeTelNo;
	}

	public String getOfficeTelNo() {
		return this.officeTelNo;
	}

	public void setOfficeTelNo(String officeTelNo) {
		this.officeTelNo = officeTelNo;
	}

	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEduLevel() {
		return this.eduLevel;
	}

	public void setEduLevel(String eduLevel) {
		this.eduLevel = eduLevel;
	}

	public String getVocation() {
		return this.vocation;
	}

	public void setVocation(String vocation) {
		this.vocation = vocation;
	}

	public String getIncLevel() {
		return this.incLevel;
	}

	public void setIncLevel(String incLevel) {
		this.incLevel = incLevel;
	}

	public String getBirthday() {
		return this.birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getFundManDlvyMode() {
		return this.fundManDlvyMode;
	}

	public void setFundManDlvyMode(String fundManDlvyMode) {
		this.fundManDlvyMode = fundManDlvyMode;
	}

	public String getAgentDlvyMode() {
		return this.agentDlvyMode;
	}

	public void setAgentDlvyMode(String agentDlvyMode) {
		this.agentDlvyMode = agentDlvyMode;
	}

	public String getConfDocNo() {
		return this.confDocNo;
	}

	public void setConfDocNo(String confDocNo) {
		this.confDocNo = confDocNo;
	}

	public String getSecuShAcctNo() {
		return this.secuShAcctNo;
	}

	public void setSecuShAcctNo(String secuShAcctNo) {
		this.secuShAcctNo = secuShAcctNo;
	}

	public String getSecuSzAcctNo() {
		return this.secuSzAcctNo;
	}

	public void setSecuSzAcctNo(String secuSzAcctNo) {
		this.secuSzAcctNo = secuSzAcctNo;
	}

	public String getMinorFlag() {
		return this.minorFlag;
	}

	public void setMinorFlag(String minorFlag) {
		this.minorFlag = minorFlag;
	}

	public String getMinorId() {
		return this.minorId;
	}

	public void setMinorId(String minorId) {
		this.minorId = minorId;
	}

	public String getCorpCode() {
		return this.corpCode;
	}

	public void setCorpCode(String corpCode) {
		this.corpCode = corpCode;
	}

	public String getConsCode() {
		return this.consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getRegDt() {
		return this.regDt;
	}

	public void setRegDt(String regDt) {
		this.regDt = regDt;
	}

	public String getUdDt() {
		return this.udDt;
	}

	public void setUdDt(String udDt) {
		this.udDt = udDt;
	}

	public String getCanDt() {
		return this.canDt;
	}

	public void setCanDt(String canDt) {
		this.canDt = canDt;
	}

	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}

	public String getCustRiskLevel() {
		return this.custRiskLevel;
	}

	public void setCustRiskLevel(String custRiskLevel) {
		this.custRiskLevel = custRiskLevel;
	}

	public String getProvCode() {
		return this.provCode;
	}

	public void setProvCode(String provCode) {
		this.provCode = provCode;
	}

	public String getCityCode() {
		return this.cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCompany() {
		return this.company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getIdValidityStart() {
		return this.idValidityStart;
	}

	public void setIdValidityStart(String idValidityStart) {
		this.idValidityStart = idValidityStart;
	}

	public String getIdValidityEnd() {
		return this.idValidityEnd;
	}

	public void setIdValidityEnd(String idValidityEnd) {
		this.idValidityEnd = idValidityEnd;
	}

	public String getIdAlwaysValidFlag() {
		return this.idAlwaysValidFlag;
	}

	public void setIdAlwaysValidFlag(String idAlwaysValidFlag) {
		this.idAlwaysValidFlag = idAlwaysValidFlag;
	}

	public String getIdVrfyStat() {
		return this.idVrfyStat;
	}

	public void setIdVrfyStat(String idVrfyStat) {
		this.idVrfyStat = idVrfyStat;
	}

	public String getCustLoginPasswd() {
		return this.custLoginPasswd;
	}

	public void setCustLoginPasswd(String custLoginPasswd) {
		this.custLoginPasswd = custLoginPasswd;
	}

	public String getCustTxPasswd() {
		return this.custTxPasswd;
	}

	public void setCustTxPasswd(String custTxPasswd) {
		this.custTxPasswd = custTxPasswd;
	}

	public String getSelfMsg() {
		return this.selfMsg;
	}

	public void setSelfMsg(String selfMsg) {
		this.selfMsg = selfMsg;
	}

	public String getSmsRemindOption() {
		return this.smsRemindOption;
	}

	public void setSmsRemindOption(String smsRemindOption) {
		this.smsRemindOption = smsRemindOption;
	}

	public String getEmailRemindOption() {
		return this.emailRemindOption;
	}

	public void setEmailRemindOption(String emailRemindOption) {
		this.emailRemindOption = emailRemindOption;
	}

	public String getActiveStat() {
		return this.activeStat;
	}

	public void setActiveStat(String activeStat) {
		this.activeStat = activeStat;
	}

	public String getNationality() {
		return this.nationality;
	}

	public void setNationality(String nationality) {
		this.nationality = nationality;
	}

	public String getAgentDlvyType() {
		return this.agentDlvyType;
	}

	public void setAgentDlvyType(String agentDlvyType) {
		this.agentDlvyType = agentDlvyType;
	}

	public String getFundManDlvyType() {
		return this.fundManDlvyType;
	}

	public void setFundManDlvyType(String fundManDlvyType) {
		this.fundManDlvyType = fundManDlvyType;
	}

	public String getMarriageStat() {
		return this.marriageStat;
	}

	public void setMarriageStat(String marriageStat) {
		this.marriageStat = marriageStat;
	}

	public String getSecuInvestExp() {
		return this.secuInvestExp;
	}

	public void setSecuInvestExp(String secuInvestExp) {
		this.secuInvestExp = secuInvestExp;
	}

	public String getRegAddr() {
		return this.regAddr;
	}

	public void setRegAddr(String regAddr) {
		this.regAddr = regAddr;
	}

	public String getRegPostCode() {
		return this.regPostCode;
	}

	public void setRegPostCode(String regPostCode) {
		this.regPostCode = regPostCode;
	}

	public String getRiskSurveyDt() {
		return this.riskSurveyDt;
	}

	public void setRiskSurveyDt(String riskSurveyDt) {
		this.riskSurveyDt = riskSurveyDt;
	}

	public String getLastRiskLevelBySelf() {
		return this.lastRiskLevelBySelf;
	}

	public void setLastRiskLevelBySelf(String lastRiskLevelBySelf) {
		this.lastRiskLevelBySelf = lastRiskLevelBySelf;
	}

	public String getCorpIdAlwaysValidFlag() {
		return this.corpIdAlwaysValidFlag;
	}

	public void setCorpIdAlwaysValidFlag(String corpIdAlwaysValidFlag) {
		this.corpIdAlwaysValidFlag = corpIdAlwaysValidFlag;
	}

	public String getCorpIdValidityEnd() {
		return this.corpIdValidityEnd;
	}

	public void setCorpIdValidityEnd(String corpIdValidityEnd) {
		this.corpIdValidityEnd = corpIdValidityEnd;
	}

	public String getRegOutletCode() {
		return this.regOutletCode;
	}

	public void setRegOutletCode(String regOutletCode) {
		this.regOutletCode = regOutletCode;
	}

	public String getRegTradeChan() {
		return this.regTradeChan;
	}

	public void setRegTradeChan(String regTradeChan) {
		this.regTradeChan = regTradeChan;
	}

	public String getActivityno() {
		return this.activityno;
	}

	public void setActivityno(String activityno) {
		this.activityno = activityno;
	}

	public String getPartnerno() {
		return this.partnerno;
	}

	public void setPartnerno(String partnerno) {
		this.partnerno = partnerno;
	}

	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getIsrelated() {
		return this.isrelated;
	}

	public void setIsrelated(String isrelated) {
		this.isrelated = isrelated;
	}

	public String getRelatedt() {
		return this.relatedt;
	}

	public void setRelatedt(String relatedt) {
		this.relatedt = relatedt;
	}

	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getPartnername() {
		return partnername;
	}

	public void setPartnername(String partnername) {
		this.partnername = partnername;
	}

	public VPubCust(String custNo, String invstType, String idType,
			String idNo, String custName, String custStat, String custAbbr,
			String corpIdType, String corpIdNo, String corporation,
			String postCode, String addr, String telNo, String linkMan,
			String linkIdType, String linkIdNo, String linkMethod,
			String linkTel, String custType, String pagerNo, String email,
			String fax, String faxFlag, String homeTelNo, String officeTelNo,
			String mobile, String eduLevel, String vocation, String incLevel,
			String birthday, String gender, String fundManDlvyMode,
			String agentDlvyMode, String confDocNo, String secuShAcctNo,
			String secuSzAcctNo, String minorFlag, String minorId,
			String corpCode, String consCode, String regDt, String udDt,
			String canDt, Date stimestamp, String custRiskLevel,
			String provCode, String cityCode, String company,
			String idValidityStart, String idValidityEnd,
			String idAlwaysValidFlag, String idVrfyStat,
			String custLoginPasswd, String custTxPasswd, String selfMsg,
			String smsRemindOption, String emailRemindOption,
			String activeStat, String nationality, String agentDlvyType,
			String fundManDlvyType, String marriageStat, String secuInvestExp,
			String regAddr, String regPostCode, String riskSurveyDt,
			String lastRiskLevelBySelf, String corpIdAlwaysValidFlag,
			String corpIdValidityEnd, String regOutletCode,
			String regTradeChan, String activityno, String partnerno,
			String conscustno, String isrelated, String relatedt,
			String partnername ,String conscode2,String regDisCode,String ackAmt) {
		super();
		this.custNo = custNo;
		this.invstType = invstType;
		this.idType = idType;
		this.idNo = idNo;
		this.custName = custName;
		this.custStat = custStat;
		this.custAbbr = custAbbr;
		this.corpIdType = corpIdType;
		this.corpIdNo = corpIdNo;
		this.corporation = corporation;
		this.postCode = postCode;
		this.addr = addr;
		this.telNo = telNo;
		this.linkMan = linkMan;
		this.linkIdType = linkIdType;
		this.linkIdNo = linkIdNo;
		this.linkMethod = linkMethod;
		this.linkTel = linkTel;
		this.custType = custType;
		this.pagerNo = pagerNo;
		this.email = email;
		this.fax = fax;
		this.faxFlag = faxFlag;
		this.homeTelNo = homeTelNo;
		this.officeTelNo = officeTelNo;
		this.mobile = mobile;
		this.eduLevel = eduLevel;
		this.vocation = vocation;
		this.incLevel = incLevel;
		this.birthday = birthday;
		this.gender = gender;
		this.fundManDlvyMode = fundManDlvyMode;
		this.agentDlvyMode = agentDlvyMode;
		this.confDocNo = confDocNo;
		this.secuShAcctNo = secuShAcctNo;
		this.secuSzAcctNo = secuSzAcctNo;
		this.minorFlag = minorFlag;
		this.minorId = minorId;
		this.corpCode = corpCode;
		this.consCode = consCode;
		this.regDt = regDt;
		this.udDt = udDt;
		this.canDt = canDt;
		this.stimestamp = stimestamp;
		this.custRiskLevel = custRiskLevel;
		this.provCode = provCode;
		this.cityCode = cityCode;
		this.company = company;
		this.idValidityStart = idValidityStart;
		this.idValidityEnd = idValidityEnd;
		this.idAlwaysValidFlag = idAlwaysValidFlag;
		this.idVrfyStat = idVrfyStat;
		this.custLoginPasswd = custLoginPasswd;
		this.custTxPasswd = custTxPasswd;
		this.selfMsg = selfMsg;
		this.smsRemindOption = smsRemindOption;
		this.emailRemindOption = emailRemindOption;
		this.activeStat = activeStat;
		this.nationality = nationality;
		this.agentDlvyType = agentDlvyType;
		this.fundManDlvyType = fundManDlvyType;
		this.marriageStat = marriageStat;
		this.secuInvestExp = secuInvestExp;
		this.regAddr = regAddr;
		this.regPostCode = regPostCode;
		this.riskSurveyDt = riskSurveyDt;
		this.lastRiskLevelBySelf = lastRiskLevelBySelf;
		this.corpIdAlwaysValidFlag = corpIdAlwaysValidFlag;
		this.corpIdValidityEnd = corpIdValidityEnd;
		this.regOutletCode = regOutletCode;
		this.regTradeChan = regTradeChan;
		this.activityno = activityno;
		this.partnerno = partnerno;
		this.conscustno = conscustno;
		this.isrelated = isrelated;
		this.relatedt = relatedt;
		this.conscode = conscode2;
		this.partnername=partnername;
		this.regDisCode = regDisCode;
		this.ackAmt = ackAmt;
	}

	public VPubCust() {
		super();
	}

	public String getFundTxAcctNo() {
		return fundTxAcctNo;
	}

	public void setFundTxAcctNo(String fundTxAcctNo) {
		this.fundTxAcctNo = fundTxAcctNo;
	}

	public String getCustBankId() {
		return custBankId;
	}

	public void setCustBankId(String custBankId) {
		this.custBankId = custBankId;
	}

	public String getRegDisCode() {
		return regDisCode;
	}

	public void setRegDisCode(String regDisCode) {
		this.regDisCode = regDisCode;
	}

	public String getAckAmt() {
		return ackAmt;
	}

	public void setAckAmt(String ackAmt) {
		this.ackAmt = ackAmt;
	}
	
}
