package com.hb.crm.domain.bookservicekind;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类CmBookservkind.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmBookservkind implements Serializable {

private static final long serialVersionUID = 1L;

	private Integer bookserviceid;
	
	private String servicetype;
	
	private String servicename;
	
	private String sendtype;
	
	private String percust;
	
	private String iswebview;
	
	private String sampleurl;
	
	private String iscimview;
	
	private String defaultsubscribe;
	
	private Integer showorder;
	
	private String mailType;
	
	private String recstat;
	
	public Integer getBookserviceid() {
		return this.bookserviceid;
	}

	public void setBookserviceid(Integer bookserviceid) {
		this.bookserviceid = bookserviceid;
	}
	
	public String getServicetype() {
		return this.servicetype;
	}

	public void setServicetype(String servicetype) {
		this.servicetype = servicetype;
	}
	
	public String getServicename() {
		return this.servicename;
	}

	public void setServicename(String servicename) {
		this.servicename = servicename;
	}
	
	public String getSendtype() {
		return this.sendtype;
	}

	public void setSendtype(String sendtype) {
		this.sendtype = sendtype;
	}
	
	public String getPercust() {
		return this.percust;
	}

	public void setPercust(String percust) {
		this.percust = percust;
	}
	
	public String getIswebview() {
		return this.iswebview;
	}

	public void setIswebview(String iswebview) {
		this.iswebview = iswebview;
	}
	
	public String getSampleurl() {
		return this.sampleurl;
	}

	public void setSampleurl(String sampleurl) {
		this.sampleurl = sampleurl;
	}
	
	public String getIscimview() {
		return this.iscimview;
	}

	public void setIscimview(String iscimview) {
		this.iscimview = iscimview;
	}
	
	public String getDefaultsubscribe() {
		return this.defaultsubscribe;
	}

	public void setDefaultsubscribe(String defaultsubscribe) {
		this.defaultsubscribe = defaultsubscribe;
	}
	
	public Integer getShoworder() {
		return this.showorder;
	}

	public void setShoworder(Integer showorder) {
		this.showorder = showorder;
	}
	
	public String getMailType() {
		return this.mailType;
	}

	public void setMailType(String mailType) {
		this.mailType = mailType;
	}
	
	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
