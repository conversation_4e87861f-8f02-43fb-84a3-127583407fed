package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类PotentialCust.java
 * @version 1.0
 */
public class CmPotentialCust implements Serializable {

	private static final long serialVersionUID = 1L;

	private String pcustid;

	private String pcustlevel;

	private Integer pgrade;

	private String pcuststatus;

	private String idtype;

	private String idno;

	private String custname;

	private String provcode;

	private String citycode;

	private String edulevel;

	private String vocation;

	private String inclevel;

	private String birthday;

	private String gender;

	private String married;

	private String pincome;

	private String fincome;

	private String decisionflag;

	private String interests;

	private String familycondition;

	private String contacttime;

	private String contactmethod;

	private String sendinfoflag;

	private String recvemailflag;

	private String recvtelflag;

	private String recvmsgflag;

	private String company;

	private String risklevel;

	private String selfrisklevel;

	private String addr;

	private String postcode;

	private String addr2;

	private String postcode2;

	private String addr3;

	private String postcode3;

	private String mobile;

	private String mobile2;

	private String telno;

	private String fax;

	private String pagerno;

	private String email;

	private String email2;

	private String hometelno;

	private String officetelno;

	private String actcode;

	private String brokercode;

	private String conscode;

	private String intrcustno;

	private String source;

	private String knowchan;

	private String otherchan;

	private String otherinvest;

	private String salon;

	private Integer winvestamt;

	private String wcustlevel;

	private String financememo;

	private String beforeinvest;

	private String im;

	private String regdt;

	private String uddt;

	private String memo;

	private String subsource;
	
	private String conscustno;
	
	private String batchid;
	
	private String newsourceno;
	
	private String newsourcename;

	public String getPcustid() {
		return this.pcustid;
	}

	public void setPcustid(String pcustid) {
		this.pcustid = pcustid;
	}

	public String getPcustlevel() {
		return this.pcustlevel;
	}

	public void setPcustlevel(String pcustlevel) {
		this.pcustlevel = pcustlevel;
	}

	public Integer getPgrade() {
		return this.pgrade;
	}

	public void setPgrade(Integer pgrade) {
		this.pgrade = pgrade;
	}

	public String getPcuststatus() {
		return this.pcuststatus;
	}

	public void setPcuststatus(String pcuststatus) {
		this.pcuststatus = pcuststatus;
	}

	public String getIdtype() {
		return this.idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}

	public String getIdno() {
		return this.idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getCustname() {
		return this.custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getProvcode() {
		return this.provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return this.citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getEdulevel() {
		return this.edulevel;
	}

	public void setEdulevel(String edulevel) {
		this.edulevel = edulevel;
	}

	public String getVocation() {
		return this.vocation;
	}

	public void setVocation(String vocation) {
		this.vocation = vocation;
	}

	public String getInclevel() {
		return this.inclevel;
	}

	public void setInclevel(String inclevel) {
		this.inclevel = inclevel;
	}

	public String getBirthday() {
		return this.birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getMarried() {
		return this.married;
	}

	public void setMarried(String married) {
		this.married = married;
	}

	public String getPincome() {
		return this.pincome;
	}

	public void setPincome(String pincome) {
		this.pincome = pincome;
	}

	public String getFincome() {
		return this.fincome;
	}

	public void setFincome(String fincome) {
		this.fincome = fincome;
	}

	public String getDecisionflag() {
		return this.decisionflag;
	}

	public void setDecisionflag(String decisionflag) {
		this.decisionflag = decisionflag;
	}

	public String getInterests() {
		return this.interests;
	}

	public void setInterests(String interests) {
		this.interests = interests;
	}

	public String getFamilycondition() {
		return this.familycondition;
	}

	public void setFamilycondition(String familycondition) {
		this.familycondition = familycondition;
	}

	public String getContacttime() {
		return this.contacttime;
	}

	public void setContacttime(String contacttime) {
		this.contacttime = contacttime;
	}

	public String getContactmethod() {
		return this.contactmethod;
	}

	public void setContactmethod(String contactmethod) {
		this.contactmethod = contactmethod;
	}

	public String getSendinfoflag() {
		return this.sendinfoflag;
	}

	public void setSendinfoflag(String sendinfoflag) {
		this.sendinfoflag = sendinfoflag;
	}

	public String getRecvemailflag() {
		return this.recvemailflag;
	}

	public void setRecvemailflag(String recvemailflag) {
		this.recvemailflag = recvemailflag;
	}

	public String getRecvtelflag() {
		return this.recvtelflag;
	}

	public void setRecvtelflag(String recvtelflag) {
		this.recvtelflag = recvtelflag;
	}

	public String getRecvmsgflag() {
		return this.recvmsgflag;
	}

	public void setRecvmsgflag(String recvmsgflag) {
		this.recvmsgflag = recvmsgflag;
	}

	public String getCompany() {
		return this.company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getRisklevel() {
		return this.risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}

	public String getSelfrisklevel() {
		return this.selfrisklevel;
	}

	public void setSelfrisklevel(String selfrisklevel) {
		this.selfrisklevel = selfrisklevel;
	}

	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getPostcode() {
		return this.postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}

	public String getAddr2() {
		return this.addr2;
	}

	public void setAddr2(String addr2) {
		this.addr2 = addr2;
	}

	public String getPostcode2() {
		return this.postcode2;
	}

	public void setPostcode2(String postcode2) {
		this.postcode2 = postcode2;
	}

	public String getAddr3() {
		return this.addr3;
	}

	public void setAddr3(String addr3) {
		this.addr3 = addr3;
	}

	public String getPostcode3() {
		return this.postcode3;
	}

	public void setPostcode3(String postcode3) {
		this.postcode3 = postcode3;
	}

	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMobile2() {
		return this.mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}

	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}

	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getPagerno() {
		return this.pagerno;
	}

	public void setPagerno(String pagerno) {
		this.pagerno = pagerno;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getEmail2() {
		return this.email2;
	}

	public void setEmail2(String email2) {
		this.email2 = email2;
	}

	public String getHometelno() {
		return this.hometelno;
	}

	public void setHometelno(String hometelno) {
		this.hometelno = hometelno;
	}

	public String getOfficetelno() {
		return this.officetelno;
	}

	public void setOfficetelno(String officetelno) {
		this.officetelno = officetelno;
	}

	public String getActcode() {
		return this.actcode;
	}

	public void setActcode(String actcode) {
		this.actcode = actcode;
	}

	public String getBrokercode() {
		return this.brokercode;
	}

	public void setBrokercode(String brokercode) {
		this.brokercode = brokercode;
	}

	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getIntrcustno() {
		return this.intrcustno;
	}

	public void setIntrcustno(String intrcustno) {
		this.intrcustno = intrcustno;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getKnowchan() {
		return this.knowchan;
	}

	public void setKnowchan(String knowchan) {
		this.knowchan = knowchan;
	}

	public String getOtherchan() {
		return this.otherchan;
	}

	public void setOtherchan(String otherchan) {
		this.otherchan = otherchan;
	}

	public String getOtherinvest() {
		return this.otherinvest;
	}

	public void setOtherinvest(String otherinvest) {
		this.otherinvest = otherinvest;
	}

	public String getSalon() {
		return this.salon;
	}

	public void setSalon(String salon) {
		this.salon = salon;
	}

	public Integer getWinvestamt() {
		return this.winvestamt;
	}

	public void setWinvestamt(Integer winvestamt) {
		this.winvestamt = winvestamt;
	}

	public String getWcustlevel() {
		return this.wcustlevel;
	}

	public void setWcustlevel(String wcustlevel) {
		this.wcustlevel = wcustlevel;
	}

	public String getFinancememo() {
		return this.financememo;
	}

	public void setFinancememo(String financememo) {
		this.financememo = financememo;
	}

	public String getBeforeinvest() {
		return this.beforeinvest;
	}

	public void setBeforeinvest(String beforeinvest) {
		this.beforeinvest = beforeinvest;
	}

	public String getIm() {
		return this.im;
	}

	public void setIm(String im) {
		this.im = im;
	}

	public String getRegdt() {
		return this.regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}

	public String getUddt() {
		return this.uddt;
	}

	public void setUddt(String uddt) {
		this.uddt = uddt;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getSubsource() {
		return subsource;
	}

	public void setSubsource(String subsource) {
		this.subsource = subsource;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getBatchid() {
		return batchid;
	}

	public void setBatchid(String batchid) {
		this.batchid = batchid;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getNewsourceno() {
		return newsourceno;
	}

	public void setNewsourceno(String newsourceno) {
		this.newsourceno = newsourceno;
	}

	public String getNewsourcename() {
		return newsourcename;
	}

	public void setNewsourcename(String newsourcename) {
		this.newsourcename = newsourcename;
	}
	
	
	
}
