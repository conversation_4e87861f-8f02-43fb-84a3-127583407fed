package com.hb.crm.domain.callout;

import java.io.Serializable;

/**
 * @Description: 制定客服任务来源
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CsTaskTargetSource implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer serialNo;

	private String conscode;

	private String firstSource;

	private String secondSource;

	private String thirdSource;

	private String finalSource;

	private String recStat;

	private String remark;

	public String getFinalSource() {
		return finalSource;
	}

	public void setFinalSource(String finalSource) {
		this.finalSource = finalSource;
	}

	public Integer getSerialNo() {
		return this.serialNo;
	}

	public void setSerialNo(Integer serialNo) {
		this.serialNo = serialNo;
	}

	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getFirstSource() {
		return this.firstSource;
	}

	public void setFirstSource(String firstSource) {
		this.firstSource = firstSource;
	}

	public String getSecondSource() {
		return this.secondSource;
	}

	public void setSecondSource(String secondSource) {
		this.secondSource = secondSource;
	}

	public String getThirdSource() {
		return this.thirdSource;
	}

	public void setThirdSource(String thirdSource) {
		this.thirdSource = thirdSource;
	}

	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
