package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * Version:3.5.9
 *
 * Created by jingya.xu on 2017/1/9.
 */
public class MergeConscust implements Serializable {

    private static final long serialVersionUID = 1L;
    //投顾客户号
    private String conscustno;

    //公募客户号
    private String  pubcustno;

    //投顾客户姓名
    private String custname;

    //身份证号
    private String idno;

    //手机号
    private String mobile;

    //手机号1
    private String mobile2;

    //手机号2
    private String linkmobile;

    //电话号码
    private String telno;

    //电话号码1
    private String linktel;

    //电话号码2
    private String officetelno;

    //email
    private String email;

    //email1
    private String email2;

    //email2
    private String linkmail;

    //一账通号
    private String hboneno;

    //是否甄财绑定
    private String iszcbind;

    //序号
    private int rownum;

    //网站客户号
    private String webCustno;

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getPubcustno() {
        return pubcustno;
    }

    public void setPubcustno(String pubcustno) {
        this.pubcustno = pubcustno;
    }

    public String getCustname() {
        return custname;
    }

    public void setCustname(String custname) {
        this.custname = custname;
    }

    public String getIdno() {
        return idno;
    }

    public void setIdno(String idno) {
        this.idno = idno;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelno() {
        return telno;
    }

    public void setTelno(String telno) {
        this.telno = telno;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile2() {
        return mobile2;
    }

    public void setMobile2(String mobile2) {
        this.mobile2 = mobile2;
    }

    public String getLinkmobile() {
        return linkmobile;
    }

    public void setLinkmobile(String linkmobile) {
        this.linkmobile = linkmobile;
    }

    public String getLinktel() {
        return linktel;
    }

    public void setLinktel(String linktel) {
        this.linktel = linktel;
    }

    public String getOfficetelno() {
        return officetelno;
    }

    public void setOfficetelno(String officetelno) {
        this.officetelno = officetelno;
    }

    public String getEmail2() {
        return email2;
    }

    public void setEmail2(String email2) {
        this.email2 = email2;
    }

    public String getLinkmail() {
        return linkmail;
    }

    public void setLinkmail(String linkmail) {
        this.linkmail = linkmail;
    }

    public String getHboneno() {
        return hboneno;
    }

    public void setHboneno(String hboneno) {
        this.hboneno = hboneno;
    }

    public String getIszcbind() {
        return iszcbind;
    }

    public void setIszcbind(String iszcbind) {
        this.iszcbind = iszcbind;
    }

    public String getWebCustno() {
        return webCustno;
    }

    public void setWebCustno(String webCustno) {
        this.webCustno = webCustno;
    }

    public int getRownum() {
        return rownum;
    }

    public void setRownum(int rownum) {
        this.rownum = rownum;
    }
}
