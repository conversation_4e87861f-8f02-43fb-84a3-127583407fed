package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustIndexFund implements Serializable {

	private static final long serialVersionUID = 1L;

	private String hbtypeName;

	private int custNum;

	private Double marketCap;

	public String getHbtypeName() {
		return hbtypeName;
	}

	public void setHbtypeName(String hbtypeName) {
		this.hbtypeName = hbtypeName;
	}

	public int getCustNum() {
		return custNum;
	}

	public void setCustNum(int custNum) {
		this.custNum = custNum;
	}

	public Double getMarketCap() {
		return marketCap;
	}

	public void setMarketCap(Double marketCap) {
		this.marketCap = marketCap;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
