package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 针对财务固收二级产品费用计算费率用
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class ConfSecondProductRate implements Serializable {


    private static final long serialVersionUID = 6328010121640381383L;
    /**
     * @Fields taskId : taskid
     */
    private String taskId;//主键

    private String fundCode;//基金code

    private String fundName;//基金名称

    private String company;//公司

    private BigDecimal subRate = new BigDecimal(0);//认购费率
    private BigDecimal manageRate = new BigDecimal(0);//管理费率

    private String manageFormula;//管理费公式

    private String manageRemark;//管理费备注

    private BigDecimal performanceRate = new BigDecimal(0);// 业绩报酬费率
    private BigDecimal performanceShareRate = new BigDecimal(0);// 业绩报酬分成费率

    private String redemRate;// 赎回费率 20210907 改成字符串类型


    private String performanceDate;//业绩报酬提取日

    private String redemDate;//赎回日 1-是2-否

    private String settleDate;//清算日 1-是2-否

    private String shareDate;//分红日 1-是2-否

    private String performanceFormula;//业绩报酬公式

    private String performanceRemark;//业绩报酬备注

    private String redemFormula;//赎回费公式

    private String redemRemark;//赎回费备注

    private String productManage;//产品经理
    private String signDate;//签约日期
    private String signSubject;//好买签约主体
    private String oppositeContact;//对方联系人

    private String reviewState;//审核状态

    private Date reviewtime;//审核时间

    private String reviewer;//审核人

    private String creator;//创建者

    private Date createtime;//创建时间

    private String updater;//更新者

    private Date updattime;//更新时间

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public BigDecimal getSubRate() {
        return subRate;
    }

    public void setSubRate(BigDecimal subRate) {
        this.subRate = subRate;
    }

    public BigDecimal getManageRate() {
        return manageRate;
    }

    public void setManageRate(BigDecimal manageRate) {
        this.manageRate = manageRate;
    }

    public String getManageFormula() {
        return manageFormula;
    }

    public void setManageFormula(String manageFormula) {
        this.manageFormula = manageFormula;
    }

    public String getManageRemark() {
        return manageRemark;
    }

    public void setManageRemark(String manageRemark) {
        this.manageRemark = manageRemark;
    }

    public BigDecimal getPerformanceRate() {
        return performanceRate;
    }

    public void setPerformanceRate(BigDecimal performanceRate) {
        this.performanceRate = performanceRate;
    }

    public String getRedemRate() {
        return redemRate;
    }

    public void setRedemRate(String redemRate) {
        this.redemRate = redemRate;
    }

    public String getPerformanceDate() {
        return performanceDate;
    }

    public void setPerformanceDate(String performanceDate) {
        this.performanceDate = performanceDate;
    }

    public String getRedemDate() {
        return redemDate;
    }

    public void setRedemDate(String redemDate) {
        this.redemDate = redemDate;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getShareDate() {
        return shareDate;
    }

    public void setShareDate(String shareDate) {
        this.shareDate = shareDate;
    }

    public String getPerformanceFormula() {
        return performanceFormula;
    }

    public void setPerformanceFormula(String performanceFormula) {
        this.performanceFormula = performanceFormula;
    }

    public String getPerformanceRemark() {
        return performanceRemark;
    }

    public void setPerformanceRemark(String performanceRemark) {
        this.performanceRemark = performanceRemark;
    }

    public String getRedemFormula() {
        return redemFormula;
    }

    public void setRedemFormula(String redemFormula) {
        this.redemFormula = redemFormula;
    }

    public String getRedemRemark() {
        return redemRemark;
    }

    public void setRedemRemark(String redemRemark) {
        this.redemRemark = redemRemark;
    }

    public String getProductManage() {
        return productManage;
    }

    public void setProductManage(String productManage) {
        this.productManage = productManage;
    }

    public String getSignDate() {
        return signDate;
    }

    public void setSignDate(String signDate) {
        this.signDate = signDate;
    }

    public String getSignSubject() {
        return signSubject;
    }

    public void setSignSubject(String signSubject) {
        this.signSubject = signSubject;
    }

    public String getOppositeContact() {
        return oppositeContact;
    }

    public void setOppositeContact(String oppositeContact) {
        this.oppositeContact = oppositeContact;
    }

    public String getReviewState() {
        return reviewState;
    }

    public void setReviewState(String reviewState) {
        this.reviewState = reviewState;
    }

    public Date getReviewtime() {
        return reviewtime;
    }

    public void setReviewtime(Date reviewtime) {
        this.reviewtime = reviewtime;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getUpdattime() {
        return updattime;
    }

    public void setUpdattime(Date updattime) {
        this.updattime = updattime;
    }

    public BigDecimal getPerformanceShareRate() {
        return performanceShareRate;
    }

    public void setPerformanceShareRate(BigDecimal performanceShareRate) {
        this.performanceShareRate = performanceShareRate;
    }
}
