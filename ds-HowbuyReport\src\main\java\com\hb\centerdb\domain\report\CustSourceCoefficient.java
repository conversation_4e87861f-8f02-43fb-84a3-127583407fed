package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;


public class CustSourceCoefficient implements Serializable{


    private static final long serialVersionUID = 7173117528725385045L;

    private String u1name;//一级部门

    private String u2name;//二级部门

    private String u3name;//三级部门

    private String consname;//投顾

    private String conscustno;//投顾客户号

    private String custName;//客户姓名

    private String source;//来源类型

    private String startPoint;//来源系数起点(0.2、1)

    private BigDecimal zbCofficient=new BigDecimal(0);//折标系数

    private BigDecimal manageCoeff=new BigDecimal(0);//管理系数

    private String cxa;//存续A 可选项有：公司资源、投顾资源

    private String cxb;//存续B 可选项有：公司资源、投顾资源

    private String isCalc;//是否公募计提

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public BigDecimal getZbCofficient() {
        return zbCofficient;
    }

    public void setZbCofficient(BigDecimal zbCofficient) {
        this.zbCofficient = zbCofficient;
    }

    public BigDecimal getManageCoeff() {
        return manageCoeff;
    }

    public void setManageCoeff(BigDecimal manageCoeff) {
        this.manageCoeff = manageCoeff;
    }

    public String getCxa() {
        return cxa;
    }

    public void setCxa(String cxa) {
        this.cxa = cxa;
    }

    public String getCxb() {
        return cxb;
    }

    public void setCxb(String cxb) {
        this.cxb = cxb;
    }

    public String getIsCalc() {
        return isCalc;
    }

    public void setIsCalc(String isCalc) {
        this.isCalc = isCalc;
    }
}
