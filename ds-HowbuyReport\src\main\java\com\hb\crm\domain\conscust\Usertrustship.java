package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Usertrustship.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Usertrustship implements Serializable {

private static final long serialVersionUID = 1L;

	private Long id;
	
	private String username;
	
	private String trustshipusername;
	
	private String trustshipdate;
	
	private String trustshipstatus;
	
	private Integer authorityid;
	
	private String trustshiptype;
	
	private String trustshipchannel;
	
	private String trustshipremark;
	
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public String getUsername() {
		return this.username;
	}

	public void setUsername(String username) {
		this.username = username;
	}
	
	public String getTrustshipusername() {
		return this.trustshipusername;
	}

	public void setTrustshipusername(String trustshipusername) {
		this.trustshipusername = trustshipusername;
	}
	
	public String getTrustshipdate() {
		return this.trustshipdate;
	}

	public void setTrustshipdate(String trustshipdate) {
		this.trustshipdate = trustshipdate;
	}
	
	public String getTrustshipstatus() {
		return this.trustshipstatus;
	}

	public void setTrustshipstatus(String trustshipstatus) {
		this.trustshipstatus = trustshipstatus;
	}
	
	public Integer getAuthorityid() {
		return this.authorityid;
	}

	public void setAuthorityid(Integer authorityid) {
		this.authorityid = authorityid;
	}
	
	public String getTrustshiptype() {
		return this.trustshiptype;
	}

	public void setTrustshiptype(String trustshiptype) {
		this.trustshiptype = trustshiptype;
	}
	
	public String getTrustshipchannel() {
		return this.trustshipchannel;
	}

	public void setTrustshipchannel(String trustshipchannel) {
		this.trustshipchannel = trustshipchannel;
	}
	
	public String getTrustshipremark() {
		return this.trustshipremark;
	}

	public void setTrustshipremark(String trustshipremark) {
		this.trustshipremark = trustshipremark;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
