package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class GdKhAsset implements Serializable {

    private static final long serialVersionUID = 2903226452511320440L;

    private String hbonNo; // 一账通号

	private String consCustNo; // 投顾客户号

	private String consCustName; // 客户姓名

	private String consCode; // 投顾账号

	private String consName; // 投顾姓名

    private String heCurMarketAmt; //高端当前总存量

    private String heCurFundCnt; //当前持有产品数量

    private String heLgsAmtCur; //类固收产品当前存量

    private String heGpdcAmtCur; //二级产品当前存量

    private String heHedgeAmtCur; //对冲产品当前存量
    private String heShareAmtCur; //股票产品当前存量

    private String hePevcAmtCur; //股权产品当前存量

    private String heHwAmtCur; //海外产品当前存量

    private String fofAmtCur; //FOF产品当前存量

    private String heLgsRatio;  //类固收产品当前存量占比
    private String heGpdcRatio;  //二级产品当前存量占比

    private String heHedgeRatio;  //对冲产品当前存量占比
    private String heShareRatio;  //股票产品当前存量占比

    private String hePevcRatio;  //股权产品当前存量占比
    private String heHwRatio;  //海外产品当前存量占比

    private String heMaxMarketCapHis; //高端历史最大存量

    private String accuGpdcBalInc; //二级产品当前持仓收益

    private String accuHedgeBalInc; //对冲产品当前持仓收益
    private String accuShareBalInc; //股票产品当前持仓收益

    private String accuGpdcInc; //二级产品累计收益

    private String accuInc; //高端累计收益(不含股权)

    private String accuHeBalInc; //高端当前收益(不含股权)

    private String lsFlag; //是否零售客户

    private String hbLsCurAmt; //好买渠道零售当前总存量

    private String totalLsBalInc; //零售当前持仓收益
    private String totalLsAccumIncome;// 零售累计收益

    private String hbLsCurQyAmt; //权益类单基金当前存量

    private String hbLsCurHmclAmt; //组合产品当前存量

    private String hbLsCxgMarketAmt; //储蓄罐当前存量

    private String hbLsQyRatio ;//权益类单基金当前存量占比
    private String hbLsHmclRatio;// 组合产品当前存量占比
    private String hbLsCxgRatio;// 储蓄罐当前存量占比
    private String hbLsElseRatio;// 零售其他类型产品占比

    private String maxZjLogTime; //半年内最近一次登录掌基时间
    private String maxBuyLsTime; //近购买公募产品时间（非储蓄罐）

    private int zeroStockNum; //零存量客户
    private int noZeroStockNum; //有存量客户
    private double zeroStockRatio; //零存量客户占比

    private String finshYearSale;// 年度销量
    private String finshLastYearSale;// 去年年度销量
    private String finshQuaterSale;// 季度销量
    private String finshMonthSale;// 季度月销量
    private String quterMonth;// 季度月

    private String quterMonthSale1;// 季度第一月销量
    private String quterMonthSale2;// 季度第二月销量
    private String quterMonthSale3;// 季度第三月销量


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getHbonNo() {
        return hbonNo;
    }

    public void setHbonNo(String hbonNo) {
        this.hbonNo = hbonNo;
    }

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getConsCustName() {
        return consCustName;
    }

    public void setConsCustName(String consCustName) {
        this.consCustName = consCustName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getHeCurMarketAmt() {
        return heCurMarketAmt;
    }

    public void setHeCurMarketAmt(String heCurMarketAmt) {
        this.heCurMarketAmt = heCurMarketAmt;
    }

    public String getHeCurFundCnt() {
        return heCurFundCnt;
    }

    public void setHeCurFundCnt(String heCurFundCnt) {
        this.heCurFundCnt = heCurFundCnt;
    }

    public String getHeLgsAmtCur() {
        return heLgsAmtCur;
    }

    public void setHeLgsAmtCur(String heLgsAmtCur) {
        this.heLgsAmtCur = heLgsAmtCur;
    }

    public String getHeGpdcAmtCur() {
        return heGpdcAmtCur;
    }

    public void setHeGpdcAmtCur(String heGpdcAmtCur) {
        this.heGpdcAmtCur = heGpdcAmtCur;
    }

    public String getHePevcAmtCur() {
        return hePevcAmtCur;
    }

    public void setHePevcAmtCur(String hePevcAmtCur) {
        this.hePevcAmtCur = hePevcAmtCur;
    }

    public String getHeHwAmtCur() {
        return heHwAmtCur;
    }

    public void setHeHwAmtCur(String heHwAmtCur) {
        this.heHwAmtCur = heHwAmtCur;
    }

    public String getHeMaxMarketCapHis() {
        return heMaxMarketCapHis;
    }

    public void setHeMaxMarketCapHis(String heMaxMarketCapHis) {
        this.heMaxMarketCapHis = heMaxMarketCapHis;
    }

    public String getAccuGpdcBalInc() {
        return accuGpdcBalInc;
    }

    public void setAccuGpdcBalInc(String accuGpdcBalInc) {
        this.accuGpdcBalInc = accuGpdcBalInc;
    }

    public String getAccuGpdcInc() {
        return accuGpdcInc;
    }

    public void setAccuGpdcInc(String accuGpdcInc) {
        this.accuGpdcInc = accuGpdcInc;
    }

    public String getAccuInc() {
        return accuInc;
    }

    public void setAccuInc(String accuInc) {
        this.accuInc = accuInc;
    }

    public String getLsFlag() {
        return lsFlag;
    }

    public void setLsFlag(String lsFlag) {
        this.lsFlag = lsFlag;
    }

    public String getHbLsCurAmt() {
        return hbLsCurAmt;
    }

    public void setHbLsCurAmt(String hbLsCurAmt) {
        this.hbLsCurAmt = hbLsCurAmt;
    }

    public String getHbLsCurQyAmt() {
        return hbLsCurQyAmt;
    }

    public void setHbLsCurQyAmt(String hbLsCurQyAmt) {
        this.hbLsCurQyAmt = hbLsCurQyAmt;
    }

    public String getHbLsCurHmclAmt() {
        return hbLsCurHmclAmt;
    }

    public void setHbLsCurHmclAmt(String hbLsCurHmclAmt) {
        this.hbLsCurHmclAmt = hbLsCurHmclAmt;
    }

    public String getHbLsCxgMarketAmt() {
        return hbLsCxgMarketAmt;
    }

    public void setHbLsCxgMarketAmt(String hbLsCxgMarketAmt) {
        this.hbLsCxgMarketAmt = hbLsCxgMarketAmt;
    }

    public String getMaxZjLogTime() {
        return maxZjLogTime;
    }

    public void setMaxZjLogTime(String maxZjLogTime) {
        this.maxZjLogTime = maxZjLogTime;
    }

    public String getHeLgsRatio() {
        return heLgsRatio;
    }

    public void setHeLgsRatio(String heLgsRatio) {
        this.heLgsRatio = heLgsRatio;
    }

    public String getHeGpdcRatio() {
        return heGpdcRatio;
    }

    public void setHeGpdcRatio(String heGpdcRatio) {
        this.heGpdcRatio = heGpdcRatio;
    }

    public String getHePevcRatio() {
        return hePevcRatio;
    }

    public void setHePevcRatio(String hePevcRatio) {
        this.hePevcRatio = hePevcRatio;
    }

    public String getHeHwRatio() {
        return heHwRatio;
    }

    public void setHeHwRatio(String heHwRatio) {
        this.heHwRatio = heHwRatio;
    }

    public String getTotalLsBalInc() {
        return totalLsBalInc;
    }

    public void setTotalLsBalInc(String totalLsBalInc) {
        this.totalLsBalInc = totalLsBalInc;
    }

    public String getTotalLsAccumIncome() {
        return totalLsAccumIncome;
    }

    public void setTotalLsAccumIncome(String totalLsAccumIncome) {
        this.totalLsAccumIncome = totalLsAccumIncome;
    }

    public String getHbLsQyRatio() {
        return hbLsQyRatio;
    }

    public void setHbLsQyRatio(String hbLsQyRatio) {
        this.hbLsQyRatio = hbLsQyRatio;
    }

    public String getHbLsHmclRatio() {
        return hbLsHmclRatio;
    }

    public void setHbLsHmclRatio(String hbLsHmclRatio) {
        this.hbLsHmclRatio = hbLsHmclRatio;
    }

    public String getHbLsCxgRatio() {
        return hbLsCxgRatio;
    }

    public void setHbLsCxgRatio(String hbLsCxgRatio) {
        this.hbLsCxgRatio = hbLsCxgRatio;
    }

    public String getHbLsElseRatio() {
        return hbLsElseRatio;
    }

    public void setHbLsElseRatio(String hbLsElseRatio) {
        this.hbLsElseRatio = hbLsElseRatio;
    }

    public String getAccuHeBalInc() {
        return accuHeBalInc;
    }

    public void setAccuHeBalInc(String accuHeBalInc) {
        this.accuHeBalInc = accuHeBalInc;
    }

    public String getHeHedgeAmtCur() {
        return heHedgeAmtCur;
    }

    public void setHeHedgeAmtCur(String heHedgeAmtCur) {
        this.heHedgeAmtCur = heHedgeAmtCur;
    }

    public String getHeShareAmtCur() {
        return heShareAmtCur;
    }

    public void setHeShareAmtCur(String heShareAmtCur) {
        this.heShareAmtCur = heShareAmtCur;
    }

    public String getHeHedgeRatio() {
        return heHedgeRatio;
    }

    public void setHeHedgeRatio(String heHedgeRatio) {
        this.heHedgeRatio = heHedgeRatio;
    }

    public String getHeShareRatio() {
        return heShareRatio;
    }

    public void setHeShareRatio(String heShareRatio) {
        this.heShareRatio = heShareRatio;
    }

    public String getAccuHedgeBalInc() {
        return accuHedgeBalInc;
    }

    public void setAccuHedgeBalInc(String accuHedgeBalInc) {
        this.accuHedgeBalInc = accuHedgeBalInc;
    }

    public String getAccuShareBalInc() {
        return accuShareBalInc;
    }

    public void setAccuShareBalInc(String accuShareBalInc) {
        this.accuShareBalInc = accuShareBalInc;
    }

    public String getMaxBuyLsTime() {
        return maxBuyLsTime;
    }

    public void setMaxBuyLsTime(String maxBuyLsTime) {
        this.maxBuyLsTime = maxBuyLsTime;
    }

    public int getZeroStockNum() {
        return zeroStockNum;
    }

    public void setZeroStockNum(int zeroStockNum) {
        this.zeroStockNum = zeroStockNum;
    }

    public int getNoZeroStockNum() {
        return noZeroStockNum;
    }

    public void setNoZeroStockNum(int noZeroStockNum) {
        this.noZeroStockNum = noZeroStockNum;
    }

    public double getZeroStockRatio() {
        return zeroStockRatio;
    }

    public void setZeroStockRatio(double zeroStockRatio) {
        this.zeroStockRatio = zeroStockRatio;
    }

    public String getFinshYearSale() {
        return finshYearSale;
    }

    public void setFinshYearSale(String finshYearSale) {
        this.finshYearSale = finshYearSale;
    }

    public String getFinshLastYearSale() {
        return finshLastYearSale;
    }

    public void setFinshLastYearSale(String finshLastYearSale) {
        this.finshLastYearSale = finshLastYearSale;
    }

    public String getFinshQuaterSale() {
        return finshQuaterSale;
    }

    public void setFinshQuaterSale(String finshQuaterSale) {
        this.finshQuaterSale = finshQuaterSale;
    }

    public String getFinshMonthSale() {
        return finshMonthSale;
    }

    public void setFinshMonthSale(String finshMonthSale) {
        this.finshMonthSale = finshMonthSale;
    }

    public String getQuterMonth() {
        return quterMonth;
    }

    public void setQuterMonth(String quterMonth) {
        this.quterMonth = quterMonth;
    }

    public String getFofAmtCur() {
        return fofAmtCur;
    }

    public void setFofAmtCur(String fofAmtCur) {
        this.fofAmtCur = fofAmtCur;
    }

    public String getQuterMonthSale1() {
        return quterMonthSale1;
    }

    public void setQuterMonthSale1(String quterMonthSale1) {
        this.quterMonthSale1 = quterMonthSale1;
    }

    public String getQuterMonthSale2() {
        return quterMonthSale2;
    }

    public void setQuterMonthSale2(String quterMonthSale2) {
        this.quterMonthSale2 = quterMonthSale2;
    }

    public String getQuterMonthSale3() {
        return quterMonthSale3;
    }

    public void setQuterMonthSale3(String quterMonthSale3) {
        this.quterMonthSale3 = quterMonthSale3;
    }
}
