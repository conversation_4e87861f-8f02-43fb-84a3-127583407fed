package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Vwebcust.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Vwebcust implements Serializable {

private static final long serialVersionUID = 1L;

	private String custno;
	
	private String custusername;
	
	private String custstatus;
	
	private String regdt;
	
	private String ipaddr;
	
	private String ipcity;
	
	private String knowchan;
	
	private String intrcustno;
	
	private String provcode;
	
	private String citycode;
	
	private String custname;
	
	private String email;
	
	private Integer conscustgrade;
	
	private String conscustlvl;
	
	private String mobile;
	
	private String email2;
	
	private String mobile2;
	
	private String addr;
	
	private String postcode;
	
	private String addr2;
	
	private String postcode2;
	
	private String addr3;
	
	private String postcode3;
	
	private String selfdefflag;
	
	private String buyingfreeprod;
	
	private String gender;
	
	private String risklevel;
	
	private String recvemailflag;
	
	private String recvmsgflag;
	
	private String im;
	
	private String msn;
	
	private String conscustno;
	
	private String conscode;
	
	private Integer totalmarketvalue;
	
	private String surveyflag;
	
	private String surveydt;
	
	private String myfundflag;
	
	private String emailvrfy;
	
	private String mobilevrfy;
	
	private String isrelated;
	
	private String isrweb;
	
	private String activedt;
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getCustusername() {
		return this.custusername;
	}

	public void setCustusername(String custusername) {
		this.custusername = custusername;
	}
	
	public String getCuststatus() {
		return this.custstatus;
	}

	public void setCuststatus(String custstatus) {
		this.custstatus = custstatus;
	}
	
	public String getRegdt() {
		return this.regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}
	
	public String getIpaddr() {
		return this.ipaddr;
	}

	public void setIpaddr(String ipaddr) {
		this.ipaddr = ipaddr;
	}
	
	public String getIpcity() {
		return this.ipcity;
	}

	public void setIpcity(String ipcity) {
		this.ipcity = ipcity;
	}
	
	public String getKnowchan() {
		return this.knowchan;
	}

	public void setKnowchan(String knowchan) {
		this.knowchan = knowchan;
	}
	
	public String getIntrcustno() {
		return this.intrcustno;
	}

	public void setIntrcustno(String intrcustno) {
		this.intrcustno = intrcustno;
	}
	
	public String getProvcode() {
		return this.provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}
	
	public String getCitycode() {
		return this.citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}
	
	public String getCustname() {
		return this.custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}
	
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public Integer getConscustgrade() {
		return this.conscustgrade;
	}

	public void setConscustgrade(Integer conscustgrade) {
		this.conscustgrade = conscustgrade;
	}
	
	public String getConscustlvl() {
		return this.conscustlvl;
	}

	public void setConscustlvl(String conscustlvl) {
		this.conscustlvl = conscustlvl;
	}
	
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	public String getEmail2() {
		return this.email2;
	}

	public void setEmail2(String email2) {
		this.email2 = email2;
	}
	
	public String getMobile2() {
		return this.mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}
	
	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}
	
	public String getPostcode() {
		return this.postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}
	
	public String getAddr2() {
		return this.addr2;
	}

	public void setAddr2(String addr2) {
		this.addr2 = addr2;
	}
	
	public String getPostcode2() {
		return this.postcode2;
	}

	public void setPostcode2(String postcode2) {
		this.postcode2 = postcode2;
	}
	
	public String getAddr3() {
		return this.addr3;
	}

	public void setAddr3(String addr3) {
		this.addr3 = addr3;
	}
	
	public String getPostcode3() {
		return this.postcode3;
	}

	public void setPostcode3(String postcode3) {
		this.postcode3 = postcode3;
	}
	
	public String getSelfdefflag() {
		return this.selfdefflag;
	}

	public void setSelfdefflag(String selfdefflag) {
		this.selfdefflag = selfdefflag;
	}
	
	public String getBuyingfreeprod() {
		return this.buyingfreeprod;
	}

	public void setBuyingfreeprod(String buyingfreeprod) {
		this.buyingfreeprod = buyingfreeprod;
	}
	
	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}
	
	public String getRisklevel() {
		return this.risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}
	
	public String getRecvemailflag() {
		return this.recvemailflag;
	}

	public void setRecvemailflag(String recvemailflag) {
		this.recvemailflag = recvemailflag;
	}
	
	public String getRecvmsgflag() {
		return this.recvmsgflag;
	}

	public void setRecvmsgflag(String recvmsgflag) {
		this.recvmsgflag = recvmsgflag;
	}
	
	public String getIm() {
		return this.im;
	}

	public void setIm(String im) {
		this.im = im;
	}
	
	public String getMsn() {
		return this.msn;
	}

	public void setMsn(String msn) {
		this.msn = msn;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	
	public Integer getTotalmarketvalue() {
		return this.totalmarketvalue;
	}

	public void setTotalmarketvalue(Integer totalmarketvalue) {
		this.totalmarketvalue = totalmarketvalue;
	}
	
	public String getSurveyflag() {
		return this.surveyflag;
	}

	public void setSurveyflag(String surveyflag) {
		this.surveyflag = surveyflag;
	}
	
	public String getSurveydt() {
		return this.surveydt;
	}

	public void setSurveydt(String surveydt) {
		this.surveydt = surveydt;
	}
	
	public String getMyfundflag() {
		return this.myfundflag;
	}

	public void setMyfundflag(String myfundflag) {
		this.myfundflag = myfundflag;
	}
	
	public String getEmailvrfy() {
		return this.emailvrfy;
	}

	public void setEmailvrfy(String emailvrfy) {
		this.emailvrfy = emailvrfy;
	}
	
	public String getMobilevrfy() {
		return this.mobilevrfy;
	}

	public void setMobilevrfy(String mobilevrfy) {
		this.mobilevrfy = mobilevrfy;
	}
	
	public String getIsrelated() {
		return this.isrelated;
	}

	public void setIsrelated(String isrelated) {
		this.isrelated = isrelated;
	}
	
	public String getIsrweb() {
		return this.isrweb;
	}

	public void setIsrweb(String isrweb) {
		this.isrweb = isrweb;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getActivedt() {
	return activedt;
}

public void setActivedt(String activedt) {
	this.activedt = activedt;
}
}
