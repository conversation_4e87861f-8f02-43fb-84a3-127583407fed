package com.hb.centerdb.enums;

/**
 * <AUTHOR>
 * @description: 考核结果枚举
 * @date 2024/2/22 10:54
 * @since JDK 1.8
 */
public enum ExaimneResultEnum {

    JIN_SHENG("5", "晋升"),
    WEI_CHI("1", "维持"),
    JIANG_JI("2", "降级"),
    JIN_GUAN_CHA("3", "进观察期"),
    TAO_TAI("4", "降职或淘汰"),
    CHU_GUAN_CHA("6", "出观察期"),;
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    ExaimneResultEnum(String code, String description){
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
