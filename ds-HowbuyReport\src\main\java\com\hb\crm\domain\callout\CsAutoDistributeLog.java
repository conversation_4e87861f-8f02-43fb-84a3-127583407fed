package com.hb.crm.domain.callout;

import java.io.Serializable;

public class CsAutoDistributeLog implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private long id;    //序号ID
	private String advisorId;  //投顾编号
	private String advisorName;  //投顾名称
	private String custNo;  //客户编号
	private String custName;   //客户名称
	private String orgCode;  //所属部门编号
	private int custType;   //"0" 表示非howbuy，“1”表示howbuy
	private int pubCustFlag;  //	“0” 表示非公募，“1”表示公募
	private String procId;   //客户所在省份
	private int autoType;   //“1”表示非公募投顾分配 “2”表示公募客户投顾分配
	private int statisticType;   //'1' 表示呼出，'2'表示呼入
	private String waitId;   // 待分配id
	private String distributeDate;  //分配时间
	private String teamCode;  //团队编号
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getAdvisorId() {
		return advisorId;
	}
	public void setAdvisorId(String advisorId) {
		this.advisorId = advisorId;
	}
	public String getAdvisorName() {
		return advisorName;
	}
	public void setAdvisorName(String advisorName) {
		this.advisorName = advisorName;
	}
	public String getCustNo() {
		return custNo;
	}
	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public int getCustType() {
		return custType;
	}
	public void setCustType(int custType) {
		this.custType = custType;
	}
	public int getPubCustFlag() {
		return pubCustFlag;
	}
	public void setPubCustFlag(int pubCustFlag) {
		this.pubCustFlag = pubCustFlag;
	}
	public String getProcId() {
		return procId;
	}
	public void setProcId(String procId) {
		this.procId = procId;
	}
	public int getAutoType() {
		return autoType;
	}
	public void setAutoType(int autoType) {
		this.autoType = autoType;
	}	
	public int getStatisticType() {
		return statisticType;
	}
	public void setStatisticType(int statisticType) {
		this.statisticType = statisticType;
	}	
	public String getWaitId() {
		return waitId;
	}
	public void setWaitId(String waitId) {
		this.waitId = waitId;
	}
	public String getDistributeDate() {
		return distributeDate;
	}
	public void setDistributeDate(String distributeDate) {
		this.distributeDate = distributeDate;
	}
	public String getTeamCode() {
		return teamCode;
	}
	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}
	
	

}
