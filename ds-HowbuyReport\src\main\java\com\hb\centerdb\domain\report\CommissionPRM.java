package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类Commission.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CommissionPRM implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String id;

	private String fundManCode;//基金公司代码
	
	private String fundManName;//基金公司名称
	
	private String isBackSpot;//是否垫付后端申购费 0:是 1:否
	
	private String commissionCycle ;//手续费结算周期
	
	private String salesFeeCycle;//销售服务费结算周期
	
	private String tailFeeCycle;//尾随佣金结算周期

	private String status; //记录状态 0:正常 1:删除

	private String creator; //创建人

	private String modifier; //修改人

	private String credt; //创建时间

	private String moddt; //修改时间
	

	public String getId() {
		return id;
	}


	public void setId(String id) {
		this.id = id;
	}


	public String getFundManCode() {
		return fundManCode;
	}


	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}


	public String getFundManName() {
		return fundManName;
	}


	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}


	public String getIsBackSpot() {
		return isBackSpot;
	}


	public void setIsBackSpot(String isBackSpot) {
		this.isBackSpot = isBackSpot;
	}


	public String getCommissionCycle() {
		return commissionCycle;
	}


	public void setCommissionCycle(String commissionCycle) {
		this.commissionCycle = commissionCycle;
	}


	public String getSalesFeeCycle() {
		return salesFeeCycle;
	}


	public void setSalesFeeCycle(String salesFeeCycle) {
		this.salesFeeCycle = salesFeeCycle;
	}


	public String getTailFeeCycle() {
		return tailFeeCycle;
	}


	public void setTailFeeCycle(String tailFeeCycle) {
		this.tailFeeCycle = tailFeeCycle;
	}


	public String getStatus() {
		return status;
	}


	public void setStatus(String status) {
		this.status = status;
	}


	public String getCreator() {
		return creator;
	}


	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return modifier;
	}


	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getCredt() {
		return credt;
	}


	public void setCredt(String credt) {
		this.credt = credt;
	}


	public String getModdt() {
		return moddt;
	}


	public void setModdt(String moddt) {
		this.moddt = moddt;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
