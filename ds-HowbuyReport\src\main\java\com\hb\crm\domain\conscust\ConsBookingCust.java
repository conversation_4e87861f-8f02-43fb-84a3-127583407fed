package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import java.sql.Timestamp;

/** <AUTHOR> CodeGenerator */
public class ConsBookingCust implements Serializable {

    /** identifier field */
    private String consBookingID;

    /** nullable persistent field */
    private String consCustNo;

    /** persistent field */
    private String bookingStatus;

    /** persistent field */
    private String bookingCons;

    /** nullable persistent field */
    private String content;

    /** persistent field */
    private String bookingDT;

    /** nullable persistent field */
    private String creator;

    /** nullable persistent field */
    private String creDt;

    /** nullable persistent field */
    private String modDt;

   

    /** default constructor */
    public ConsBookingCust() {
    }

    

  

    public String getConsBookingID() {
		return consBookingID;
	}





	public void setConsBookingID(String consBookingID) {
		this.consBookingID = consBookingID;
	}





	public String getConsCustNo() {
        return this.consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getBookingStatus() {
        return this.bookingStatus;
    }

    public void setBookingStatus(String bookingStatus) {
        this.bookingStatus = bookingStatus;
    }

    public String getBookingCons() {
        return this.bookingCons;
    }

    public void setBookingCons(String bookingCons) {
        this.bookingCons = bookingCons;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBookingDT() {
        return this.bookingDT;
    }

    public void setBookingDT(String bookingDT) {
        this.bookingDT = bookingDT;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreDt() {
        return this.creDt;
    }

    public void setCreDt(String creDt) {
        this.creDt = creDt;
    }

    public String getModDt() {
        return this.modDt;
    }

    public void setModDt(String modDt) {
        this.modDt = modDt;
    }


    public String toString() {
        return new ToStringBuilder(this)
            .append("consBookingID", getConsBookingID())
            .toString();
    }

    public boolean equals(Object other) {
        if ( !(other instanceof ConsBookingCust) ) return false;
        ConsBookingCust castOther = (ConsBookingCust) other;
        return new EqualsBuilder()
            .append(this.getConsBookingID(), castOther.getConsBookingID())
            .isEquals();
    }

    public int hashCode() {
        return new HashCodeBuilder()
            .append(getConsBookingID())
            .toHashCode();
    }

}
