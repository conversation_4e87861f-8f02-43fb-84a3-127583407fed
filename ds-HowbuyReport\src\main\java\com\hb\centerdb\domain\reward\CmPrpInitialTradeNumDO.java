package com.hb.centerdb.domain.reward;


import com.alibaba.excel.annotation.ExcelProperty;
import com.hb.crm.util.validation.AddGroup;
import com.hb.crm.util.validation.UpdateGroup;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2020/9/15 11:10
 * @Description 配置初始交易次数实体类
 * @Version 1.0
 */


/**
    * 配置初始交易次数
    */
@Data
public class CmPrpInitialTradeNumDO implements Serializable {
    /**
    * 主键id
    */
    @NotNull(message = "主键id不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private Long id;

    /**
    * 客户号
    */
    @NotBlank(message ="客户号不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String conscustno;

    /**
    * 投顾号
    */
    @NotBlank(message ="投顾号不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String conscode;

    /**
    * 初始交易次数
    */
    @ExcelProperty(value = "初始交易次数",index =5)
    @NotBlank(message = "初始交易次数不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String initialNum;

    /**
    * 生效状态(1有效，0无效)
    */
    @NotBlank(message ="生效状态不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String isValid;
    /**
    * 备注
    */
    private String remark;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private String createTime;

    /**
    * 修改人
    */
    private String modor;
    /**
     * 修改人姓名
     */
    private String modorname;

    /**
    * 修改时间
    */
    private String updateTime;

    /**
     * 所属部门
     */
    //@TableField(exist = false)
    private String orgname;

    /**
     * 客户姓名
     */
    private String conscustname;

    /**
     * 投顾姓名
     */
    private String consname;

    /**
     * 部门id
     */
    private String orgcode;


    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getInitialNum() {
        return initialNum;
    }

    public void setInitialNum(String initialNum) {
        this.initialNum = initialNum;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public String getModorname() {
        return modorname;
    }

    public void setModorname(String modorname) {
        this.modorname = modorname;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getConscustname() {
        return conscustname;
    }

    public void setConscustname(String conscustname) {
        this.conscustname = conscustname;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(String orgcode) {
        this.orgcode = orgcode;
    }
}