package com.hb.crm.domain.conscust;

// Generated 2012-6-12 10:46:45 by Hibernate Tools 3.2.0.b9

import java.util.Date;

/**
 * CmConscustrpubcusthis generated by hbm2java
 */
public class Conscustrpubcusthis implements java.io.Serializable {

	private String appserialno;
	private String conscustrpubcustid;
	private String conscustno;
	private String pubcustno;
	private String operateType;
	private String creator;
	private String modifier;
	private String credt;
	private String moddt;
	private Date stimestamp;

	public Conscustrpubcusthis() {
	}

	public Conscustrpubcusthis(String appserialno, String conscustrpubcustid,
			String conscustno, String pubcustno, Date stimestamp) {
		this.appserialno = appserialno;
		this.conscustrpubcustid = conscustrpubcustid;
		this.conscustno = conscustno;
		this.pubcustno = pubcustno;
		this.stimestamp = stimestamp;
	}

	public Conscustrpubcusthis(String appserialno, String conscustrpubcustid,
			String conscustno, String pubcustno, String operateType,
			String creator, String modifier, String credt, String moddt,
			Date stimestamp) {
		this.appserialno = appserialno;
		this.conscustrpubcustid = conscustrpubcustid;
		this.conscustno = conscustno;
		this.pubcustno = pubcustno;
		this.operateType = operateType;
		this.creator = creator;
		this.modifier = modifier;
		this.credt = credt;
		this.moddt = moddt;
		this.stimestamp = stimestamp;
	}

	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}

	public String getConscustrpubcustid() {
		return this.conscustrpubcustid;
	}

	public void setConscustrpubcustid(String conscustrpubcustid) {
		this.conscustrpubcustid = conscustrpubcustid;
	}

	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getPubcustno() {
		return this.pubcustno;
	}

	public void setPubcustno(String pubcustno) {
		this.pubcustno = pubcustno;
	}

	public String getOperateType() {
		return this.operateType;
	}

	public void setOperateType(String operateType) {
		this.operateType = operateType;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}

}
