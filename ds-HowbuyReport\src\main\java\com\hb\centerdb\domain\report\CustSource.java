package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类ZSReport.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustSource implements Serializable {

	private static final long serialVersionUID = 1L;
    private String outletcode; // 部门code
	private String outletname; // 部门
    private String u2name; // 区域
    private String teamCode; // 团队code
    private String team; // 团队
	private String consname; // 投顾姓名
    private String conscode; // 投顾代码
    private String conscustNo; // 投顾客户号
    private String conscustName; // 客户姓名
	private Integer custAll=0;// 总客户数
	private Integer custTrade=0;// 总成交客户数
	private Integer clf0=0;// 存量
	private Integer cl0=0;// 0存量
	private Integer custqzAll=0;// 总潜在客户数
	private Integer cj1s=0;// 一手成交
	private Integer qz2cj2s=0;// 二手潜在客户划转成交
	private Integer cj2cj2s=0;// 二手成交客户划转成交
	private Integer cj2nocj2s=0;// 二手成交客户未转化
	private Integer qz1s=0;// 一手潜在
	private Integer qz2s=0;// 二手潜在
	private String custSource;// 客户来源
    private String month;// 月份

    private BigDecimal mgmNum = new BigDecimal(0); // mgm录入客户数
    private BigDecimal mgmNumCj = new BigDecimal(0);//mgm成交客户数
    private BigDecimal mgmNumCjRate = new BigDecimal(0);//mgm成交率

    private BigDecimal custDevelopNum = new BigDecimal(0); // 投顾开发录入客户数
    private BigDecimal custDevelopNumCj = new BigDecimal(0);// 投顾开发成交客户数
    private BigDecimal custDevelopNumCjRate = new BigDecimal(0);//投顾开发成交率


    private BigDecimal market0In3NewNum = new BigDecimal(0); // 0市值(3个月内)新增客户数
    private BigDecimal market0In3ActiveNum = new BigDecimal(0);//0市值(3个月内)激活客户数
    private BigDecimal market0In3ActiveRate = new BigDecimal(0);//0市值(3个月内)激活率

    private BigDecimal market0Out3NewNum = new BigDecimal(0); // 0存量(超过3个月)新增客户数
    private BigDecimal market0Out3ActiveNum = new BigDecimal(0);//0存量(超过3个月)激活客户数
    private BigDecimal market0Out3ActiveRate = new BigDecimal(0);//0存量(超过3个月)激活率

    private String regDt; //进入系统时间
    private String isCj; //是否成交
    private String firstTradeDt; //首次成交时间
    private String firstTradeConscode; //首次成交投顾code
    private String firstTradeConsName; //首次成交投顾
    private String firstTradeOutletname; //首次成交部门
    private String firstTradeOutletcode; //首次成交部门code
    private String fundType; //成交产品类型
    private String fundName; //成交产品名称
    private String ackAmtRmb; //成交金额

    private String redemDt; //赎回日期
    private String custState; //客户状态
    private String lastFundType; //最后持有产品类型
    private String lastFundName; //最后持有产品类型
    private String lastFundCode; //最后持有产品类型

    private String firstConscode; //后续首次成交投顾
    private String firstConsname; //后续首次成交投顾
    private String firstFundType; //后续首成次交产品类型
    private String firstFundCode; //后续首次成交产品code
    private String firstFundName; //后续首次成交产品名称
    private String firstAckAmtRmb; //后续首次成交金额
    private String currentConscode; //当前投顾code
    private String currentConsName; //当前投顾

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public String getOutletname() {
		return outletname;
	}

	public void setOutletname(String outletname) {
		this.outletname = outletname;
	}

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public Integer getCustAll() {
		return custAll;
	}

	public void setCustAll(Integer custAll) {
		this.custAll = custAll;
	}

	public Integer getCustTrade() {
		return custTrade;
	}

	public void setCustTrade(Integer custTrade) {
		this.custTrade = custTrade;
	}

	public Integer getClf0() {
		return clf0;
	}

	public void setClf0(Integer clf0) {
		this.clf0 = clf0;
	}

	public Integer getCl0() {
		return cl0;
	}

	public void setCl0(Integer cl0) {
		this.cl0 = cl0;
	}

	public Integer getCustqzAll() {
		return custqzAll;
	}

	public void setCustqzAll(Integer custqzAll) {
		this.custqzAll = custqzAll;
	}

	public Integer getCj1s() {
		return cj1s;
	}

	public void setCj1s(Integer cj1s) {
		this.cj1s = cj1s;
	}

	public Integer getQz2cj2s() {
		return qz2cj2s;
	}

	public void setQz2cj2s(Integer qz2cj2s) {
		this.qz2cj2s = qz2cj2s;
	}

	public Integer getCj2cj2s() {
		return cj2cj2s;
	}

	public void setCj2cj2s(Integer cj2cj2s) {
		this.cj2cj2s = cj2cj2s;
	}

	public Integer getCj2nocj2s() {
		return cj2nocj2s;
	}

	public void setCj2nocj2s(Integer cj2nocj2s) {
		this.cj2nocj2s = cj2nocj2s;
	}

	public Integer getQz1s() {
		return qz1s;
	}

	public void setQz1s(Integer qz1s) {
		this.qz1s = qz1s;
	}

	public Integer getQz2s() {
		return qz2s;
	}

	public void setQz2s(Integer qz2s) {
		this.qz2s = qz2s;
	}

	public String getCustSource() {
		return custSource;
	}

	public void setCustSource(String custSource) {
		this.custSource = custSource;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

    public BigDecimal getMgmNum() {
        return mgmNum;
    }

    public void setMgmNum(BigDecimal mgmNum) {
        this.mgmNum = mgmNum;
    }

    public BigDecimal getMgmNumCj() {
        return mgmNumCj;
    }

    public void setMgmNumCj(BigDecimal mgmNumCj) {
        this.mgmNumCj = mgmNumCj;
    }

    public BigDecimal getMgmNumCjRate() {
        return mgmNumCjRate;
    }

    public void setMgmNumCjRate(BigDecimal mgmNumCjRate) {
        this.mgmNumCjRate = mgmNumCjRate;
    }

    public BigDecimal getCustDevelopNum() {
        return custDevelopNum;
    }

    public void setCustDevelopNum(BigDecimal custDevelopNum) {
        this.custDevelopNum = custDevelopNum;
    }

    public BigDecimal getCustDevelopNumCj() {
        return custDevelopNumCj;
    }

    public void setCustDevelopNumCj(BigDecimal custDevelopNumCj) {
        this.custDevelopNumCj = custDevelopNumCj;
    }

    public BigDecimal getCustDevelopNumCjRate() {
        return custDevelopNumCjRate;
    }

    public void setCustDevelopNumCjRate(BigDecimal custDevelopNumCjRate) {
        this.custDevelopNumCjRate = custDevelopNumCjRate;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getConscustName() {
        return conscustName;
    }

    public void setConscustName(String conscustName) {
        this.conscustName = conscustName;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }

    public String getIsCj() {
        return isCj;
    }

    public void setIsCj(String isCj) {
        this.isCj = isCj;
    }

    public String getFirstTradeDt() {
        return firstTradeDt;
    }

    public void setFirstTradeDt(String firstTradeDt) {
        this.firstTradeDt = firstTradeDt;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getAckAmtRmb() {
        return ackAmtRmb;
    }

    public void setAckAmtRmb(String ackAmtRmb) {
        this.ackAmtRmb = ackAmtRmb;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public BigDecimal getMarket0In3NewNum() {
        return market0In3NewNum;
    }

    public void setMarket0In3NewNum(BigDecimal market0In3NewNum) {
        this.market0In3NewNum = market0In3NewNum;
    }

    public BigDecimal getMarket0In3ActiveNum() {
        return market0In3ActiveNum;
    }

    public void setMarket0In3ActiveNum(BigDecimal market0In3ActiveNum) {
        this.market0In3ActiveNum = market0In3ActiveNum;
    }

    public BigDecimal getMarket0In3ActiveRate() {
        return market0In3ActiveRate;
    }

    public void setMarket0In3ActiveRate(BigDecimal market0In3ActiveRate) {
        this.market0In3ActiveRate = market0In3ActiveRate;
    }

    public BigDecimal getMarket0Out3NewNum() {
        return market0Out3NewNum;
    }

    public void setMarket0Out3NewNum(BigDecimal market0Out3NewNum) {
        this.market0Out3NewNum = market0Out3NewNum;
    }

    public BigDecimal getMarket0Out3ActiveNum() {
        return market0Out3ActiveNum;
    }

    public void setMarket0Out3ActiveNum(BigDecimal market0Out3ActiveNum) {
        this.market0Out3ActiveNum = market0Out3ActiveNum;
    }

    public BigDecimal getMarket0Out3ActiveRate() {
        return market0Out3ActiveRate;
    }

    public void setMarket0Out3ActiveRate(BigDecimal market0Out3ActiveRate) {
        this.market0Out3ActiveRate = market0Out3ActiveRate;
    }

    public String getRedemDt() {
        return redemDt;
    }

    public void setRedemDt(String redemDt) {
        this.redemDt = redemDt;
    }

    public String getCustState() {
        return custState;
    }

    public void setCustState(String custState) {
        this.custState = custState;
    }

    public String getLastFundType() {
        return lastFundType;
    }

    public void setLastFundType(String lastFundType) {
        this.lastFundType = lastFundType;
    }

    public String getLastFundCode() {
        return lastFundCode;
    }

    public void setLastFundCode(String lastFundCode) {
        this.lastFundCode = lastFundCode;
    }

    public String getLastFundName() {
        return lastFundName;
    }

    public void setLastFundName(String lastFundName) {
        this.lastFundName = lastFundName;
    }

    public String getFirstConscode() {
        return firstConscode;
    }

    public void setFirstConscode(String firstConscode) {
        this.firstConscode = firstConscode;
    }

    public String getFirstConsname() {
        return firstConsname;
    }

    public void setFirstConsname(String firstConsname) {
        this.firstConsname = firstConsname;
    }

    public String getFirstFundType() {
        return firstFundType;
    }

    public void setFirstFundType(String firstFundType) {
        this.firstFundType = firstFundType;
    }

    public String getFirstFundCode() {
        return firstFundCode;
    }

    public void setFirstFundCode(String firstFundCode) {
        this.firstFundCode = firstFundCode;
    }

    public String getFirstFundName() {
        return firstFundName;
    }

    public void setFirstFundName(String firstFundName) {
        this.firstFundName = firstFundName;
    }

    public String getFirstAckAmtRmb() {
        return firstAckAmtRmb;
    }

    public void setFirstAckAmtRmb(String firstAckAmtRmb) {
        this.firstAckAmtRmb = firstAckAmtRmb;
    }

    public String getCurrentConscode() {
        return currentConscode;
    }

    public void setCurrentConscode(String currentConscode) {
        this.currentConscode = currentConscode;
    }

    public String getCurrentConsName() {
        return currentConsName;
    }

    public void setCurrentConsName(String currentConsName) {
        this.currentConsName = currentConsName;
    }

    public String getFirstTradeConscode() {
        return firstTradeConscode;
    }

    public void setFirstTradeConscode(String firstTradeConscode) {
        this.firstTradeConscode = firstTradeConscode;
    }

    public String getFirstTradeConsName() {
        return firstTradeConsName;
    }

    public void setFirstTradeConsName(String firstTradeConsName) {
        this.firstTradeConsName = firstTradeConsName;
    }

    public String getFirstTradeOutletname() {
        return firstTradeOutletname;
    }

    public void setFirstTradeOutletname(String firstTradeOutletname) {
        this.firstTradeOutletname = firstTradeOutletname;
    }

    public String getFirstTradeOutletcode() {
        return firstTradeOutletcode;
    }

    public void setFirstTradeOutletcode(String firstTradeOutletcode) {
        this.firstTradeOutletcode = firstTradeOutletcode;
    }
}
