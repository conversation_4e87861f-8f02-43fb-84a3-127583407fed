package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustIndexCust implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custIndexBalType;

	private Double custMarket;

	private Double custPriMarket;

	public String getCustIndexBalType() {
		return custIndexBalType;
	}

	public void setCustIndexBalType(String custIndexBalType) {
		this.custIndexBalType = custIndexBalType;
	}

	public Double getCustMarket() {
		return custMarket;
	}

	public void setCustMarket(Double custMarket) {
		this.custMarket = custMarket;
	}

	public Double getCustPriMarket() {
		return custPriMarket;
	}

	public void setCustPriMarket(Double custPriMarket) {
		this.custPriMarket = custPriMarket;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
