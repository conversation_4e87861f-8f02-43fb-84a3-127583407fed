package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Conscustcontractinfo.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Conscustcontractinfo implements Serializable {

private static final long serialVersionUID = 1L;

	private String id;
	
	private String conscustno;
	
	private String columntype;
	
	private String columnvalue;
	
	private String delfflag;
	
	private String recstat;
	
	private String creator;
	
	private String modifier;
	
	private String credt;
	
	private String moddt;
	
	private String provcode;
	
	private String citycode;
	
	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getColumntype() {
		return this.columntype;
	}

	public void setColumntype(String columntype) {
		this.columntype = columntype;
	}
	
	public String getColumnvalue() {
		return this.columnvalue;
	}

	public void setColumnvalue(String columnvalue) {
		this.columnvalue = columnvalue;
	}
	
	public String getDelfflag() {
		return this.delfflag;
	}

	public void setDelfflag(String delfflag) {
		this.delfflag = delfflag;
	}
	
	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getProvcode() {
	return provcode;
}

public void setProvcode(String provcode) {
	this.provcode = provcode;
}

public String getCitycode() {
	return citycode;
}

public void setCitycode(String citycode) {
	this.citycode = citycode;
}
}
