package com.hb.crm.domain.conscust;

import java.io.Serializable;

/**
 * 
 * 群继电子成单OGG同步表
 *
 * 修改历史:											 
 * 修改日期    		                  修改人员   	    版本	 	修改内容 
 * -------------------------------------------------  
 * 2016年6月1日 上午9:58:37   yu.zhang     1.0    	初始化创建 
 *
 * <AUTHOR> 
 *
 */
public class ICmCustCustinfo implements Serializable {

	/** 
	 * long ICmCustCustinfo.java serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	private String distributorcode;
	
	private String certificatetype;
	
	private String certificateno;
	
	private String certificateno15;
	
	private String investorname;
	
	private String individualorinstitution;
	
	private String custid;
	
	private String custstatus;
	
	private String isqualitycust;
	
	private String assetcertifyflag;
	
	private String picid;
	
	private String verifystatus;
	
	private String verifystatusvalue;
	
	private String returncode;
	
	private String returnmessage;
	
	private String assetcertifystatus;
	
	private String assetcertifymaturity;
	
	private String assetcertifystatusvalue;
	
	public String getVerifystatusvalue() {
		return verifystatusvalue;
	}

	public void setVerifystatusvalue(String verifystatusvalue) {
		this.verifystatusvalue = verifystatusvalue;
	}

	public String getDistributorcode() {
		return distributorcode;
	}
	
	public void setDistributorcode(String distributorcode) {
		this.distributorcode = distributorcode;
	}
	
	public String getCertificatetype() {
		return certificatetype;
	}
	
	public void setCertificatetype(String certificatetype) {
		this.certificatetype = certificatetype;
	}
	
	public String getCertificateno() {
		return certificateno;
	}
	
	public void setCertificateno(String certificateno) {
		this.certificateno = certificateno;
	}
	
	public String getCertificateno15() {
		return certificateno15;
	}
	
	public void setCertificateno15(String certificateno15) {
		this.certificateno15 = certificateno15;
	}
	
	public String getInvestorname() {
		return investorname;
	}
	
	public void setInvestorname(String investorname) {
		this.investorname = investorname;
	}
	
	public String getIndividualorinstitution() {
		return individualorinstitution;
	}
	
	public void setIndividualorinstitution(String individualorinstitution) {
		this.individualorinstitution = individualorinstitution;
	}
	
	public String getCustid() {
		return custid;
	}
	
	public void setCustid(String custid) {
		this.custid = custid;
	}
	
	public String getCuststatus() {
		return custstatus;
	}
	
	public void setCuststatus(String custstatus) {
		this.custstatus = custstatus;
	}
	
	public String getIsqualitycust() {
		return isqualitycust;
	}
	
	public void setIsqualitycust(String isqualitycust) {
		this.isqualitycust = isqualitycust;
	}
	
	public String getAssetcertifyflag() {
		return assetcertifyflag;
	}
	
	public void setAssetcertifyflag(String assetcertifyflag) {
		this.assetcertifyflag = assetcertifyflag;
	}
	
	public String getPicid() {
		return picid;
	}
	
	public void setPicid(String picid) {
		this.picid = picid;
	}
	
	public String getVerifystatus() {
		return verifystatus;
	}
	
	public void setVerifystatus(String verifystatus) {
		this.verifystatus = verifystatus;
	}
	
	public String getReturncode() {
		return returncode;
	}
	
	public void setReturncode(String returncode) {
		this.returncode = returncode;
	}
	
	public String getReturnmessage() {
		return returnmessage;
	}
	
	public void setReturnmessage(String returnmessage) {
		this.returnmessage = returnmessage;
	}

	public String getAssetcertifystatus() {
		return assetcertifystatus;
	}

	public void setAssetcertifystatus(String assetcertifystatus) {
		this.assetcertifystatus = assetcertifystatus;
	}

	public String getAssetcertifymaturity() {
		return assetcertifymaturity;
	}

	public void setAssetcertifymaturity(String assetcertifymaturity) {
		this.assetcertifymaturity = assetcertifymaturity;
	}

	public String getAssetcertifystatusvalue() {
		return assetcertifystatusvalue;
	}

	public void setAssetcertifystatusvalue(String assetcertifystatusvalue) {
		this.assetcertifystatusvalue = assetcertifystatusvalue;
	}	

}
