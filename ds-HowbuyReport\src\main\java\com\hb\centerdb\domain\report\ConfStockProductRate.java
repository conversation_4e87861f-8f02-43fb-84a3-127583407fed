package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 针对财务固收二级产品费用计算费率用
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class ConfStockProductRate implements Serializable {


    private static final long serialVersionUID = 553992223082906821L;
    /**
     * @Fields taskId : taskid
     */
    private String taskId;//主键

    private String fundCode;//基金code

    private String fundName;//基金名称

    private String bottomFundName;//底层产品名称

    private String productRange;//产品范围

    private String term;//期限

    private String contractDate;//合同日期

    private String fundType;//股权产品类型

    private BigDecimal collectSales= new BigDecimal(0);//收款销量(万元)

    private BigDecimal sales= new BigDecimal(0);//销量(万元)

    private BigDecimal subRate = new BigDecimal(0);//认购/销售服务费率

    private BigDecimal subLowerLimit= new BigDecimal(0);//认购下限

    private BigDecimal subUpLimit= new BigDecimal(0);//认购上限

    private BigDecimal subIncome= new BigDecimal(0);//认购收入

    private String subPayDate;//认购支付时间

    private String fixedRate;//固定费率

    private String fixedPayFrequency;//固定费用支付频率

    private BigDecimal periodRate1=new BigDecimal(0);//投资期费率1

    private BigDecimal investmentPeriod1=new BigDecimal(0);//投资期期限1

    private BigDecimal periodRate2=new BigDecimal(0);//投资期费率2

    private BigDecimal investmentPeriod2=new BigDecimal(0);//投资期期限2

    private BigDecimal quitRate=new BigDecimal(0);//退出期费率

    private BigDecimal quitPeriod=new BigDecimal(0);//退出期期限

    private String quitRemark;//退出期备注

    private String manageFormula;//管理费公式

    private BigDecimal fixedManage=new BigDecimal(0);//固定管理费

    private String fixedPayDate;//固定管理费支付时间

    private String performance;//业绩提成

    private String performanceFrequency;//业绩提成支付频率

    private BigDecimal performanceSupple=new BigDecimal(0);//业绩提成补充

    private String performancePayDate;//业绩提成支付时间

    private String investmentAfter;//投后

    private String agarement;//协议主体

    private String reviewState;//审核状态

    private Date reviewtime;//审核时间

    private String reviewer;//审核人

    private String creator;//创建者

    private Date createtime;//创建时间

    private String updater;//更新者

    private Date updattime;//更新时间

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getBottomFundName() {
        return bottomFundName;
    }

    public void setBottomFundName(String bottomFundName) {
        this.bottomFundName = bottomFundName;
    }

    public String getProductRange() {
        return productRange;
    }

    public void setProductRange(String productRange) {
        this.productRange = productRange;
    }

    public String getTerm() {
        return term;
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public String getContractDate() {
        return contractDate;
    }

    public void setContractDate(String contractDate) {
        this.contractDate = contractDate;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public BigDecimal getCollectSales() {
        return collectSales;
    }

    public void setCollectSales(BigDecimal collectSales) {
        this.collectSales = collectSales;
    }

    public BigDecimal getSales() {
        return sales;
    }

    public void setSales(BigDecimal sales) {
        this.sales = sales;
    }

    public BigDecimal getSubRate() {
        return subRate;
    }

    public void setSubRate(BigDecimal subRate) {
        this.subRate = subRate;
    }

    public BigDecimal getSubLowerLimit() {
        return subLowerLimit;
    }

    public void setSubLowerLimit(BigDecimal subLowerLimit) {
        this.subLowerLimit = subLowerLimit;
    }

    public BigDecimal getSubUpLimit() {
        return subUpLimit;
    }

    public void setSubUpLimit(BigDecimal subUpLimit) {
        this.subUpLimit = subUpLimit;
    }

    public BigDecimal getSubIncome() {
        return subIncome;
    }

    public void setSubIncome(BigDecimal subIncome) {
        this.subIncome = subIncome;
    }

    public String getSubPayDate() {
        return subPayDate;
    }

    public void setSubPayDate(String subPayDate) {
        this.subPayDate = subPayDate;
    }

    public String getFixedRate() {
        return fixedRate;
    }

    public void setFixedRate(String fixedRate) {
        this.fixedRate = fixedRate;
    }

    public String getFixedPayFrequency() {
        return fixedPayFrequency;
    }

    public void setFixedPayFrequency(String fixedPayFrequency) {
        this.fixedPayFrequency = fixedPayFrequency;
    }

    public BigDecimal getPeriodRate1() {
        return periodRate1;
    }

    public void setPeriodRate1(BigDecimal periodRate1) {
        this.periodRate1 = periodRate1;
    }

    public BigDecimal getInvestmentPeriod1() {
        return investmentPeriod1;
    }

    public void setInvestmentPeriod1(BigDecimal investmentPeriod1) {
        this.investmentPeriod1 = investmentPeriod1;
    }

    public BigDecimal getPeriodRate2() {
        return periodRate2;
    }

    public void setPeriodRate2(BigDecimal periodRate2) {
        this.periodRate2 = periodRate2;
    }

    public BigDecimal getInvestmentPeriod2() {
        return investmentPeriod2;
    }

    public void setInvestmentPeriod2(BigDecimal investmentPeriod2) {
        this.investmentPeriod2 = investmentPeriod2;
    }

    public BigDecimal getQuitRate() {
        return quitRate;
    }

    public void setQuitRate(BigDecimal quitRate) {
        this.quitRate = quitRate;
    }

    public BigDecimal getQuitPeriod() {
        return quitPeriod;
    }

    public void setQuitPeriod(BigDecimal quitPeriod) {
        this.quitPeriod = quitPeriod;
    }

    public String getQuitRemark() {
        return quitRemark;
    }

    public void setQuitRemark(String quitRemark) {
        this.quitRemark = quitRemark;
    }

    public String getManageFormula() {
        return manageFormula;
    }

    public void setManageFormula(String manageFormula) {
        this.manageFormula = manageFormula;
    }

    public BigDecimal getFixedManage() {
        return fixedManage;
    }

    public void setFixedManage(BigDecimal fixedManage) {
        this.fixedManage = fixedManage;
    }

    public String getFixedPayDate() {
        return fixedPayDate;
    }

    public void setFixedPayDate(String fixedPayDate) {
        this.fixedPayDate = fixedPayDate;
    }

    public String getPerformance() {
        return performance;
    }

    public void setPerformance(String performance) {
        this.performance = performance;
    }

    public String getPerformanceFrequency() {
        return performanceFrequency;
    }

    public void setPerformanceFrequency(String performanceFrequency) {
        this.performanceFrequency = performanceFrequency;
    }

    public BigDecimal getPerformanceSupple() {
        return performanceSupple;
    }

    public void setPerformanceSupple(BigDecimal performanceSupple) {
        this.performanceSupple = performanceSupple;
    }

    public String getPerformancePayDate() {
        return performancePayDate;
    }

    public void setPerformancePayDate(String performancePayDate) {
        this.performancePayDate = performancePayDate;
    }

    public String getInvestmentAfter() {
        return investmentAfter;
    }

    public void setInvestmentAfter(String investmentAfter) {
        this.investmentAfter = investmentAfter;
    }

    public String getAgarement() {
        return agarement;
    }

    public void setAgarement(String agarement) {
        this.agarement = agarement;
    }

    public String getReviewState() {
        return reviewState;
    }

    public void setReviewState(String reviewState) {
        this.reviewState = reviewState;
    }

    public Date getReviewtime() {
        return reviewtime;
    }

    public void setReviewtime(Date reviewtime) {
        this.reviewtime = reviewtime;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getUpdattime() {
        return updattime;
    }

    public void setUpdattime(Date updattime) {
        this.updattime = updattime;
    }
}
