package com.hb.crm.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类VBpFundBasicInfo.java
 * @version 1.0
 */
public class VBpFundBasicInfo implements Serializable {

private static final long serialVersionUID = 1L;

	private String fundCode;
	
	private String fundName;
	
	private String taCode;
	
	private String fundManCode;
	
	private String categoryId;
	
	private String currency;
	
	private String dfltDivMode;
	
	private String chgTrusteeMode;
	
	private String startTm;
	
	private String endTm;
	
	private String suppleSubsRule;
	
	private BigDecimal faceValue;
	
	private String feeCalMode;
	
	private BigDecimal minAcctVol;
	
	private String fundRiskLevel;
	
	private String recStat;
	
	private String checkFlag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String creDt;
	
	private String modDt;
	
	private String fundType;
	
	private String fundAttrPinyin;
	
	private String ipoEndTm;
	
	private String mainFundCode;
	
	private BigDecimal distributeSize;
	
	private String ipoStartDt;
	
	private String ipoEndDt;
	
	private String establishDt;
	
	private String fundCustodianCode;
	
	private String syncDate;
	
	private String fundAttr;
	
	private String partCode;
	
	private String fundAttrHb;
	
	private String fundSubType;
	
	private String redeOpenTerm;
	
	private String summary;
	
	private String fundOpenMode;
	
	private String yieldType;
	
	private String recommInfo;
	
	private String fundClass;
	
	private BigDecimal nav;
	
	private String fundStat;
	
	private BigDecimal sumOfNav;
	
	private String navDt;
	
	private BigDecimal minAppAmt;
	
	private String shareClass;
	
	private String shareStat;
	
	private BigDecimal agentDisc;
	
	private BigDecimal feeRate;
	
	private String pdSubsStat;
	
	private BigDecimal minSavingPlanNum;
	
	private String fundManName;
	
	private String fundManAbbr;
	
	public String getFundCode() {
		return this.fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}
	
	public String getFundName() {
		return this.fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}
	
	public String getTaCode() {
		return this.taCode;
	}

	public void setTaCode(String taCode) {
		this.taCode = taCode;
	}
	
	public String getFundManCode() {
		return this.fundManCode;
	}

	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}
	
	public String getCategoryId() {
		return this.categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}
	
	public String getCurrency() {
		return this.currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}
	
	public String getDfltDivMode() {
		return this.dfltDivMode;
	}

	public void setDfltDivMode(String dfltDivMode) {
		this.dfltDivMode = dfltDivMode;
	}
	
	public String getChgTrusteeMode() {
		return this.chgTrusteeMode;
	}

	public void setChgTrusteeMode(String chgTrusteeMode) {
		this.chgTrusteeMode = chgTrusteeMode;
	}
	
	public String getStartTm() {
		return this.startTm;
	}

	public void setStartTm(String startTm) {
		this.startTm = startTm;
	}
	
	public String getEndTm() {
		return this.endTm;
	}

	public void setEndTm(String endTm) {
		this.endTm = endTm;
	}
	
	public String getSuppleSubsRule() {
		return this.suppleSubsRule;
	}

	public void setSuppleSubsRule(String suppleSubsRule) {
		this.suppleSubsRule = suppleSubsRule;
	}
	
	public BigDecimal getFaceValue() {
		return this.faceValue;
	}

	public void setFaceValue(BigDecimal faceValue) {
		this.faceValue = faceValue;
	}
	
	public String getFeeCalMode() {
		return this.feeCalMode;
	}

	public void setFeeCalMode(String feeCalMode) {
		this.feeCalMode = feeCalMode;
	}
	
	public BigDecimal getMinAcctVol() {
		return this.minAcctVol;
	}

	public void setMinAcctVol(BigDecimal minAcctVol) {
		this.minAcctVol = minAcctVol;
	}
	
	public String getFundRiskLevel() {
		return this.fundRiskLevel;
	}

	public void setFundRiskLevel(String fundRiskLevel) {
		this.fundRiskLevel = fundRiskLevel;
	}
	
	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}
	
	public String getCheckFlag() {
		return this.checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}
	
	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}
	
	public String getFundType() {
		return this.fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}
	
	public String getFundAttrPinyin() {
		return this.fundAttrPinyin;
	}

	public void setFundAttrPinyin(String fundAttrPinyin) {
		this.fundAttrPinyin = fundAttrPinyin;
	}
	
	public String getIpoEndTm() {
		return this.ipoEndTm;
	}

	public void setIpoEndTm(String ipoEndTm) {
		this.ipoEndTm = ipoEndTm;
	}
	
	public String getMainFundCode() {
		return this.mainFundCode;
	}

	public void setMainFundCode(String mainFundCode) {
		this.mainFundCode = mainFundCode;
	}
	
	public BigDecimal getDistributeSize() {
		return this.distributeSize;
	}

	public void setDistributeSize(BigDecimal distributeSize) {
		this.distributeSize = distributeSize;
	}
	
	public String getIpoStartDt() {
		return this.ipoStartDt;
	}

	public void setIpoStartDt(String ipoStartDt) {
		this.ipoStartDt = ipoStartDt;
	}
	
	public String getIpoEndDt() {
		return this.ipoEndDt;
	}

	public void setIpoEndDt(String ipoEndDt) {
		this.ipoEndDt = ipoEndDt;
	}
	
	public String getEstablishDt() {
		return this.establishDt;
	}

	public void setEstablishDt(String establishDt) {
		this.establishDt = establishDt;
	}
	
	public String getFundCustodianCode() {
		return this.fundCustodianCode;
	}

	public void setFundCustodianCode(String fundCustodianCode) {
		this.fundCustodianCode = fundCustodianCode;
	}
	
	public String getSyncDate() {
		return this.syncDate;
	}

	public void setSyncDate(String syncDate) {
		this.syncDate = syncDate;
	}
	
	public String getFundAttr() {
		return this.fundAttr;
	}

	public void setFundAttr(String fundAttr) {
		this.fundAttr = fundAttr;
	}
	
	public String getPartCode() {
		return this.partCode;
	}

	public void setPartCode(String partCode) {
		this.partCode = partCode;
	}
	
	public String getFundAttrHb() {
		return this.fundAttrHb;
	}

	public void setFundAttrHb(String fundAttrHb) {
		this.fundAttrHb = fundAttrHb;
	}
	
	public String getFundSubType() {
		return this.fundSubType;
	}

	public void setFundSubType(String fundSubType) {
		this.fundSubType = fundSubType;
	}
	
	public String getRedeOpenTerm() {
		return this.redeOpenTerm;
	}

	public void setRedeOpenTerm(String redeOpenTerm) {
		this.redeOpenTerm = redeOpenTerm;
	}
	
	public String getSummary() {
		return this.summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}
	
	public String getFundOpenMode() {
		return this.fundOpenMode;
	}

	public void setFundOpenMode(String fundOpenMode) {
		this.fundOpenMode = fundOpenMode;
	}
	
	public String getYieldType() {
		return this.yieldType;
	}

	public void setYieldType(String yieldType) {
		this.yieldType = yieldType;
	}
	
	public String getRecommInfo() {
		return this.recommInfo;
	}

	public void setRecommInfo(String recommInfo) {
		this.recommInfo = recommInfo;
	}
	
	public String getFundClass() {
		return this.fundClass;
	}

	public void setFundClass(String fundClass) {
		this.fundClass = fundClass;
	}
	
	public BigDecimal getNav() {
		return this.nav;
	}

	public void setNav(BigDecimal nav) {
		this.nav = nav;
	}
	
	public String getFundStat() {
		return this.fundStat;
	}

	public void setFundStat(String fundStat) {
		this.fundStat = fundStat;
	}
	
	public BigDecimal getSumOfNav() {
		return this.sumOfNav;
	}

	public void setSumOfNav(BigDecimal sumOfNav) {
		this.sumOfNav = sumOfNav;
	}
	
	public String getNavDt() {
		return this.navDt;
	}

	public void setNavDt(String navDt) {
		this.navDt = navDt;
	}
	
	public BigDecimal getMinAppAmt() {
		return this.minAppAmt;
	}

	public void setMinAppAmt(BigDecimal minAppAmt) {
		this.minAppAmt = minAppAmt;
	}
	
	public String getShareClass() {
		return this.shareClass;
	}

	public void setShareClass(String shareClass) {
		this.shareClass = shareClass;
	}
	
	public String getShareStat() {
		return this.shareStat;
	}

	public void setShareStat(String shareStat) {
		this.shareStat = shareStat;
	}
	
	public BigDecimal getAgentDisc() {
		return this.agentDisc;
	}

	public void setAgentDisc(BigDecimal agentDisc) {
		this.agentDisc = agentDisc;
	}
	
	public BigDecimal getFeeRate() {
		return this.feeRate;
	}

	public void setFeeRate(BigDecimal feeRate) {
		this.feeRate = feeRate;
	}
	
	public String getPdSubsStat() {
		return this.pdSubsStat;
	}

	public void setPdSubsStat(String pdSubsStat) {
		this.pdSubsStat = pdSubsStat;
	}
	
	public BigDecimal getMinSavingPlanNum() {
		return this.minSavingPlanNum;
	}

	public void setMinSavingPlanNum(BigDecimal minSavingPlanNum) {
		this.minSavingPlanNum = minSavingPlanNum;
	}
	
	public String getFundManName() {
		return this.fundManName;
	}

	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}
	
	public String getFundManAbbr() {
		return this.fundManAbbr;
	}

	public void setFundManAbbr(String fundManAbbr) {
		this.fundManAbbr = fundManAbbr;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
