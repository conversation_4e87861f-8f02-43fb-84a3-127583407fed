package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类AutoLoadTaInfo.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class AutoLoadFundMan implements Serializable {

	private static final long serialVersionUID = 1L;

	private String fundManCode; // 基金管理人代码

	private String fundManName; // 基金管理人名称

	private String fundManAbbr; // 基金管理人简称

	private String fundManAttrPinyin; // 基金管理人简称

	public String getFundManCode() {
		return fundManCode;
	}

	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}

	public String getFundManName() {
		return fundManName;
	}

	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}

	public String getFundManAbbr() {
		return fundManAbbr;
	}

	public void setFundManAbbr(String fundManAbbr) {
		this.fundManAbbr = fundManAbbr;
	}

	public String getFundManAttrPinyin() {
		return fundManAttrPinyin;
	}

	public void setFundManAttrPinyin(String fundManAttrPinyin) {
		this.fundManAttrPinyin = fundManAttrPinyin;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
