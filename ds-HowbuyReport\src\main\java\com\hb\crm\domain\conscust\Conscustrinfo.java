package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Conscustrinfo.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Conscustrinfo implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustrid;
	
	private String conscustno;
	
	private String rcustname;
	
	private String email;
	
	private String mobile;
	
	private String idtype;
	
	private String idno;
	
	private String postcode;
	
	private String addr;
	
	private String telno;
	
	private String remark;
	
	private String creator;
	
	private String credt;
	
	private String moddt;
	
	private Date stimestamp;
	
	public String getConscustrid() {
		return this.conscustrid;
	}

	public void setConscustrid(String conscustrid) {
		this.conscustrid = conscustrid;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getRcustname() {
		return this.rcustname;
	}

	public void setRcustname(String rcustname) {
		this.rcustname = rcustname;
	}
	
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	public String getIdtype() {
		return this.idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}
	
	public String getIdno() {
		return this.idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}
	
	public String getPostcode() {
		return this.postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}
	
	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}
	
	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
