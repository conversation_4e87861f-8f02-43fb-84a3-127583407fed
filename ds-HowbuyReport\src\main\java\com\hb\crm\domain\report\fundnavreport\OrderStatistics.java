package com.hb.crm.domain.report.fundnavreport;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderStatistics implements Serializable {
	private String hbtype;
	private String hmcpx;
    private String saleType;//销售类型
	private String pcode;// 产品代码
	private String publishMan;// 发布人
	private String managerMan;// 公司名称(管理人)
	private String pname;// 产品名称
	private String prebookstate;//产品预约状态
	private String isonline;//线上线下
	private int orderNum = 0;// 预约客户数
	
	private int preType1 = 0;// 纸质成单客户数
	private int preType2 = 0;// 电子成单客户数

    private int holdDays = 0;// 持有天数
	
	private BigDecimal orderAmt = new BigDecimal(0);// 预约金额总
	private BigDecimal orderAmtRmb = new BigDecimal(0);// 预约金额人民币
	private BigDecimal orderAmtUs = new BigDecimal(0);// 预约金额美元
	private BigDecimal payAmt = new BigDecimal(0);// 打款金额
	private BigDecimal payInsureAmt = new BigDecimal(0);// 打款确认金额总
    private BigDecimal disPayInsureAmt = new BigDecimal(0);// 到账折标销量总
	private BigDecimal payInsureAmtRmb = new BigDecimal(0);// 打款确认金额人民币
	private BigDecimal payInsureAmtUs = new BigDecimal(0);// 打款确认金额美元

//	private BigDecimal callAmt = new BigDecimal(0);// 分次 call认缴金额	
	private String fccl;//线上线下

    private BigDecimal remainNum = new BigDecimal(0);// 剩余人数
    private BigDecimal remainAmt = new BigDecimal(0);// 剩余金额
    private BigDecimal realRemainNum = new BigDecimal(0);// 实际剩余金额
    private BigDecimal realRemainAmt = new BigDecimal(0);// 实际剩余金额
    private BigDecimal newIncreaseNum = new BigDecimal(0);// 新买入客户数

    private String userid;//登录的投顾userid
    private String operateType;//操作类型

    private String fundTypeInner;//产品类型(对内)

	/**
	 * 币种
	 */
	private String currency;
	
	public String getFccl() {
		return fccl;
	}
	public void setFccl(String fccl) {
		this.fccl = fccl;
	}
	
/*	public BigDecimal getCallAmt() {
		return callAmt;
	}
	public void setCallAmt(BigDecimal callAmt) {
		this.callAmt = callAmt;
	}*/
	
	public String getIsonline() {
		return isonline;
	}
	public void setIsonline(String isonline) {
		this.isonline = isonline;
	}
	public String getHbtype() {
		return hbtype;
	}
	public void setHbtype(String hbtype) {
		this.hbtype = hbtype;
	}
	public String getHmcpx() {
		return hmcpx;
	}
	public void setHmcpx(String hmcpx) {
		this.hmcpx = hmcpx;
	}
	public String getPcode() {
		return pcode;
	}
	public void setPcode(String pcode) {
		this.pcode = pcode;
	}
	public String getPublishMan() {
		return publishMan;
	}
	public void setPublishMan(String publishMan) {
		this.publishMan = publishMan;
	}
	public String getManagerMan() {
		return managerMan;
	}
	public void setManagerMan(String managerMan) {
		this.managerMan = managerMan;
	}
	public String getPname() {
		return pname;
	}
	public void setPname(String pname) {
		this.pname = pname;
	}
	public String getPrebookstate() {
		return prebookstate;
	}
	public void setPrebookstate(String prebookstate) {
		this.prebookstate = prebookstate;
	}
	public int getOrderNum() {
		return orderNum;
	}
	public void setOrderNum(int orderNum) {
		this.orderNum = orderNum;
	}
	public BigDecimal getOrderAmt() {
		return orderAmt;
	}
	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}
	public BigDecimal getPayAmt() {
		return payAmt;
	}
	public void setPayAmt(BigDecimal payAmt) {
		this.payAmt = payAmt;
	}
	public BigDecimal getPayInsureAmt() {
		return payInsureAmt;
	}
	public void setPayInsureAmt(BigDecimal payInsureAmt) {
		this.payInsureAmt = payInsureAmt;
	}
	public BigDecimal getOrderAmtRmb() {
		return orderAmtRmb;
	}
	public void setOrderAmtRmb(BigDecimal orderAmtRmb) {
		this.orderAmtRmb = orderAmtRmb;
	}
	public BigDecimal getOrderAmtUs() {
		return orderAmtUs;
	}
	public void setOrderAmtUs(BigDecimal orderAmtUs) {
		this.orderAmtUs = orderAmtUs;
	}
	public BigDecimal getPayInsureAmtRmb() {
		return payInsureAmtRmb;
	}
	public void setPayInsureAmtRmb(BigDecimal payInsureAmtRmb) {
		this.payInsureAmtRmb = payInsureAmtRmb;
	}
	public BigDecimal getPayInsureAmtUs() {
		return payInsureAmtUs;
	}
	public void setPayInsureAmtUs(BigDecimal payInsureAmtUs) {
		this.payInsureAmtUs = payInsureAmtUs;
	}
	public int getPreType1() {
		return preType1;
	}
	public void setPreType1(int preType1) {
		this.preType1 = preType1;
	}
	public int getPreType2() {
		return preType2;
	}
	public void setPreType2(int preType2) {
		this.preType2 = preType2;
	}

    public int getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(int holdDays) {
        this.holdDays = holdDays;
    }

    public BigDecimal getDisPayInsureAmt() {
        return disPayInsureAmt;
    }

    public void setDisPayInsureAmt(BigDecimal disPayInsureAmt) {
        this.disPayInsureAmt = disPayInsureAmt;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public BigDecimal getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(BigDecimal remainNum) {
        this.remainNum = remainNum;
    }

    public BigDecimal getRemainAmt() {
        return remainAmt;
    }

    public void setRemainAmt(BigDecimal remainAmt) {
        this.remainAmt = remainAmt;
    }

    public BigDecimal getRealRemainNum() {
        return realRemainNum;
    }

    public void setRealRemainNum(BigDecimal realRemainNum) {
        this.realRemainNum = realRemainNum;
    }

    public BigDecimal getRealRemainAmt() {
        return realRemainAmt;
    }

    public void setRealRemainAmt(BigDecimal realRemainAmt) {
        this.realRemainAmt = realRemainAmt;
    }

    public BigDecimal getNewIncreaseNum() {
        return newIncreaseNum;
    }

    public void setNewIncreaseNum(BigDecimal newIncreaseNum) {
        this.newIncreaseNum = newIncreaseNum;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getFundTypeInner() {
        return fundTypeInner;
    }

    public void setFundTypeInner(String fundTypeInner) {
        this.fundTypeInner = fundTypeInner;
    }

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}
}
