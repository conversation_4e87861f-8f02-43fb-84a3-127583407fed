package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class QuarterPerformance implements Serializable {

    private static final long serialVersionUID = 3256420863434073294L;

    private String conscustNo; // 投顾客户号
	
	private String custName;//客户名称
	
	private String custNo;//客户号	
	
	private String consCode;//投顾
	
	private String consName;//投顾

    private String orgCode;//部门code

    private String orgName;//部门名称

    private String fundCode;//基金code

    private String fundtype;//基金类型

    private String fundAttr;//投资基金简称

    private Double avgDayBal=0d;//日均资产

    private Double avgQuarterDayBal=0d;//客户季度日均存量

    private String busiCode;//费用类型code

    private String busiName;//费用类型

    private Double svcFee=0d;//服务费

    private Double tailcommisionFee=0d;//尾佣

    private Double svcIncome1=0d;//分配基数1

    private Double svcIncome2=0d;//分配基数2

    private String tradeDt;//交易日期

	private Double ackAmtRmb=0d;//交易确认金额

    private Double commissionFee=0d;//好买费用收入

    private Double superviseFee=0d;//监管费用支出

    private String startDt;//分配日期

    private Double discount=0d;//折标系数

    private Double disCountSale=0d;//折标销量

    private Double multiple=0d;//除倍数

    private Double accureDisCountSale=0d;//实际折标销量

    private Double commissionRate=0d;//佣金比例

    private Double commission=0d;//佣金

    private Double accureCommission=0d;//实际佣金

    private String startdt;//开始日期

    private String enddt;//结束日期

    private String taskId;//id

    private String firstDictname;//一级来源

    private String secondDictname;//二级来源

    private Double sourcecoefficient=0d;//来源系数

    private String isBigV;//是否大v

    private String is20wConcode;//20w投顾

    private String is20wCust;//20w客户


    private Double zbCoeff=0d;//客户折标系数
    private Double manageCoeff=0d;//管理系数
    private Double zbSale= 0d;//折标系数销量
    private Double manageSale= 0d;//管理系数销量
    private String sourcetype;//客户类型
    private Double manageCommission= 0d;//管理系数佣金

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public Double getAvgDayBal() {
        return avgDayBal;
    }

    public void setAvgDayBal(Double avgDayBal) {
        this.avgDayBal = avgDayBal;
    }

    public Double getAvgQuarterDayBal() {
        return avgQuarterDayBal;
    }

    public void setAvgQuarterDayBal(Double avgQuarterDayBal) {
        this.avgQuarterDayBal = avgQuarterDayBal;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getBusiName() {
        return busiName;
    }

    public void setBusiName(String busiName) {
        this.busiName = busiName;
    }

    public Double getSvcFee() {
        return svcFee;
    }

    public void setSvcFee(Double svcFee) {
        this.svcFee = svcFee;
    }

    public Double getTailcommisionFee() {
        return tailcommisionFee;
    }

    public void setTailcommisionFee(Double tailcommisionFee) {
        this.tailcommisionFee = tailcommisionFee;
    }

    public Double getSvcIncome1() {
        return svcIncome1;
    }

    public void setSvcIncome1(Double svcIncome1) {
        this.svcIncome1 = svcIncome1;
    }

    public Double getSvcIncome2() {
        return svcIncome2;
    }

    public void setSvcIncome2(Double svcIncome2) {
        this.svcIncome2 = svcIncome2;
    }

    public Double getAckAmtRmb() {
        return ackAmtRmb;
    }

    public void setAckAmtRmb(Double ackAmtRmb) {
        this.ackAmtRmb = ackAmtRmb;
    }

    public Double getCommissionFee() {
        return commissionFee;
    }

    public void setCommissionFee(Double commissionFee) {
        this.commissionFee = commissionFee;
    }

    public Double getSuperviseFee() {
        return superviseFee;
    }

    public void setSuperviseFee(Double superviseFee) {
        this.superviseFee = superviseFee;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getDisCountSale() {
        return disCountSale;
    }

    public void setDisCountSale(Double disCountSale) {
        this.disCountSale = disCountSale;
    }

    public Double getMultiple() {
        return multiple;
    }

    public void setMultiple(Double multiple) {
        this.multiple = multiple;
    }

    public Double getAccureDisCountSale() {
        return accureDisCountSale;
    }

    public void setAccureDisCountSale(Double accureDisCountSale) {
        this.accureDisCountSale = accureDisCountSale;
    }

    public Double getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(Double commissionRate) {
        this.commissionRate = commissionRate;
    }

    public Double getCommission() {
        return commission;
    }

    public void setCommission(Double commission) {
        this.commission = commission;
    }

    public Double getAccureCommission() {
        return accureCommission;
    }

    public void setAccureCommission(Double accureCommission) {
        this.accureCommission = accureCommission;
    }

    public String getStartdt() {
        return startdt;
    }

    public void setStartdt(String startdt) {
        this.startdt = startdt;
    }

    public String getEnddt() {
        return enddt;
    }

    public void setEnddt(String enddt) {
        this.enddt = enddt;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFirstDictname() {
        return firstDictname;
    }

    public void setFirstDictname(String firstDictname) {
        this.firstDictname = firstDictname;
    }

    public String getSecondDictname() {
        return secondDictname;
    }

    public void setSecondDictname(String secondDictname) {
        this.secondDictname = secondDictname;
    }

    public Double getSourcecoefficient() {
        return sourcecoefficient;
    }

    public void setSourcecoefficient(Double sourcecoefficient) {
        this.sourcecoefficient = sourcecoefficient;
    }

    public String getIsBigV() {
        return isBigV;
    }

    public void setIsBigV(String isBigV) {
        this.isBigV = isBigV;
    }

    public String getIs20wConcode() {
        return is20wConcode;
    }

    public void setIs20wConcode(String is20wConcode) {
        this.is20wConcode = is20wConcode;
    }

    public String getIs20wCust() {
        return is20wCust;
    }

    public void setIs20wCust(String is20wCust) {
        this.is20wCust = is20wCust;
    }

    public String getFundtype() {
        return fundtype;
    }

    public void setFundtype(String fundtype) {
        this.fundtype = fundtype;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public Double getZbCoeff() {
        return zbCoeff;
    }

    public void setZbCoeff(Double zbCoeff) {
        this.zbCoeff = zbCoeff;
    }

    public Double getManageCoeff() {
        return manageCoeff;
    }

    public void setManageCoeff(Double manageCoeff) {
        this.manageCoeff = manageCoeff;
    }

    public Double getZbSale() {
        return zbSale;
    }

    public void setZbSale(Double zbSale) {
        this.zbSale = zbSale;
    }

    public Double getManageSale() {
        return manageSale;
    }

    public void setManageSale(Double manageSale) {
        this.manageSale = manageSale;
    }

    public String getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(String sourcetype) {
        this.sourcetype = sourcetype;
    }

    public Double getManageCommission() {
        return manageCommission;
    }

    public void setManageCommission(Double manageCommission) {
        this.manageCommission = manageCommission;
    }
}
