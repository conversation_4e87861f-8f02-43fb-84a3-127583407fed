package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类AutoLoadPrivFund.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class AutoLoadConference implements Serializable {

	private static final long serialVersionUID = 1L;

	private String conferenceId; //

	private String conferenceName; //

    private String conferenceNameType; //会议类型

	public String getConferenceId() {
		return conferenceId;
	}

	public void setConferenceId(String conferenceId) {
		this.conferenceId = conferenceId;
	}

	public String getConferenceName() {
		return conferenceName;
	}

	public void setConferenceName(String conferenceName) {
		this.conferenceName = conferenceName;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

    public String getConferenceNameType() {
        return conferenceNameType;
    }

    public void setConferenceNameType(String conferenceNameType) {
        this.conferenceNameType = conferenceNameType;
    }
}
