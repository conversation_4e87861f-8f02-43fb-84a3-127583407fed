package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class AppPvPage implements Serializable {

	private static final long serialVersionUID = 1L;

	private String hbonNo; // 一账通号

	private String custTradeNo; // 交易账号

	private String consCustNo; // 投顾客户号

	private String consCustName; // 客户姓名

	private String consCode; // 投顾账号

	private String consName; // 投顾姓名

    private String fundCode; //基金代码

    private String jjjc; //基金简称

    private String days30FundPv; //近30天产品页PV

    private String days30FundGap; //近30天产品页PV平均停留时长

    private String days14FundPv; //近14天产品页PV

    private String days14FundGap; //近14天产品页PV平均停留时长

    private String pvSp; //近30天产品相关视频页PV

    private String pvYb; //近30天产品相关研报页PV

    private String pvYp; //近30天产品相关音频页PV

    private String pvZx; //近30天产品相关资讯页PV

    private String txnFlag; //是否购买

    private String xsbz; //是否在销

    private String balanceVol; //持仓份额

    private String marketCap; //持仓市值

    private String balanceIncome; //当前持仓收益

    private String accumIncome; //累计收益

    private String favFlag; //当前是否收藏

	private String classifyName; // 一级部门

	private String outletName; // 二级部门

	private String appPid;// 
	
	private String appPname; //
	
	private String appPtype;//

	private int pv30; // 最近30天访问量

	private int pv14; // 最近14天访问量

	private String lastDay;// 最近一次访问日期

	public String getHbonNo() {
		return hbonNo;
	}

	public void setHbonNo(String hbonNo) {
		this.hbonNo = hbonNo;
	}

	public String getCustTradeNo() {
		return custTradeNo;
	}

	public void setCustTradeNo(String custTradeNo) {
		this.custTradeNo = custTradeNo;
	}

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getConsCustName() {
		return consCustName;
	}

	public void setConsCustName(String consCustName) {
		this.consCustName = consCustName;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getClassifyName() {
		return classifyName;
	}

	public void setClassifyName(String classifyName) {
		this.classifyName = classifyName;
	}

	public String getOutletName() {
		return outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}

	public String getAppPid() {
		return appPid;
	}

	public void setAppPid(String appPid) {
		this.appPid = appPid;
	}

	public String getAppPname() {
		return appPname;
	}

	public void setAppPname(String appPname) {
		this.appPname = appPname;
	}

	public String getAppPtype() {
		return appPtype;
	}

	public void setAppPtype(String appPtype) {
		this.appPtype = appPtype;
	}

	public int getPv30() {
		return pv30;
	}

	public void setPv30(int pv30) {
		this.pv30 = pv30;
	}

	public int getPv14() {
		return pv14;
	}

	public void setPv14(int pv14) {
		this.pv14 = pv14;
	}

	public String getLastDay() {
		return lastDay;
	}

	public void setLastDay(String lastDay) {
		this.lastDay = lastDay;
	}


    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getJjjc() {
        return jjjc;
    }

    public void setJjjc(String jjjc) {
        this.jjjc = jjjc;
    }

    public String getDays30FundPv() {
        return days30FundPv;
    }

    public void setDays30FundPv(String days30FundPv) {
        this.days30FundPv = days30FundPv;
    }

    public String getDays30FundGap() {
        return days30FundGap;
    }

    public void setDays30FundGap(String days30FundGap) {
        this.days30FundGap = days30FundGap;
    }

    public String getDays14FundPv() {
        return days14FundPv;
    }

    public void setDays14FundPv(String days14FundPv) {
        this.days14FundPv = days14FundPv;
    }

    public String getDays14FundGap() {
        return days14FundGap;
    }

    public void setDays14FundGap(String days14FundGap) {
        this.days14FundGap = days14FundGap;
    }

    public String getPvSp() {
        return pvSp;
    }

    public void setPvSp(String pvSp) {
        this.pvSp = pvSp;
    }

    public String getPvYb() {
        return pvYb;
    }

    public void setPvYb(String pvYb) {
        this.pvYb = pvYb;
    }

    public String getPvYp() {
        return pvYp;
    }

    public void setPvYp(String pvYp) {
        this.pvYp = pvYp;
    }

    public String getPvZx() {
        return pvZx;
    }

    public void setPvZx(String pvZx) {
        this.pvZx = pvZx;
    }

    public String getTxnFlag() {
        return txnFlag;
    }

    public void setTxnFlag(String txnFlag) {
        this.txnFlag = txnFlag;
    }

    public String getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(String balanceVol) {
        this.balanceVol = balanceVol;
    }

    public String getMarketCap() {
        return marketCap;
    }

    public void setMarketCap(String marketCap) {
        this.marketCap = marketCap;
    }

    public String getBalanceIncome() {
        return balanceIncome;
    }

    public void setBalanceIncome(String balanceIncome) {
        this.balanceIncome = balanceIncome;
    }

    public String getAccumIncome() {
        return accumIncome;
    }

    public void setAccumIncome(String accumIncome) {
        this.accumIncome = accumIncome;
    }

    public String getFavFlag() {
        return favFlag;
    }

    public void setFavFlag(String favFlag) {
        this.favFlag = favFlag;
    }

    public String getXsbz() {
        return xsbz;
    }

    public void setXsbz(String xsbz) {
        this.xsbz = xsbz;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
}
