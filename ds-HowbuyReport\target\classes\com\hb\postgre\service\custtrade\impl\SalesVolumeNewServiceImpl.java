package com.hb.postgre.service.custtrade.impl;

import com.hb.crm.domain.report.fundnavreport.SalesVolume;
import com.hb.crm.tools.CommPageBean;
import com.hb.crm.tools.PageData;
import com.hb.postgre.domain.trade.CustTradeNew;
import com.hb.postgre.domain.trade.DmGmNetIncreaseSum;
import com.hb.postgre.domain.trade.GdTurnoverNew;
import com.hb.postgre.persistence.custtrade.SalesVolumeNewMapper;
import com.hb.postgre.service.custtrade.SalesVolumeNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service(value="SalesVolumeNewService")
@Transactional(value="postgredbTransaction")
public class SalesVolumeNewServiceImpl implements SalesVolumeNewService {
	
	@Autowired
	private SalesVolumeNewMapper salesVolumeNewMapper;
	
	public List<SalesVolume> listSalesVolumeReport(Map<String,String> param){
		return salesVolumeNewMapper.listSalesVolumeReport(param);
	}


    public PageData<SalesVolume> listSalesVolumeReportByPage(Map<String, String> param) {

        PageData<SalesVolume> pageData = new PageData();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<SalesVolume> cmConslevelrangesetList = salesVolumeNewMapper.listSalesVolumeReportByPage(param, pageBean);
        pageData.setPageBean(pageBean);
        pageData.setListData(cmConslevelrangesetList);

        return pageData;
    }

    @Override
    public List<GdTurnoverNew> listNetAppIncreaseReport(Map<String,String> param){
        return salesVolumeNewMapper.listNetAppIncreaseReport(param);
    }


    /**
     * 公募净申购汇总
     * @param param
     * @return
     */
    public List<DmGmNetIncreaseSum> listPubFundNetAppIncreaseReport(Map<String,String> param){
        return salesVolumeNewMapper.listPubFundNetAppIncreaseReport(param);
    }
    /**
     * 公募净申购明细
     * @param param
     * @return
     */
    public PageData<CustTradeNew> listPubFundNetAppIncreaseDtlReportByPage(Map<String, String> param) {

        PageData<CustTradeNew> pageData = new PageData<CustTradeNew>();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<CustTradeNew> dtlList = salesVolumeNewMapper.listPubFundNetAppIncreaseDtlReportByPage(param, pageBean);
        pageData.setPageBean(pageBean);
        pageData.setListData(dtlList);

        return pageData;
    }
    public List<CustTradeNew> listPubFundNetAppIncreaseDtlReport(Map<String,String> param){
        return salesVolumeNewMapper.listPubFundNetAppIncreaseDtlReport(param);
    }

    public long countPubFundNetAppIncreaseDtlReport(Map<String,String> param){
        return salesVolumeNewMapper.countPubFundNetAppIncreaseDtlReport(param);
    }

    /**
     * 公募转托管汇总
     * @param param
     * @return
     */
    public List<Map<String,Object>> listPubFundTransferReport(Map<String,String> param){
        return salesVolumeNewMapper.listPubFundTransferReport(param);
    }
    /**
     * 公募转托管明细
     * @param param
     * @return
     */
    public PageData<CustTradeNew> listPubFundTransferReportDtlReportByPage(Map<String, String> param) {

        PageData<CustTradeNew> pageData = new PageData<CustTradeNew>();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<CustTradeNew> dtlList = salesVolumeNewMapper.listPubFundTransferReportDtlReportByPage(param, pageBean);
        pageData.setPageBean(pageBean);
        pageData.setListData(dtlList);

        return pageData;
    }
    public List<CustTradeNew> listPubFundTransferReportDtlReport(Map<String,String> param){
        return salesVolumeNewMapper.listPubFundTransferReportDtlReport(param);
    }

    /**
     * 净申购投顾报表
     *
     * @param param
     * @return
     */
    @Override
    public List<GdTurnoverNew> listConscodeNetAppIncreaseReport(Map<String, String> param) {
        return salesVolumeNewMapper.listConscodeNetAppIncreaseReport(param);
    }

    /**
     * @param param
     * @return List<Map < String, Object>>
     * @Description:净申购投顾 客户明细报表 分页
     */
    @Override
    public PageData<GdTurnoverNew> listCustConscodeNetIncreaseDtlReportByPage(Map<String, String> param) {
        PageData<GdTurnoverNew> pageData = new PageData();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<GdTurnoverNew> dtlList = salesVolumeNewMapper.listCustConscodeNetIncreaseDtlReportByPage(param, pageBean);
        pageData.setPageBean(pageBean);
        pageData.setListData(dtlList);

        return pageData;
    }

    /**
     * 净申购投顾报表 客户明细
     *
     * @param param
     * @return
     */
    @Override
    public List<GdTurnoverNew> listCustConscodeNetIncreaseDtlReport(Map<String, String> param) {
        return salesVolumeNewMapper.listCustConscodeNetIncreaseDtlReport(param);
    }

    /**
     * @param param
     * @return List<Map < String, Object>>
     * @Description:净申购投顾 产品 明细报表 分页
     */
    @Override
    public PageData<GdTurnoverNew> listCustConscodeNetIncreaseProductDtlReportByPage(Map<String, String> param) {
        PageData<GdTurnoverNew> pageData = new PageData();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<GdTurnoverNew> dtlList = salesVolumeNewMapper.listCustConscodeNetIncreaseProductDtlReportByPage(param, pageBean);
        pageData.setPageBean(pageBean);
        pageData.setListData(dtlList);

        return pageData;
    }

    /**
     * 净申购投顾报表 产品明细
     *
     * @param param
     * @return
     */
    @Override
    public List<GdTurnoverNew> listCustConscodeNetIncreaseProductDtlReport(Map<String, String> param) {
        return salesVolumeNewMapper.listCustConscodeNetIncreaseProductDtlReport(param);
    }

    /**
     * @param param
     * @return List<Map < String, Object>>
     * @Description:净申购投顾 交易明细报表 分页
     */
    @Override
    public PageData<GdTurnoverNew> listCustConscodeNetIncreaseTradeDtlReportByPage(Map<String, String> param) {
        PageData<GdTurnoverNew> pageData = new PageData();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<GdTurnoverNew> dtlList = salesVolumeNewMapper.listCustConscodeNetIncreaseTradeDtlReportByPage(param, pageBean);
        pageData.setPageBean(pageBean);
        pageData.setListData(dtlList);

        return pageData;
    }

    /**
     * 净申购投顾报表 交易明细
     *
     * @param param
     * @return
     */
    @Override
    public List<GdTurnoverNew> listCustConscodeNetIncreaseTradeDtlReport(Map<String, String> param) {
        return salesVolumeNewMapper.listCustConscodeNetIncreaseTradeDtlReport(param);
    }
}

