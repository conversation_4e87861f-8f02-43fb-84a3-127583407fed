package com.hb.centerdb.domain.fince;

import java.io.Serializable;
import java.math.BigDecimal;

public class FinceBuyReport implements Serializable {

    private static final long serialVersionUID = 6592754651245600259L;

    /**
     * @Description: 录入日期
     */
    private String credt;
    /**
     * @Description: 成交日期
     */
    private String expectpayamtdt;
    /**
     * @Description: 好买产品线
     */
    private String hbtype;
    /**
     * @Description: 管理人
     */
    private String managerMan;
    /**
     * @Description: 发布人
     */
    private String publishMan;
    /**
     * @Description: 产品code
     */
    private String pcode;
    /**
     * @Description: 产品名称
     */
    private String pname;
    /**
     * @Description: 客户姓名
     */
    private String conscustname;
    /**
     * @Description: 投顾客户号
     */
    private String conscustno;
    /**
     * @Description: 投顾code
     */
    private String conscode;
    /**
     * @Description: 交易类型 认购/申购
     */
    private String tradeType;
    /**
     * @Description: 认购金额（万）
     */
    private BigDecimal buyamt = new BigDecimal(0);
    /**
     * @Description: 币种
     */
    private String currency;
    /**
     * @Description: 手续费（万）
     */
    private BigDecimal fee = new BigDecimal(0);
    /**
     * @Description: 一级部门
     */
    private String u1name;
    /**
     * @Description: 二级部门
     */
    private String u2name;
    /**
     * @Description: 三级部门
     */
    private String departCode;
    /**
     * @Description: 成交客户经理
     */
    private String custManager;
    /**
     * @Description: 录入日期
     */
    private String creatdt;
    /**
     * @Description: 折扣类型
     */
    private String discounttype;
    /**
     * @Description: 税后折扣金额
     */
    private BigDecimal afterdiscountamt = new BigDecimal(0);
    /**
     * @Description: 税前折扣金额
     */
    private BigDecimal beforediscountamt = new BigDecimal(0);
    /**
     * @Description: 税前MGM金额
     */
    private BigDecimal beforemgmamt = new BigDecimal(0);
    /**
     * @Description: 客户来源
     */
    private String custSource;

    /**
     * @Description: 金蝶k3
     */
    private String kingCode;

    public String getCredt() {
        return credt;
    }

    public void setCredt(String credt) {
        this.credt = credt;
    }

    public String getExpectpayamtdt() {
        return expectpayamtdt;
    }

    public void setExpectpayamtdt(String expectpayamtdt) {
        this.expectpayamtdt = expectpayamtdt;
    }

    public String getHbtype() {
        return hbtype;
    }

    public void setHbtype(String hbtype) {
        this.hbtype = hbtype;
    }

    public String getManagerMan() {
        return managerMan;
    }

    public void setManagerMan(String managerMan) {
        this.managerMan = managerMan;
    }

    public String getPublishMan() {
        return publishMan;
    }

    public void setPublishMan(String publishMan) {
        this.publishMan = publishMan;
    }

    public String getPcode() {
        return pcode;
    }

    public void setPcode(String pcode) {
        this.pcode = pcode;
    }

    public String getPname() {
        return pname;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public String getConscustname() {
        return conscustname;
    }

    public void setConscustname(String conscustname) {
        this.conscustname = conscustname;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public BigDecimal getBuyamt() {
        return buyamt;
    }

    public void setBuyamt(BigDecimal buyamt) {
        this.buyamt = buyamt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getDepartCode() {
        return departCode;
    }

    public void setDepartCode(String departCode) {
        this.departCode = departCode;
    }

    public String getCustManager() {
        return custManager;
    }

    public void setCustManager(String custManager) {
        this.custManager = custManager;
    }

    public String getCreatdt() {
        return creatdt;
    }

    public void setCreatdt(String creatdt) {
        this.creatdt = creatdt;
    }

    public String getDiscounttype() {
        return discounttype;
    }

    public void setDiscounttype(String discounttype) {
        this.discounttype = discounttype;
    }

    public BigDecimal getAfterdiscountamt() {
        return afterdiscountamt;
    }

    public void setAfterdiscountamt(BigDecimal afterdiscountamt) {
        this.afterdiscountamt = afterdiscountamt;
    }

    public BigDecimal getBeforediscountamt() {
        return beforediscountamt;
    }

    public void setBeforediscountamt(BigDecimal beforediscountamt) {
        this.beforediscountamt = beforediscountamt;
    }

    public BigDecimal getBeforemgmamt() {
        return beforemgmamt;
    }

    public void setBeforemgmamt(BigDecimal beforemgmamt) {
        this.beforemgmamt = beforemgmamt;
    }

    public String getCustSource() {
        return custSource;
    }

    public void setCustSource(String custSource) {
        this.custSource = custSource;
    }

    public String getKingCode() {
        return kingCode;
    }

    public void setKingCode(String kingCode) {
        this.kingCode = kingCode;
    }
}
