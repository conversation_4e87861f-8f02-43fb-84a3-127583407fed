package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 产品系数异步任务执行DTO
 * @reason:
 * @Date: 2021年12月6日10:18:41
 */
public class CmProductCoefficientAsy implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 待执行产品系数ID,逗号分割
     */
    private String taskIds;
    /**
     * 任务状态  0新建1执行中2执行完成3执行异常
     */
    private String taskStatus;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人
     */
    private String modor;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(String taskIds) {
        this.taskIds = taskIds;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
