/**
 * 
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 交易渠道.
 */
public enum TradeChan {
	/**
	 * 柜台.
	 */
	Counter("1"),
	/**
	 * 网上.
	 */
	Internet("2"),
	/**
	 * 电话.
	 */
	Tel("3"),
	/**
	 * 无线.
	 */
	Wireless("4");
	private String value;

    public String getValue() {
        return value;
    }

    TradeChan(String value) {
        this.value = value;
    }
public final static Map<TradeChan, String> tradeChannumMAP;
    
    static{

    	tradeChannumMAP = new EnumMap<TradeChan, String>(TradeChan.class);
    	tradeChannumMAP.put(TradeChan.Counter, "柜台");
    	tradeChannumMAP.put(TradeChan.Internet, "网上");
    	tradeChannumMAP.put(TradeChan.Tel, "电话");
    	tradeChannumMAP.put(TradeChan.Wireless, "无线");
    
    }
    /**
     * 跟据value返回枚举对应的key
     * 
     * @param value
     * @return LinkMethod
     */
    public static TradeChan getEnumMAPKey(String value) {
    	TradeChan tmpKey = null;
        for (TradeChan tmpEnum : TradeChan.values()) {
            if (tmpEnum.value.equals(value)) {
                tmpKey = tmpEnum;
                break;
            }
        }
        return tmpKey;
    }
    /**
     * 返回对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final String value) {
        return TradeChan.tradeChannumMAP.get(
        		TradeChan.getEnumMAPKey(value));
    }
}
