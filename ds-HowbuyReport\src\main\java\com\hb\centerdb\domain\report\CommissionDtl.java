package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类CommissionDtl.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CommissionDtl implements Serializable {

private static final long serialVersionUID = 1L;

	private String tradeDt;//交易日期
	
	private String fofConsCode;//FOF投管人
	
	private String custName;//客户名称
	
	private String custNo;//客户号	
	
	private String consCode;//投顾

    private String outletcode;//部门code
	
	private String orgName;//部门名称
	
	private String fundTxAcctNo;//基金交易账号
	
	private String busiCode;//费用类型
	
	private String fundType;//基金类型
	
	private String fundAttr;//投资基金简称
	
	private String fundCode;//基金代码
		
	private Double ackAmt=0d;//交易确认金额
	
	private Double commissionFee=0d;//好买费用收入
	
	private Double superviseFee=0d;//监管费用支出
	
	private Double commissionIncome=0d;//分配基数
	
	private String consultantTelno;//投顾分机	
	
	private String startDt;//分配日期
	
	private String firstDictname;//一级来源
	
	private String secondDictname;//二级来源
	
	private Double sourceCoefficient=0d;//来源系数
	
	private String teamCode;//二级来源
	
	private String consName;//投顾

    private String year;//年度

    private String quarter;//季度

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getTeamCode() {
		return teamCode;
	}

	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getFofConsCode() {
		return fofConsCode;
	}


	public void setFofConsCode(String fofConsCode) {
		this.fofConsCode = fofConsCode;
	}

	public String getCustName() {
		return custName;
	}



	public void setCustName(String custName) {
		this.custName = custName;
	}



	public String getCustNo() {
		return custNo;
	}



	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}



	public String getConsCode() {
		return consCode;
	}



	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}


	public String getOrgName() {
		return orgName;
	}



	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}



	public String getFundTxAcctNo() {
		return fundTxAcctNo;
	}



	public void setFundTxAcctNo(String fundTxAcctNo) {
		this.fundTxAcctNo = fundTxAcctNo;
	}



	public String getBusiCode() {
		return busiCode;
	}



	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}



	public String getFundType() {
		return fundType;
	}



	public void setFundType(String fundType) {
		this.fundType = fundType;
	}



	public String getFundAttr() {
		return fundAttr;
	}



	public void setFundAttr(String fundAttr) {
		this.fundAttr = fundAttr;
	}



	public String getFundCode() {
		return fundCode;
	}



	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}



	public Double getAckAmt() {
		return ackAmt;
	}



	public void setAckAmt(Double ackAmt) {
		this.ackAmt = ackAmt;
	}



	public Double getCommissionFee() {
		return commissionFee;
	}



	public void setCommissionFee(Double commissionFee) {
		this.commissionFee = commissionFee;
	}



	public Double getSuperviseFee() {
		return superviseFee;
	}



	public void setSuperviseFee(Double superviseFee) {
		this.superviseFee = superviseFee;
	}



	public Double getCommissionIncome() {
		return commissionIncome;
	}



	public void setCommissionIncome(Double commissionIncome) {
		this.commissionIncome = commissionIncome;
	}



	public String getConsultantTelno() {
		return consultantTelno;
	}



	public void setConsultantTelno(String consultantTelno) {
		this.consultantTelno = consultantTelno;
	}



	public String getStartDt() {
		return startDt;
	}



	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}



	public String getFirstDictname() {
		return firstDictname;
	}



	public void setFirstDictname(String firstDictname) {
		this.firstDictname = firstDictname;
	}



	public String getSecondDictname() {
		return secondDictname;
	}



	public void setSecondDictname(String secondDictname) {
		this.secondDictname = secondDictname;
	}



	public Double getSourceCoefficient() {
		return sourceCoefficient;
	}



	public void setSourceCoefficient(Double sourceCoefficient) {
		this.sourceCoefficient = sourceCoefficient;
	}

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getQuarter() {
        return quarter;
    }

    public void setQuarter(String quarter) {
        this.quarter = quarter;
    }

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
