package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.math.BigDecimal;

public class MemberCenterFundBean implements Serializable {

	// 基金类型
	private String fundType;

	private String bigType;
	// 基金代码
	private String fundCode;
	// 基金名称
	private String fundName;
	// 持仓成本
	private BigDecimal currentCost = BigDecimal.ZERO;
	//持仓成本Str
	private String currentCostStr;
	// 份额
	private BigDecimal balanceVol;
	// 市值
	private BigDecimal marketCap = BigDecimal.ZERO;
	//市值Str
	private String   marketCapStr;
	// 当前净值
	private BigDecimal nav = BigDecimal.ZERO;
	// 净值日期
	private String navDate;
	// 当前收益
	private BigDecimal fundInc = BigDecimal.ZERO;
	//当前收益str
	private String fundIncStr;
	// 当前收益率
	private BigDecimal floatProfit = BigDecimal.ZERO;
	// 当前收益率str
	private String floatProfitStr;
	// 七日年化收益
	private BigDecimal yearYield = BigDecimal.ZERO;
	
	//七日年化收益率
	private String yearYieldStr;
	// 七日年化收益日期
	private String yearYieldDt;
	// 最新收益
	private BigDecimal income = BigDecimal.ZERO;

	// 预期收益率
	private BigDecimal expectedYied;
	
	
	
	public String getYearYieldStr() {
		return yearYieldStr;
	}

	public void setYearYieldStr(String yearYieldStr) {
		this.yearYieldStr = yearYieldStr;
	}

	private String expectedYiedStr;
	// 产品期限
	private String productTerm;

	// 到期日
	private String termDate;

	// 下次派息日
	private String nextSendMomeyDay;

	// 派息金额
	private BigDecimal sendMoneyCount;

	// 币种
	private String currency;

	// 产品管理人
	private String productManager;

	// 成立日期
	private String createDate;

	// 主基金代码
	private String mainFundCode;

	public String getMainFundCode() {
		return mainFundCode;
	}

	public void setMainFundCode(String mainFundCode) {
		this.mainFundCode = mainFundCode;
	}

	public BigDecimal getExpectedYied() {
		return expectedYied;
	}

	public void setExpectedYied(BigDecimal expectedYied) {
		this.expectedYied = expectedYied;
	}

	public String getProductTerm() {
		return productTerm;
	}

	public void setProductTerm(String productTerm) {
		this.productTerm = productTerm;
	}

	public String getTermDate() {
		return termDate;
	}

	public void setTermDate(String termDate) {
		this.termDate = termDate;
	}

	public String getNextSendMomeyDay() {
		return nextSendMomeyDay;
	}

	public void setNextSendMomeyDay(String nextSendMomeyDay) {
		this.nextSendMomeyDay = nextSendMomeyDay;
	}

	public BigDecimal getSendMoneyCount() {
		return sendMoneyCount;
	}

	public void setSendMoneyCount(BigDecimal sendMoneyCount) {
		this.sendMoneyCount = sendMoneyCount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getProductManager() {
		return productManager;
	}

	public void setProductManager(String productManager) {
		this.productManager = productManager;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getFundType() {
		return this.fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}

	public String getFundCode() {
		return this.fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public BigDecimal getCurrentCost() {
		return this.currentCost;
	}

	public void setCurrentCost(BigDecimal currentCost) {
		this.currentCost = currentCost;
	}

	public BigDecimal getBalanceVol() {
		return this.balanceVol;
	}

	public void setBalanceVol(BigDecimal balanceVol) {
		this.balanceVol = balanceVol;
	}

	public BigDecimal getMarketCap() {
		return this.marketCap;
	}

	public void setMarketCap(BigDecimal marketCap) {
		this.marketCap = marketCap;
	}

	public BigDecimal getNav() {
		return this.nav;
	}

	public void setNav(BigDecimal nav) {
		this.nav = nav;
	}

	public String getNavDate() {
		return this.navDate;
	}

	public void setNavDate(String navDate) {
		this.navDate = navDate;
	}

	public BigDecimal getFundInc() {
		return this.fundInc;
	}

	public void setFundInc(BigDecimal fundInc) {
		this.fundInc = fundInc;
	}

	public BigDecimal getFloatProfit() {
		return this.floatProfit;
	}

	public void setFloatProfit(BigDecimal floatProfit) {
		this.floatProfit = floatProfit;
	}

	public BigDecimal getYearYield() {
		return this.yearYield;
	}

	public void setYearYield(BigDecimal yearYield) {
		this.yearYield = yearYield;
	}

	public String getYearYieldDt() {
		return this.yearYieldDt;
	}

	public void setYearYieldDt(String yearYieldDt) {
		this.yearYieldDt = yearYieldDt;
	}

	public BigDecimal getIncome() {
		return this.income;
	}

	public void setIncome(BigDecimal income) {
		this.income = income;
	}

	public String getFundName() {
		return this.fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getBigType() {
		return bigType;
	}

	public void setBigType(String bigType) {
		this.bigType = bigType;
	}
	
	public String getCurrentCostStr() {
		return currentCostStr;
	}

	public void setCurrentCostStr(String currentCostStr) {
		this.currentCostStr = currentCostStr;
	}

	public String getMarketCapStr() {
		return marketCapStr;
	}

	public void setMarketCapStr(String marketCapStr) {
		this.marketCapStr = marketCapStr;
	}

	public String getFundIncStr() {
		return fundIncStr;
	}

	public void setFundIncStr(String fundIncStr) {
		this.fundIncStr = fundIncStr;
	}

	public String getFloatProfitStr() {
		return floatProfitStr;
	}

	public void setFloatProfitStr(String floatProfitStr) {
		this.floatProfitStr = floatProfitStr;
	}
	
	

	public String getExpectedYiedStr() {
		return expectedYiedStr;
	}

	public void setExpectedYiedStr(String expectedYiedStr) {
		this.expectedYiedStr = expectedYiedStr;
	}

	@Override
	public String toString() {
		return "MemberCenterFundBean [fundType=" + fundType + ", bigType="
				+ bigType + ", fundCode=" + fundCode + ", fundName=" + fundName
				+ ", currentCost=" + currentCost + ", currentCostStr="
				+ currentCostStr + ", balanceVol=" + balanceVol
				+ ", marketCap=" + marketCap + ", marketCapStr=" + marketCapStr
				+ ", nav=" + nav + ", navDate=" + navDate + ", fundInc="
				+ fundInc + ", fundIncStr=" + fundIncStr + ", floatProfit="
				+ floatProfit + ", floatProfitStr=" + floatProfitStr
				+ ", yearYield=" + yearYield + ", yearYieldDt=" + yearYieldDt
				+ ", income=" + income + ", expectedYied=" + expectedYied
				+ ", productTerm=" + productTerm + ", termDate=" + termDate
				+ ", nextSendMomeyDay=" + nextSendMomeyDay
				+ ", sendMoneyCount=" + sendMoneyCount + ", currency="
				+ currency + ", productManager=" + productManager
				+ ", createDate=" + createDate + ", mainFundCode="
				+ mainFundCode + "]";
	}

}
