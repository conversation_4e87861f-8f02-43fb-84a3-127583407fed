package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class BoardAppPv implements Serializable {


    private static final long serialVersionUID = -7940629830197833066L;

	private String consCustNo; // 投顾客户号

	private String consCustName; // 客户姓名

	private String consCode; // 投顾账号

	private String consName; // 投顾姓名

    private String outletName; // 投顾部门

    private String u1Name; // 一级部门(新)

    private String u2Name; // 二级部门(新)

    private String appPid;// 页面id

    private String appPname; //页面名称

    private String appPtype;//页面类型

    private String jjdm; // 基金代码

	private String jjjc; // 基金简称

	private String favoriteDate; // 收藏日期

	private String cpfl; // 基金类型

    private String top5;//

    private String fundCode15; //

    private String fundName15;//

    private String fundCode30; //

    private String fundName30;//

    private String fundCode60; //

    private String fundName60;//

    private String searchWords; // 近30天搜索关键词

    private int searchCount=0;// 近30天关键词搜索次数

	private int pv30; // 最近30天访问量

    private int pv15; // 最近15天访问量

    private int pv14; // 最近14天访问量

    private int pv60; // 近60天访问量

    private int pv90; // 近90天访问量

	private String lastFavoriteDay;// 最近一次访问日期

    private String isHold;// 是否持有

    private String fundType;// 基金类型

    private String hmcpx;// 好买产品线

    private String navDt;// 最新净值日期

    private Double nav;// 最新净值

    private String hb1y;// 近一月业绩
    private String hb3y;// 近三月业绩
    private String hb6y;// 近半年业绩
    private String hb1n;// 近一年业绩

    private String maxDt;// 最后访问日期
    private String addDate;// 添加自选日期

    private String days30FundGap; //近30天产品页PV平均停留时长

    private String days14FundGap; //近14天产品页PV平均停留时长

    private String pvSp; //近30天产品相关视频页PV

    private String pvYb; //近30天产品相关研报页PV

    private String pvYp; //近30天产品相关音频页PV

    private String pvZx; //近30天产品相关资讯页PV

    private String txnFlag; //是否购买

    private String xsbz; //是否在销

    private String balanceVol; //持仓份额

    private String marketCap; //持仓市值

    private String balanceIncome; //当前持仓收益

    private String accumIncome; //累计收益

    private String favFlag;//当前是否收藏


    private String ackDt;// 赎回日期,申购日期

    private Double ackAmtRmb; //赎回金额,申购金额

    private Double currentSmVol;//当前私募存量

    private Double maxSmVol; // 历史最大私募存量

    private Double heGpdcAmtCur; // 当前剩余私募二级存量

    private String firstTradeDt; // 首交日期

    private String firstTradeAmt; // 首交金额

    private String lastAppDt; // 最近一次申购日期

    private double lastAppAmt;//最近一次申购金额

    private String appCount; //累计申购次数

    private double appSum;//累计申购金额

    private String lastRedemDt; // 最近一次赎回日期

    private double lastRedemAmt;//最近一次赎回金额

    private String redemCount; //累计赎回次数

    private double redemSum;//累计赎回金额

    private String lossProbably; //流失概率

    private String lossProbablyRate; //流失概率

    private String flagReason; //关注理由

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getConsCustName() {
        return consCustName;
    }

    public void setConsCustName(String consCustName) {
        this.consCustName = consCustName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getOutletName() {
        return outletName;
    }

    public void setOutletName(String outletName) {
        this.outletName = outletName;
    }

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getAppPid() {
        return appPid;
    }

    public void setAppPid(String appPid) {
        this.appPid = appPid;
    }

    public String getAppPname() {
        return appPname;
    }

    public void setAppPname(String appPname) {
        this.appPname = appPname;
    }

    public String getAppPtype() {
        return appPtype;
    }

    public void setAppPtype(String appPtype) {
        this.appPtype = appPtype;
    }

    public String getJjdm() {
        return jjdm;
    }

    public void setJjdm(String jjdm) {
        this.jjdm = jjdm;
    }

    public String getJjjc() {
        return jjjc;
    }

    public void setJjjc(String jjjc) {
        this.jjjc = jjjc;
    }

    public String getFavoriteDate() {
        return favoriteDate;
    }

    public void setFavoriteDate(String favoriteDate) {
        this.favoriteDate = favoriteDate;
    }

    public String getCpfl() {
        return cpfl;
    }

    public void setCpfl(String cpfl) {
        this.cpfl = cpfl;
    }

    public String getTop5() {
        return top5;
    }

    public void setTop5(String top5) {
        this.top5 = top5;
    }

    public String getFundCode15() {
        return fundCode15;
    }

    public void setFundCode15(String fundCode15) {
        this.fundCode15 = fundCode15;
    }

    public String getFundName15() {
        return fundName15;
    }

    public void setFundName15(String fundName15) {
        this.fundName15 = fundName15;
    }

    public String getFundCode30() {
        return fundCode30;
    }

    public void setFundCode30(String fundCode30) {
        this.fundCode30 = fundCode30;
    }

    public String getFundName30() {
        return fundName30;
    }

    public void setFundName30(String fundName30) {
        this.fundName30 = fundName30;
    }

    public String getFundCode60() {
        return fundCode60;
    }

    public void setFundCode60(String fundCode60) {
        this.fundCode60 = fundCode60;
    }

    public String getFundName60() {
        return fundName60;
    }

    public void setFundName60(String fundName60) {
        this.fundName60 = fundName60;
    }

    public String getSearchWords() {
        return searchWords;
    }

    public void setSearchWords(String searchWords) {
        this.searchWords = searchWords;
    }

    public int getSearchCount() {
        return searchCount;
    }

    public void setSearchCount(int searchCount) {
        this.searchCount = searchCount;
    }

    public int getPv30() {
        return pv30;
    }

    public void setPv30(int pv30) {
        this.pv30 = pv30;
    }

    public int getPv15() {
        return pv15;
    }

    public void setPv15(int pv15) {
        this.pv15 = pv15;
    }

    public int getPv14() {
        return pv14;
    }

    public void setPv14(int pv14) {
        this.pv14 = pv14;
    }

    public int getPv60() {
        return pv60;
    }

    public void setPv60(int pv60) {
        this.pv60 = pv60;
    }

    public int getPv90() {
        return pv90;
    }

    public void setPv90(int pv90) {
        this.pv90 = pv90;
    }

    public String getLastFavoriteDay() {
        return lastFavoriteDay;
    }

    public void setLastFavoriteDay(String lastFavoriteDay) {
        this.lastFavoriteDay = lastFavoriteDay;
    }

    public String getIsHold() {
        return isHold;
    }

    public void setIsHold(String isHold) {
        this.isHold = isHold;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getHmcpx() {
        return hmcpx;
    }

    public void setHmcpx(String hmcpx) {
        this.hmcpx = hmcpx;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public Double getNav() {
        return nav;
    }

    public void setNav(Double nav) {
        this.nav = nav;
    }

    public String getHb1y() {
        return hb1y;
    }

    public void setHb1y(String hb1y) {
        this.hb1y = hb1y;
    }

    public String getHb3y() {
        return hb3y;
    }

    public void setHb3y(String hb3y) {
        this.hb3y = hb3y;
    }

    public String getHb6y() {
        return hb6y;
    }

    public void setHb6y(String hb6y) {
        this.hb6y = hb6y;
    }

    public String getHb1n() {
        return hb1n;
    }

    public void setHb1n(String hb1n) {
        this.hb1n = hb1n;
    }

    public String getAddDate() {
        return addDate;
    }

    public void setAddDate(String addDate) {
        this.addDate = addDate;
    }

    public String getDays30FundGap() {
        return days30FundGap;
    }

    public void setDays30FundGap(String days30FundGap) {
        this.days30FundGap = days30FundGap;
    }

    public String getDays14FundGap() {
        return days14FundGap;
    }

    public void setDays14FundGap(String days14FundGap) {
        this.days14FundGap = days14FundGap;
    }

    public String getPvSp() {
        return pvSp;
    }

    public void setPvSp(String pvSp) {
        this.pvSp = pvSp;
    }

    public String getPvYb() {
        return pvYb;
    }

    public void setPvYb(String pvYb) {
        this.pvYb = pvYb;
    }

    public String getPvYp() {
        return pvYp;
    }

    public void setPvYp(String pvYp) {
        this.pvYp = pvYp;
    }

    public String getPvZx() {
        return pvZx;
    }

    public void setPvZx(String pvZx) {
        this.pvZx = pvZx;
    }

    public String getTxnFlag() {
        return txnFlag;
    }

    public void setTxnFlag(String txnFlag) {
        this.txnFlag = txnFlag;
    }

    public String getXsbz() {
        return xsbz;
    }

    public void setXsbz(String xsbz) {
        this.xsbz = xsbz;
    }

    public String getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(String balanceVol) {
        this.balanceVol = balanceVol;
    }

    public String getMarketCap() {
        return marketCap;
    }

    public void setMarketCap(String marketCap) {
        this.marketCap = marketCap;
    }

    public String getBalanceIncome() {
        return balanceIncome;
    }

    public void setBalanceIncome(String balanceIncome) {
        this.balanceIncome = balanceIncome;
    }

    public String getAccumIncome() {
        return accumIncome;
    }

    public void setAccumIncome(String accumIncome) {
        this.accumIncome = accumIncome;
    }

    public String getFavFlag() {
        return favFlag;
    }

    public void setFavFlag(String favFlag) {
        this.favFlag = favFlag;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public Double getAckAmtRmb() {
        return ackAmtRmb;
    }

    public void setAckAmtRmb(Double ackAmtRmb) {
        this.ackAmtRmb = ackAmtRmb;
    }

    public Double getCurrentSmVol() {
        return currentSmVol;
    }

    public void setCurrentSmVol(Double currentSmVol) {
        this.currentSmVol = currentSmVol;
    }

    public Double getMaxSmVol() {
        return maxSmVol;
    }

    public void setMaxSmVol(Double maxSmVol) {
        this.maxSmVol = maxSmVol;
    }

    public String getFirstTradeDt() {
        return firstTradeDt;
    }

    public void setFirstTradeDt(String firstTradeDt) {
        this.firstTradeDt = firstTradeDt;
    }

    public String getFirstTradeAmt() {
        return firstTradeAmt;
    }

    public void setFirstTradeAmt(String firstTradeAmt) {
        this.firstTradeAmt = firstTradeAmt;
    }

    public String getLastAppDt() {
        return lastAppDt;
    }

    public void setLastAppDt(String lastAppDt) {
        this.lastAppDt = lastAppDt;
    }

    public double getLastAppAmt() {
        return lastAppAmt;
    }

    public void setLastAppAmt(double lastAppAmt) {
        this.lastAppAmt = lastAppAmt;
    }

    public String getAppCount() {
        return appCount;
    }

    public void setAppCount(String appCount) {
        this.appCount = appCount;
    }

    public double getAppSum() {
        return appSum;
    }

    public void setAppSum(double appSum) {
        this.appSum = appSum;
    }

    public String getLastRedemDt() {
        return lastRedemDt;
    }

    public void setLastRedemDt(String lastRedemDt) {
        this.lastRedemDt = lastRedemDt;
    }

    public double getLastRedemAmt() {
        return lastRedemAmt;
    }

    public void setLastRedemAmt(double lastRedemAmt) {
        this.lastRedemAmt = lastRedemAmt;
    }

    public String getRedemCount() {
        return redemCount;
    }

    public void setRedemCount(String redemCount) {
        this.redemCount = redemCount;
    }

    public double getRedemSum() {
        return redemSum;
    }

    public void setRedemSum(double redemSum) {
        this.redemSum = redemSum;
    }

    public String getLossProbably() {
        return lossProbably;
    }

    public void setLossProbably(String lossProbably) {
        this.lossProbably = lossProbably;
    }

    public String getLossProbablyRate() {
        return lossProbablyRate;
    }

    public void setLossProbablyRate(String lossProbablyRate) {
        this.lossProbablyRate = lossProbablyRate;
    }

    public Double getHeGpdcAmtCur() {
        return heGpdcAmtCur;
    }

    public void setHeGpdcAmtCur(Double heGpdcAmtCur) {
        this.heGpdcAmtCur = heGpdcAmtCur;
    }

    public String getMaxDt() {
        return maxDt;
    }

    public void setMaxDt(String maxDt) {
        this.maxDt = maxDt;
    }

    public String getFlagReason() {
        return flagReason;
    }

    public void setFlagReason(String flagReason) {
        this.flagReason = flagReason;
    }
}
