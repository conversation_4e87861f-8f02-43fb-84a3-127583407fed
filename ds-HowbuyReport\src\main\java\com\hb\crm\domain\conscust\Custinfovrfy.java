package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Custinfovrfy.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Custinfovrfy implements Serializable {

private static final long serialVersionUID = 1L;

	private String custno;
	
	private String columnname;
	
	private String columnvalue;
	
	private String vrfystatus;
	
	private String vrfymode;
	
	private String vrfydt;
	
	private String uddt;
	
	private Date stimestamp;
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getColumnname() {
		return this.columnname;
	}

	public void setColumnname(String columnname) {
		this.columnname = columnname;
	}
	
	public String getColumnvalue() {
		return this.columnvalue;
	}

	public void setColumnvalue(String columnvalue) {
		this.columnvalue = columnvalue;
	}
	
	public String getVrfystatus() {
		return this.vrfystatus;
	}

	public void setVrfystatus(String vrfystatus) {
		this.vrfystatus = vrfystatus;
	}
	
	public String getVrfymode() {
		return this.vrfymode;
	}

	public void setVrfymode(String vrfymode) {
		this.vrfymode = vrfymode;
	}
	
	public String getVrfydt() {
		return this.vrfydt;
	}

	public void setVrfydt(String vrfydt) {
		this.vrfydt = vrfydt;
	}
	
	public String getUddt() {
		return this.uddt;
	}

	public void setUddt(String uddt) {
		this.uddt = uddt;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
