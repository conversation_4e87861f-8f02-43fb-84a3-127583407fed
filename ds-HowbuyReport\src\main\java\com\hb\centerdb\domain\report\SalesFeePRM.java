package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类SalesFeePRM.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class SalesFeePRM implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String id;

	private String fundCode;//基金代码
	
	private String fundName;//基金名称
	
	private Double adjustFee;//调整费率
	
	private String AccrueDate;//计提开始日期
	
	private String methodType;//尾数处理方式 0:四舍五入 1:截取
	
	private String startDate ;//开始日期
	
	private String endDate;//结束日期
	
	private String comments;//备注
	
	private String status; //记录状态 0:正常 1:删除

	private String creator; //创建人

	private String modifier; //修改人

	private String credt; //创建时间

	private String moddt; //修改时间
	

	public String getId() {
		return id;
	}


	public void setId(String id) {
		this.id = id;
	}




	public String getFundCode() {
		return fundCode;
	}


	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}


	public String getFundName() {
		return fundName;
	}


	public void setFundName(String fundName) {
		this.fundName = fundName;
	}


	public Double getAdjustFee() {
		return adjustFee;
	}


	public void setAdjustFee(Double adjustFee) {
		this.adjustFee = adjustFee;
	}


	public String getAccrueDate() {
		return AccrueDate;
	}


	public void setAccrueDate(String accrueDate) {
		AccrueDate = accrueDate;
	}


	public String getMethodType() {
		return methodType;
	}


	public void setMethodType(String methodType) {
		this.methodType = methodType;
	}


	public String getStartDate() {
		return startDate;
	}


	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}


	public String getEndDate() {
		return endDate;
	}


	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}


	public String getComments() {
		return comments;
	}


	public void setComments(String comments) {
		this.comments = comments;
	}


	public String getStatus() {
		return status;
	}


	public void setStatus(String status) {
		this.status = status;
	}


	public String getCreator() {
		return creator;
	}


	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return modifier;
	}


	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getCredt() {
		return credt;
	}


	public void setCredt(String credt) {
		this.credt = credt;
	}


	public String getModdt() {
		return moddt;
	}


	public void setModdt(String moddt) {
		this.moddt = moddt;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
