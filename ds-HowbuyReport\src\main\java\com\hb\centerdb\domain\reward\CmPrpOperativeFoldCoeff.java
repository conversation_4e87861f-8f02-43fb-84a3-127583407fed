package com.hb.centerdb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 配置运营费折标系数
 * @reason:
 * @Date: 2020/9/15 13:58
 */
public class CmPrpOperativeFoldCoeff implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;

	private String accountProductType;
	
	private String accountProductTypeVal;
	
	private BigDecimal operFoldCoeff;
	
	private String startDt;
	
	private String endDt;

	private String creator;

	private Date createTime;

	private String modor;
	
	private Date updateTime;

	public BigDecimal getId() {
		return id;
	}

	public void setId(BigDecimal id) {
		this.id = id;
	}

	public String getAccountProductType() {
		return accountProductType;
	}

	public void setAccountProductType(String accountProductType) {
		this.accountProductType = accountProductType;
	}

	public String getAccountProductTypeVal() {
		return accountProductTypeVal;
	}

	public void setAccountProductTypeVal(String accountProductTypeVal) {
		this.accountProductTypeVal = accountProductTypeVal;
	}

	public BigDecimal getOperFoldCoeff() {
		return operFoldCoeff;
	}

	public void setOperFoldCoeff(BigDecimal operFoldCoeff) {
		this.operFoldCoeff = operFoldCoeff;
	}

	public String getStartDt() {
		return startDt;
	}

	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}

	public String getEndDt() {
		return endDt;
	}

	public void setEndDt(String endDt) {
		this.endDt = endDt;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getModor() {
		return modor;
	}

	public void setModor(String modor) {
		this.modor = modor;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
