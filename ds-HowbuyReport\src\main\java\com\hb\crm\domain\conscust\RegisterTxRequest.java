/**
 * 
 */
package com.hb.crm.domain.conscust;

import java.io.Serializable;

import com.hb.crm.tools.MfDate;
import com.hb.crm.tools.PasswordUtil;

/**
 * <AUTHOR>
 * 注册用的request.
 */
public class RegisterTxRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -319725713215809748L;
	
	public static Integer PASSWDSENDTYPE_EMAIL = 0;
	
	public static Integer PASSWDSENDTYPE_MOBILE = 1;
	
	private String custUsername;
	private String password;
	private String ETxPasswrod      = "";		// 网上交易密码          
	private String CTxPassword      = "";		// 电话交易密码
	private String provCode;
	private String cityCode;
	private String email;
	private String mobile;
//	private String randomcode;
	private String knowChan;
	private String otherChan;
	
	//private String custType;
	//0代表机构客户，1代表个人客户
	private String invstType = "1";
	/**
	 * 登记日期.
	 */
	 private String regDt = new MfDate().toString(MfDate.strPatternYYYYMMDD);
	 /**
	  * 推荐人客户号.
	  */
	 private String intrCustNo = "";
	 /**
	  * 投资顾问代码.
	  */
	 private String consCode = "";
	 /**
	  * 经纪人代码.
	  */
	 private String brokerCode = "";
	 /**
	  * 活动代码.
	  */
	 private String actCode = "";
	 
//	 /**
//	  * 交易渠道.
//	  */
//	 private String tradeChan = "";
	 /**
	  * 网点代码.
	  */
	 private String outletCode = "";
	 /**
	  * 地区代码.
	  */
	 private String regionCode = "";
	 /**
	  * 客户级别.
	  */
	 private String custLevel = "";
	 
	 /**
	  * 客户状态.
	  */
	 private String custStatus = "";
	 
	 /**
	  * ip地址.
	  */
	 private String IPAddr;
	 /**
	  * 来源.
	  */
	 private String source;
	 /**
	  * 潜在客户编号.
	  */
	 private String PCustID;
	 
	 /** 即时通讯方式 -qq */
	 private String IM = "";
	    
	    /** 即时通讯方式 -Msn */
	private String msn;
	    
	   
	  

	/**
	  * 交易日期，默认取当前系统日期.
	  */
	 private String TradeDt = new MfDate().toString(MfDate.strPatternYYYYMMDD);
	 /**
	  * 身份验证方式.
	  */
	 private String vrfyMode;
	 
	 private String contractNo;
	 
	 private String txFlag;
	 /**
	  * 客户姓名.
	  */
	 private String custName;
	 /**
	  * 联系电话 .
	  */
	 private String telNo;
	 /**
	  * 地址.
	  */
	 private String addr;
    /**
     * 邮编.
     */
    private String postCode;

    /**
     * 自定义标志.
     */
    private String selfDefFlag="0";

    /**
     * 登录密码安全级别.
     */
    private String LPassWdSafeLvl;
    /**
     * 登录密码状态.
     */
  //  private String LPASSWDSTATUS;
    
    /**
     * 交易密码安全级别.
     */
    private String TPassWdSafeLvl;
    
    /**
     * 交易密码状态.
     */
    private String TPassWdStatus;
    
    /**
     * 电话密码安全级别.
     */
    private String CPassWdSafeLvl;
    /**
     * 初始密码发送方式.
     * 0：email 1：短信
     */
    private Integer passwdSendType;
    /**
     * 沟通频率.
     */
    private String visitFqcy;
    /**
     * 发展方向.
     */
    private String devDirection;
    /**
     * 来源细分地址。
     */
    private String subSource;
    /**
     * 来源细分类型.
     */
    private String subSourceType;
    
    /**********手机端注册增加字段：注册来源、手机型号、渠道***********/
  //手机端注册来源代码为1
	private String regSource;
	
	private long  productId;;
	
	private long  channelId;;
	
	private String  parPhoneModel;;
	
	private String  subPhoneModel;
	
	private String  version;
	
	public long getProductId() {
		return productId;
	}

	public void setProductId(long productId) {
		this.productId = productId;
	}

	public long getChannelId() {
		return channelId;
	}

	public void setChannelId(long channelId) {
		this.channelId = channelId;
	}

	public String getParPhoneModel() {
		return parPhoneModel;
	}

	public void setParPhoneModel(String parPhoneModel) {
		this.parPhoneModel = parPhoneModel;
	}

	public String getSubPhoneModel() {
		return subPhoneModel;
	}

	public void setSubPhoneModel(String subPhoneModel) {
		this.subPhoneModel = subPhoneModel;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getRegSource() {
		return regSource;
	}

	public void setRegSource(String regSource) {
		this.regSource = regSource;
	}

	public String getIM() {
		return IM;
	}

	public void setIM(String iM) {
		IM = iM;
	}

	public String getMsn() {
		return msn;
	}

	public void setMsn(String msn) {
		this.msn = msn;
	}

	public String getPCustID() {
		return PCustID;
	}

	public void setPCustID(String pCustID) {
		PCustID = pCustID;
	}
	
    /**
     * @return the vrfyMode
     */
	public String getVrfyMode() {
		return vrfyMode;
	}
	/**
	 * @param vrfyMode the vrfyMode to set
	 */
	public void setVrfyMode(String vrfyMode) {
		this.vrfyMode = vrfyMode;
	}
	/**
	 * @return the contractNo
	 */
	public String getContractNo() {
		return contractNo;
	}
	/**
	 * @param contractNo the contractNo to set
	 */
	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	/**
	 * @return the password
	 */
	public String getPassword() {
		return password;
	}
	/**
	 * @param password the password to set
	 */
	public void setPassword(String password) {
		this.password = password;
		if (password!=null){
        	setLPassWdSafeLvl(PasswordUtil.checkStrength(password));
        }
	}
	
	/**
	 * @return the provCode
	 */
	public String getProvCode() {
		return provCode;
	}
	/**
	 * @param provCode the provCode to set
	 */
	public void setProvCode(String provCode) {
		this.provCode = provCode;
	}
	/**
	 * @return the cityCode
	 */
	public String getCityCode() {
		return cityCode;
	}
	/**
	 * @param cityCode the cityCode to set
	 */
	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}
	/**
	 * @return the regDt
	 */
	public String getRegDt() {
		return regDt;
	}
	/**
	 * @param regDt the regDt to set
	 */
	public void setRegDt(String regDt) {
		this.regDt = regDt;
	}
	/**
	 * @return the intrCustNo
	 */
	public String getIntrCustNo() {
		return intrCustNo;
	}
	/**
	 * @param intrCustNo the intrCustNo to set
	 */
	public void setIntrCustNo(String intrCustNo) {
		this.intrCustNo = intrCustNo;
	}
	/**
	 * @return the consCode
	 */
	public String getConsCode() {
		return consCode;
	}
	/**
	 * @param consCode the consCode to set
	 */
	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}
	/**
	 * @return the brokerCode
	 */
	public String getBrokerCode() {
		return brokerCode;
	}
	/**
	 * @param brokerCode the brokerCode to set
	 */
	public void setBrokerCode(String brokerCode) {
		this.brokerCode = brokerCode;
	}
	/**
	 * @return the actCode
	 */
	public String getActCode() {
		return actCode;
	}
	/**
	 * @param actCode the actCode to set
	 */
	public void setActCode(String actCode) {
		this.actCode = actCode;
	}

//	/**
//	 * @return the tradeChan
//	 */
//	public String getTradeChan() {
//		return tradeChan;
//	}
//	/**
//	 * @param tradeChan the tradeChan to set
//	 */
//	public void setTradeChan(String tradeChan) {
//		this.tradeChan = tradeChan;
//	}
	/**
	 * @return the outletCode
	 */
	public String getOutletCode() {
		return outletCode;
	}
	/**
	 * @param outletCode the outletCode to set
	 */
	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}
	/**
	 * @return the regionCode
	 */
	public String getRegionCode() {
		return regionCode;
	}
	/**
	 * @param regionCode the regionCode to set
	 */
	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}
	/**
	 * @return the custLevel
	 */
	public String getCustLevel() {
		return custLevel;
	}
	/**
	 * @param custLevel the custLevel to set
	 */
	public void setCustLevel(String custLevel) {
		this.custLevel = custLevel;
	}
	

	/**
	 * @return the email
	 */
	public String getEmail() {
		return email;
	}
	/**
	 * @param email the email to set
	 */
	public void setEmail(String email) {
		this.email = email;
	}
	/**
	 * @return the mobile
	 */
	public String getMobile() {
		return mobile;
	}
	/**
	 * @param mobile the mobile to set
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
//	/**
//	 * @return the randomcode
//	 */
//	public String getRandomcode() {
//		return randomcode;
//	}
//	/**
//	 * @param randomcode the randomcode to set
//	 */
//	public void setRandomcode(String randomcode) {
//		this.randomcode = randomcode;
//	}
	
	/**
	 * @return the custStatus
	 */
	public String getCustStatus() {
		return custStatus;
	}
	/**
	 * @param custStatus the custStatus to set
	 */
	public void setCustStatus(String custStatus) {
		this.custStatus = custStatus;
	}
	

	/**
	 * @return the knowChan
	 */
	public String getKnowChan() {
		return knowChan;
	}
	/**
	 * @param knowChan the knowChan to set
	 */
	public void setKnowChan(String knowChan) {
		this.knowChan = knowChan;
	}
	/**
	 * @return the otherChan
	 */
	public String getOtherChan() {
		return otherChan;
	}
	/**
	 * @param otherChan the otherChan to set
	 */
	public void setOtherChan(String otherChan) {
		this.otherChan = otherChan;
	}
	/**
	 * @return the eTxPasswrod
	 */
	public String getETxPasswrod() {
		return ETxPasswrod;
	}
	/**
	 * @param txPasswrod the eTxPasswrod to set
	 */
	public void setETxPasswrod(String txPasswrod) {
		ETxPasswrod = txPasswrod;
	}
	/**
	 * @return the cTxPassword
	 */
	public String getCTxPassword() {
		return CTxPassword;
	}
	/**
	 * @param txPassword the cTxPassword to set
	 */
	public void setCTxPassword(String txPassword) {
		CTxPassword = txPassword;
	}
	/**
	 * @return the tradeDt
	 */
	public String getTradeDt() {
		return TradeDt;
	}
	/**
	 * @param tradeDt the tradeDt to set
	 */
	public void setTradeDt(String tradeDt) {
		TradeDt = tradeDt;
	}
	/**
	 * @return the iPAddr
	 */
	public String getIPAddr() {
		return IPAddr;
	}
	/**
	 * @param addr the iPAddr to set
	 */
	public void setIPAddr(String addr) {
		IPAddr = addr;
	}
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * @return the invstType
	 */
	public String getInvstType() {
		return invstType;
	}
	/**
	 * @param invstType the invstType to set
	 */
	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}
	/**
	 * @return the custUsername
	 */
	public String getCustUsername() {
		return custUsername;
	}
	/**
	 * @param custUsername the custUsername to set
	 */
	public void setCustUsername(String custUsername) {
		this.custUsername = custUsername;
	}
	/**
	 * @return the txFlag
	 */
	public String getTxFlag() {
		return txFlag;
	}
	/**
	 * @param txFlag the txFlag to set
	 */
	public void setTxFlag(String txFlag) {
		this.txFlag = txFlag;
	}
	/**
	 * @return the custName
	 */
	public String getCustName() {
		return custName;
	}
	/**
	 * @param custName the custName to set
	 */
	public void setCustName(String custName) {
		this.custName = custName;
	}
	/**
	 * @return the telNo
	 */
	public String getTelNo() {
		return telNo;
	}
	/**
	 * @param telNo the telNo to set
	 */
	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}
	/**
	 * @return the addr
	 */
	public String getAddr() {
		return addr;
	}
	/**
	 * @param addr the addr to set
	 */
	public void setAddr(String addr) {
		this.addr = addr;
	}
	/**
	 * @return the postCode
	 */
	public String getPostCode() {
		return postCode;
	}
	/**
	 * @param postCode the postCode to set
	 */
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

    /**
     * @return the selfDefFlag
     */
    public String getSelfDefFlag() {
        return selfDefFlag;
    }
    /**
     * @param selfDefFlag the selfDefFlag to set
     */
    public void setSelfDefFlag(String selfDefFlag) {
        this.selfDefFlag = selfDefFlag;
    }
    /**
     * @return the lPassWdSafeLvl
     */
    public String getLPassWdSafeLvl() {
        return LPassWdSafeLvl;
    }
    /**
     * @param passWdSafeLvl the lPassWdSafeLvl to set
     */
    public void setLPassWdSafeLvl(String passWdSafeLvl) {
        LPassWdSafeLvl = passWdSafeLvl;
    }

    /**
     * @return the tPassWdStatus
     */
    public String getTPassWdStatus() {
        return TPassWdStatus;
    }
    /**
     * @param passWdStatus the tPassWdStatus to set
     */
    public void setTPassWdStatus(String passWdStatus) {
        TPassWdStatus = passWdStatus;
    }
    /**
     * @return the passwdSendType
     */
    public Integer getPasswdSendType() {
        return passwdSendType;
    }
    /**
     * @param passwdSendType the passwdSendType to set
     */
    public void setPasswdSendType(Integer passwdSendType) {
        this.passwdSendType = passwdSendType;
    }
    /**
     * @return the tPassWdSafeLvl
     */
    public String getTPassWdSafeLvl() {
        return TPassWdSafeLvl;
    }
    /**
     * @param passWdSafeLvl the tPassWdSafeLvl to set
     */
    public void setTPassWdSafeLvl(String passWdSafeLvl) {
        TPassWdSafeLvl = passWdSafeLvl;
    }
    /**
     * @return the cPassWdSafeLvl
     */
    public String getCPassWdSafeLvl() {
        return CPassWdSafeLvl;
    }
    /**
     * @param passWdSafeLvl the cPassWdSafeLvl to set
     */
    public void setCPassWdSafeLvl(String passWdSafeLvl) {
        CPassWdSafeLvl = passWdSafeLvl;
    }
    /**
     * @return the visitFqcy
     */
    public String getVisitFqcy() {
        return visitFqcy;
    }
    /**
     * @param visitFqcy the visitFqcy to set
     */
    public void setVisitFqcy(String visitFqcy) {
        this.visitFqcy = visitFqcy;
    }
    /**
     * @return the devDirection
     */
    public String getDevDirection() {
        return devDirection;
    }
    /**
     * @param devDirection the devDirection to set
     */
    public void setDevDirection(String devDirection) {
        this.devDirection = devDirection;
    }
    /**
     * @return the subSource
     */
    public String getSubSource() {
        return subSource;
    }
    /**
     * @param subSource the subSource to set
     */
    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }
    /**
     * @return the subSourceType
     */
    public String getSubSourceType() {
        return subSourceType;
    }
    /**
     * @param subSourceType the subSourceType to set
     */
    public void setSubSourceType(String subSourceType) {
        this.subSourceType = subSourceType;
    }
   
	
}
