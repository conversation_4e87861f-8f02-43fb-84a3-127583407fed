package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: AssetAllcation.java
 * <AUTHOR>
 * @version 1.0
 * @created 20200629
 */
public class AssetAllcation implements Serializable {


    private static final long serialVersionUID = -4627072401425336137L;
    private String orgCode;
    private String orgName;
    private String u2name;//区域
    private String consCode;
	private String consName;
    private String conscustNo;//投顾客户号
    private String conscustName;//客户姓名
    private String familyCode;//家庭账户
    private String familyCodeName;//家庭账户姓名
    private String expiryDt;//截止日期
    private String hedgeAmtRmb; //对冲总市值
    private String shareAmtRmb; //股票总市值
    private String shareRightAmtRmb; //股权总市值
    private String overseasAmtRmb; //海外总市值
    private String fixedAmtRmb; //类固收总市值
    private String bondAmtRmb; //债券总市值
    private String innovativeAmtRmb; //创新产品折标总金额
	private BigDecimal allocationNum = new BigDecimal(0); // 配置数
	private BigDecimal allocationRate = new BigDecimal(0);//配置率
	private BigDecimal assetCustNum = new BigDecimal(0);//存量客户数
    private BigDecimal allocationCust1 = new BigDecimal(0); // 配置1类客户数
    private BigDecimal allocationCust2 = new BigDecimal(0);//配置2类客户数

    private BigDecimal totalCust = new BigDecimal(0);//总客户数
    private BigDecimal sendCust = new BigDecimal(0);//完成客户数
    private BigDecimal proportion = new BigDecimal(0);//完成率
    private BigDecimal finishcount = new BigDecimal(0);//完成次数
    private BigDecimal marketcap = new BigDecimal(0);//市值


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getConscustName() {
        return conscustName;
    }

    public void setConscustName(String conscustName) {
        this.conscustName = conscustName;
    }

    public String getFamilyCode() {
        return familyCode;
    }

    public void setFamilyCode(String familyCode) {
        this.familyCode = familyCode;
    }

    public String getFamilyCodeName() {
        return familyCodeName;
    }

    public void setFamilyCodeName(String familyCodeName) {
        this.familyCodeName = familyCodeName;
    }

    public String getExpiryDt() {
        return expiryDt;
    }

    public void setExpiryDt(String expiryDt) {
        this.expiryDt = expiryDt;
    }

    public String getHedgeAmtRmb() {
        return hedgeAmtRmb;
    }

    public void setHedgeAmtRmb(String hedgeAmtRmb) {
        this.hedgeAmtRmb = hedgeAmtRmb;
    }

    public String getShareAmtRmb() {
        return shareAmtRmb;
    }

    public void setShareAmtRmb(String shareAmtRmb) {
        this.shareAmtRmb = shareAmtRmb;
    }

    public String getShareRightAmtRmb() {
        return shareRightAmtRmb;
    }

    public void setShareRightAmtRmb(String shareRightAmtRmb) {
        this.shareRightAmtRmb = shareRightAmtRmb;
    }

    public String getOverseasAmtRmb() {
        return overseasAmtRmb;
    }

    public void setOverseasAmtRmb(String overseasAmtRmb) {
        this.overseasAmtRmb = overseasAmtRmb;
    }

    public String getFixedAmtRmb() {
        return fixedAmtRmb;
    }

    public void setFixedAmtRmb(String fixedAmtRmb) {
        this.fixedAmtRmb = fixedAmtRmb;
    }

    public String getBondAmtRmb() {
        return bondAmtRmb;
    }

    public void setBondAmtRmb(String bondAmtRmb) {
        this.bondAmtRmb = bondAmtRmb;
    }

    public String getInnovativeAmtRmb() {
        return innovativeAmtRmb;
    }

    public void setInnovativeAmtRmb(String innovativeAmtRmb) {
        this.innovativeAmtRmb = innovativeAmtRmb;
    }

    public BigDecimal getAllocationNum() {
        return allocationNum;
    }

    public void setAllocationNum(BigDecimal allocationNum) {
        this.allocationNum = allocationNum;
    }

    public BigDecimal getAllocationRate() {
        return allocationRate;
    }

    public void setAllocationRate(BigDecimal allocationRate) {
        this.allocationRate = allocationRate;
    }

    public BigDecimal getAssetCustNum() {
        return assetCustNum;
    }

    public void setAssetCustNum(BigDecimal assetCustNum) {
        this.assetCustNum = assetCustNum;
    }

    public BigDecimal getAllocationCust1() {
        return allocationCust1;
    }

    public void setAllocationCust1(BigDecimal allocationCust1) {
        this.allocationCust1 = allocationCust1;
    }

    public BigDecimal getAllocationCust2() {
        return allocationCust2;
    }

    public void setAllocationCust2(BigDecimal allocationCust2) {
        this.allocationCust2 = allocationCust2;
    }

    public BigDecimal getTotalCust() {
        return totalCust;
    }

    public void setTotalCust(BigDecimal totalCust) {
        this.totalCust = totalCust;
    }

    public BigDecimal getSendCust() {
        return sendCust;
    }

    public void setSendCust(BigDecimal sendCust) {
        this.sendCust = sendCust;
    }

    public BigDecimal getProportion() {
        return proportion;
    }

    public void setProportion(BigDecimal proportion) {
        this.proportion = proportion;
    }

    public BigDecimal getFinishcount() {
        return finishcount;
    }

    public void setFinishcount(BigDecimal finishcount) {
        this.finishcount = finishcount;
    }

    public BigDecimal getMarketcap() {
        return marketcap;
    }

    public void setMarketcap(BigDecimal marketcap) {
        this.marketcap = marketcap;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }
}
