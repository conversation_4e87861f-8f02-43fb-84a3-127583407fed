package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class OrgTrade implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custNo;//客户号
    private String custName;//客户姓名
    private String idNo;//证件号码
	private String investorName;
	private String txAcctNo;
	private String coopMerchantName;
	private String ackSerialNo;
	private String tradeDt;//交易日期
	private String taName;
	private String fundCode;//基金代码
	private String fundName;//基金名称
	private String fundTypeName;
	private String ackDt;
	private String busiCode;
	private Double ackAmt = 0d;
	private Double commissionFee = 0d;
	private Double bankFee = 0d;
    private Double uncollectManageFee = 0d;//应收管理费
    private Double totalUnManage = 0d;//累计应收管理费
    private Double ManageFee = 0d;//已收管理费
    private Double totalManageFee = 0d;//累计已收管理费
    private Double uncollectPerformance = 0d;//应收业绩报酬
    private Double totalUnPerformance = 0d;//累计应收业绩报酬
    private Double performanceFee = 0d;//已收业绩报酬
    private Double totalPerformanceFee = 0d;//累计已收业绩报酬

	
	
	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getInvestorName() {
		return investorName;
	}

	public void setInvestorName(String investorName) {
		this.investorName = investorName;
	}

	public String getTxAcctNo() {
		return txAcctNo;
	}

	public void setTxAcctNo(String txAcctNo) {
		this.txAcctNo = txAcctNo;
	}

	public String getCoopMerchantName() {
		return coopMerchantName;
	}

	public void setCoopMerchantName(String coopMerchantName) {
		this.coopMerchantName = coopMerchantName;
	}

	public String getAckSerialNo() {
		return ackSerialNo;
	}

	public void setAckSerialNo(String ackSerialNo) {
		this.ackSerialNo = ackSerialNo;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getTaName() {
		return taName;
	}

	public void setTaName(String taName) {
		this.taName = taName;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getFundTypeName() {
		return fundTypeName;
	}

	public void setFundTypeName(String fundTypeName) {
		this.fundTypeName = fundTypeName;
	}

	public String getAckDt() {
		return ackDt;
	}

	public void setAckDt(String ackDt) {
		this.ackDt = ackDt;
	}

	public String getBusiCode() {
		return busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public Double getAckAmt() {
		return ackAmt;
	}

	public void setAckAmt(Double ackAmt) {
		this.ackAmt = ackAmt;
	}

	public Double getCommissionFee() {
		return commissionFee;
	}

	public void setCommissionFee(Double commissionFee) {
		this.commissionFee = commissionFee;
	}

	public Double getBankFee() {
		return bankFee;
	}

	public void setBankFee(Double bankFee) {
		this.bankFee = bankFee;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public Double getUncollectManageFee() {
        return uncollectManageFee;
    }

    public void setUncollectManageFee(Double uncollectManageFee) {
        this.uncollectManageFee = uncollectManageFee;
    }

    public Double getTotalUnManage() {
        return totalUnManage;
    }

    public void setTotalUnManage(Double totalUnManage) {
        this.totalUnManage = totalUnManage;
    }

    public Double getManageFee() {
        return ManageFee;
    }

    public void setManageFee(Double manageFee) {
        ManageFee = manageFee;
    }

    public Double getTotalManageFee() {
        return totalManageFee;
    }

    public void setTotalManageFee(Double totalManageFee) {
        this.totalManageFee = totalManageFee;
    }

    public Double getUncollectPerformance() {
        return uncollectPerformance;
    }

    public void setUncollectPerformance(Double uncollectPerformance) {
        this.uncollectPerformance = uncollectPerformance;
    }

    public Double getTotalUnPerformance() {
        return totalUnPerformance;
    }

    public void setTotalUnPerformance(Double totalUnPerformance) {
        this.totalUnPerformance = totalUnPerformance;
    }

    public Double getPerformanceFee() {
        return performanceFee;
    }

    public void setPerformanceFee(Double performanceFee) {
        this.performanceFee = performanceFee;
    }

    public Double getTotalPerformanceFee() {
        return totalPerformanceFee;
    }

    public void setTotalPerformanceFee(Double totalPerformanceFee) {
        this.totalPerformanceFee = totalPerformanceFee;
    }
}
