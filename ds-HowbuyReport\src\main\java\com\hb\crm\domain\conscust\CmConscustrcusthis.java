package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类CmConscustrcusthis.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmConscustrcusthis implements Serializable {

private static final long serialVersionUID = 1L;

	private String appserialno;
	
	private String conscustrcustid;
	
	private String conscustno;
	
	private String custno;
	
	private String linkedweb;
	
	private String regdt;
	
	private String creator;
	
	private String txcode;
	
	private Date stimestamp;
	
	private String recstat;
	
	private String uddt;
	
	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}
	
	public String getConscustrcustid() {
		return this.conscustrcustid;
	}

	public void setConscustrcustid(String conscustrcustid) {
		this.conscustrcustid = conscustrcustid;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getLinkedweb() {
		return this.linkedweb;
	}

	public void setLinkedweb(String linkedweb) {
		this.linkedweb = linkedweb;
	}
	
	public String getRegdt() {
		return this.regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getTxcode() {
		return this.txcode;
	}

	public void setTxcode(String txcode) {
		this.txcode = txcode;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	
	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}
	
	public String getUddt() {
		return this.uddt;
	}

	public void setUddt(String uddt) {
		this.uddt = uddt;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
