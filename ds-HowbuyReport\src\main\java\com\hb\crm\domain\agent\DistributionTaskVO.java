package com.hb.crm.domain.agent;

public class DistributionTaskVO {

	private String userID;  //客服ID
	private int targetNum;  //目标任务数
	private String typeName;  //任务类型名称
	private int totalNum;   //已接任务总数
	private int manualNum;  //手动任务总数
	private int autoNum;  //自动任务总数
	
	private int unDispose_TotalNum;  //未处理总数
	private int aDispose_TotalNum;   //再处理总数
	private int disposed_TotalNum;   //已处理总数
	
	private int unDispose_Auto_TotalNum;  //未处理自动分配总数
	private int unDispose_Manual_TotalNum;   //未处理手动分配总数
	
	private int aDispose_Auto_TotalNum;  //再处理自动分配总数
	private int aDispose_Manual_TotalNum;  //在处理手动分配总数
	
	private int disposed_Auto_TotalNum;    //已处理自动分配总数
	private int disposed_Manual_TotalNum;  //已处理手动分配总数
	
	private String totalName;
	private String unDisposeName;
	private String aDisposeName;
	private String disposedName;
	
	public String getTotalName() {
		return this.totalNum + "/" + this.manualNum + "/" + this.autoNum;
	}

	public String getUnDisposeName() {
		return this.unDispose_TotalNum + "/" + this.unDispose_Manual_TotalNum + "/" + this.unDispose_Auto_TotalNum;
	}

	public String getaDisposeName() {
		return this.aDispose_TotalNum + "/" + this.aDispose_Manual_TotalNum + "/" + this.aDispose_Auto_TotalNum;
	}

	public String getDisposedName() {
		return this.disposed_TotalNum + "/" + this.disposed_Manual_TotalNum + "/" + this.disposed_Auto_TotalNum;
	}

	public DistributionTaskVO(){
		this.totalNum = 0;
		this.manualNum = 0;
		this.autoNum = 0;
		
		this.unDispose_TotalNum = 0;
		this.aDispose_TotalNum = 0;
		this.disposed_TotalNum = 0;
		
		this.unDispose_Auto_TotalNum = 0;
		this.unDispose_Manual_TotalNum = 0;
		
		this.aDispose_Auto_TotalNum = 0;
		this.aDispose_Manual_TotalNum = 0;
		
		this.disposed_Auto_TotalNum = 0;
		this.disposed_Manual_TotalNum = 0;
	}
	
	public String getUserID() {
		return userID;
	}
	public void setUserID(String userID) {
		this.userID = userID;
	}
	public int getTargetNum() {
		return targetNum;
	}
	public void setTargetNum(int targetNum) {
		this.targetNum = targetNum;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public int getTotalNum() {
		return totalNum;
	}
	public void setTotalNum(int totalNum) {
		this.totalNum = totalNum;
	}
	public int getManualNum() {
		return manualNum;
	}
	public void setManualNum(int manualNum) {
		this.manualNum = manualNum;
	}
	public int getAutoNum() {
		return autoNum;
	}
	public void setAutoNum(int autoNum) {
		this.autoNum = autoNum;
	}
	public int getUnDispose_TotalNum() {
		return unDispose_TotalNum;
	}
	public void setUnDispose_TotalNum(int unDispose_TotalNum) {
		this.unDispose_TotalNum = unDispose_TotalNum;
	}
	public int getaDispose_TotalNum() {
		return aDispose_TotalNum;
	}
	public void setaDispose_TotalNum(int aDispose_TotalNum) {
		this.aDispose_TotalNum = aDispose_TotalNum;
	}
	public int getDisposed_TotalNum() {
		return disposed_TotalNum;
	}
	public void setDisposed_TotalNum(int disposed_TotalNum) {
		this.disposed_TotalNum = disposed_TotalNum;
	}
	public int getUnDispose_Auto_TotalNum() {
		return unDispose_Auto_TotalNum;
	}
	public void setUnDispose_Auto_TotalNum(int unDispose_Auto_TotalNum) {
		this.unDispose_Auto_TotalNum = unDispose_Auto_TotalNum;
	}
	public int getUnDispose_Manual_TotalNum() {
		return unDispose_Manual_TotalNum;
	}
	public void setUnDispose_Manual_TotalNum(int unDispose_Manual_TotalNum) {
		this.unDispose_Manual_TotalNum = unDispose_Manual_TotalNum;
	}
	public int getaDispose_Auto_TotalNum() {
		return aDispose_Auto_TotalNum;
	}
	public void setaDispose_Auto_TotalNum(int aDispose_Auto_TotalNum) {
		this.aDispose_Auto_TotalNum = aDispose_Auto_TotalNum;
	}
	public int getaDispose_Manual_TotalNum() {
		return aDispose_Manual_TotalNum;
	}
	public void setaDispose_Manual_TotalNum(int aDispose_Manual_TotalNum) {
		this.aDispose_Manual_TotalNum = aDispose_Manual_TotalNum;
	}
	public int getDisposed_Auto_TotalNum() {
		return disposed_Auto_TotalNum;
	}
	public void setDisposed_Auto_TotalNum(int disposed_Auto_TotalNum) {
		this.disposed_Auto_TotalNum = disposed_Auto_TotalNum;
	}
	public int getDisposed_Manual_TotalNum() {
		return disposed_Manual_TotalNum;
	}
	public void setDisposed_Manual_TotalNum(int disposed_Manual_TotalNum) {
		this.disposed_Manual_TotalNum = disposed_Manual_TotalNum;
	}
	
	
	
}
