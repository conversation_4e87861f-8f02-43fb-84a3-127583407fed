package com.hb.crm.domain.conscust;

/**
 * 主要是通过sql查询出新来源对应老的三级来源
 * Created by jingya.xu on 2016/10/31.
 */
public class SourceCombination {


    //一级来源
    private String source;

    //二级来源
    private String subsource;

    //三级来源
    private String subsourcetype;




    public String getSubsourcetype() {
        return subsourcetype;
    }

    public void setSubsourcetype(String subsourcetype) {
        this.subsourcetype = subsourcetype;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSubsource() {
        return subsource;
    }

    public void setSubsource(String subsource) {
        this.subsource = subsource;
    }


}
