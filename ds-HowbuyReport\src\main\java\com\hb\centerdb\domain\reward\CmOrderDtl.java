package com.hb.centerdb.domain.reward;


import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: 存续D底层明细
 * @reason:
 * @Date: 2022/9/18 18:12
 */
public class CmOrderDtl implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;//主键
    private String dealNo;//订单号
    private String u1code;//中心
    private String u1name;//中心
    private String u2code;//区域
    private String u2name;//区域
    private String u3code;//分公司
    private String u3name;//分公司

    private String regionalTotal ;//区域总
    private String regionalTotalName ;//区域总
    private String regionalSubTotal ;//区域执行副总
    private String regionalSubTotalName ;//区域执行副总
    private String subTotal ;//分总
    private String subTotalName ;//分总
    private String empState ;//员工状态
    private String workState ;//在职状态
    private String employmentDt ;//入职日期
    private String regularDt ;//转正日期

    private String conscode;//所属投顾
    private String consname;//所属投顾
    private String custno;//公募客户号
    private String conscustno;//投顾客户号
    private String conscustname;//客户姓名
    private String fundcode;//产品代码
    private String fundname;//产品名称
    private String fundType;//产品类型
    private String rewardAccountProductType;//核算产品类型
    private BigDecimal stockFeeD;//存续d系数

    private String firstrestype;//第一来源
    private String companySourceStartDt;//公司资源计算起始日
    private String companySourceYear;//公司资源所属年份

    private BigDecimal foldCoeff;//客户折算系数
    private String isAssignVol;//是否划转存量
    private BigDecimal assignRate;//划转比例
    private BigDecimal stockFoldRate;//股权折算比例
    private BigDecimal adjustRate;//调整比例
    private BigDecimal otherRate;//其他比例

    private String balanceDt;//存续日期
    private String startMonth;
    private String endMonth;

    private String modor;//修改人

    private Double volMonthDt= 0D;//上月末时点份额
    private Double assetMonthDt= 0D;//上月末时点存量

    private Double scale= 0D;//规模(万)
    private Double assetDt1= 0D;//01月末时点存量
    private Double assetDt2= 0D;//02月末时点存量
    private Double assetDt3= 0D;//03月末时点存量
    private Double assetDt4= 0D;//04月末时点存量
    private Double assetDt5= 0D;//05月末时点存量
    private Double assetDt6= 0D;//06月末时点存量
    private Double assetDt7= 0D;//07月末时点存量
    private Double assetDt8= 0D;//08月末时点存量
    private Double assetDt9= 0D;//09月末时点存量
    private Double assetDt10= 0D;//10月末时点存量
    private Double assetDt11= 0D;//11月末时点存量
    private Double assetDt12= 0D;//12月末时点存量
    private Double yearAssetDt= 0D;//年化月末时点存量

    private String valueRange1;//0.2取值区间
    private String valueRange2;//0.4取值区间
    private String valueRange3;//0.6取值区间
    private String valueRange4;//0.8取值区间
    private String valueRange5;//1取值区间

    private String isBigV;//是否大v
    private String is20wCust;//20w客户

    private Double multiple=0d;//除倍数

    private String manageConscode;//管理层code
    private String manageConsname;//管理层名称
    private String manFlag;//1-现管理层,2-原管理层
    private String title;//职级

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getU1code() {
        return u1code;
    }

    public void setU1code(String u1code) {
        this.u1code = u1code;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2code() {
        return u2code;
    }

    public void setU2code(String u2code) {
        this.u2code = u2code;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3code() {
        return u3code;
    }

    public void setU3code(String u3code) {
        this.u3code = u3code;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getConscustname() {
        return conscustname;
    }

    public void setConscustname(String conscustname) {
        this.conscustname = conscustname;
    }

    public String getFundcode() {
        return fundcode;
    }

    public void setFundcode(String fundcode) {
        this.fundcode = fundcode;
    }

    public String getFundname() {
        return fundname;
    }

    public void setFundname(String fundname) {
        this.fundname = fundname;
    }

    public String getRewardAccountProductType() {
        return rewardAccountProductType;
    }

    public void setRewardAccountProductType(String rewardAccountProductType) {
        this.rewardAccountProductType = rewardAccountProductType;
    }

    public BigDecimal getStockFeeD() {
        return stockFeeD;
    }

    public void setStockFeeD(BigDecimal stockFeeD) {
        this.stockFeeD = stockFeeD;
    }

    public String getFirstrestype() {
        return firstrestype;
    }

    public void setFirstrestype(String firstrestype) {
        this.firstrestype = firstrestype;
    }

    public String getCompanySourceStartDt() {
        return companySourceStartDt;
    }

    public void setCompanySourceStartDt(String companySourceStartDt) {
        this.companySourceStartDt = companySourceStartDt;
    }

    public String getCompanySourceYear() {
        return companySourceYear;
    }

    public void setCompanySourceYear(String companySourceYear) {
        this.companySourceYear = companySourceYear;
    }

    public BigDecimal getFoldCoeff() {
        return foldCoeff;
    }

    public void setFoldCoeff(BigDecimal foldCoeff) {
        this.foldCoeff = foldCoeff;
    }

    public String getIsAssignVol() {
        return isAssignVol;
    }

    public void setIsAssignVol(String isAssignVol) {
        this.isAssignVol = isAssignVol;
    }

    public BigDecimal getAdjustRate() {
        return adjustRate;
    }

    public void setAdjustRate(BigDecimal adjustRate) {
        this.adjustRate = adjustRate;
    }

    public BigDecimal getAssignRate() {
        return assignRate;
    }

    public void setAssignRate(BigDecimal assignRate) {
        this.assignRate = assignRate;
    }

    public BigDecimal getStockFoldRate() {
        return stockFoldRate;
    }

    public void setStockFoldRate(BigDecimal stockFoldRate) {
        this.stockFoldRate = stockFoldRate;
    }

    public BigDecimal getOtherRate() {
        return otherRate;
    }

    public void setOtherRate(BigDecimal otherRate) {
        this.otherRate = otherRate;
    }

    public String getBalanceDt() {
        return balanceDt;
    }

    public void setBalanceDt(String balanceDt) {
        this.balanceDt = balanceDt;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public String getStartMonth() {
        return startMonth;
    }

    public void setStartMonth(String startMonth) {
        this.startMonth = startMonth;
    }

    public String getEndMonth() {
        return endMonth;
    }

    public void setEndMonth(String endMonth) {
        this.endMonth = endMonth;
    }

    public Double getAssetDt1() {
        return assetDt1;
    }

    public void setAssetDt1(Double assetDt1) {
        this.assetDt1 = assetDt1;
    }

    public Double getAssetDt2() {
        return assetDt2;
    }

    public void setAssetDt2(Double assetDt2) {
        this.assetDt2 = assetDt2;
    }

    public Double getAssetDt3() {
        return assetDt3;
    }

    public void setAssetDt3(Double assetDt3) {
        this.assetDt3 = assetDt3;
    }

    public Double getAssetDt4() {
        return assetDt4;
    }

    public void setAssetDt4(Double assetDt4) {
        this.assetDt4 = assetDt4;
    }

    public Double getAssetDt5() {
        return assetDt5;
    }

    public void setAssetDt5(Double assetDt5) {
        this.assetDt5 = assetDt5;
    }

    public Double getAssetDt6() {
        return assetDt6;
    }

    public void setAssetDt6(Double assetDt6) {
        this.assetDt6 = assetDt6;
    }

    public Double getAssetDt7() {
        return assetDt7;
    }

    public void setAssetDt7(Double assetDt7) {
        this.assetDt7 = assetDt7;
    }

    public Double getAssetDt8() {
        return assetDt8;
    }

    public void setAssetDt8(Double assetDt8) {
        this.assetDt8 = assetDt8;
    }

    public Double getAssetDt9() {
        return assetDt9;
    }

    public void setAssetDt9(Double assetDt9) {
        this.assetDt9 = assetDt9;
    }

    public Double getAssetDt10() {
        return assetDt10;
    }

    public void setAssetDt10(Double assetDt10) {
        this.assetDt10 = assetDt10;
    }

    public Double getAssetDt11() {
        return assetDt11;
    }

    public void setAssetDt11(Double assetDt11) {
        this.assetDt11 = assetDt11;
    }

    public Double getAssetDt12() {
        return assetDt12;
    }

    public void setAssetDt12(Double assetDt12) {
        this.assetDt12 = assetDt12;
    }

    public Double getYearAssetDt() {
        return yearAssetDt;
    }

    public void setYearAssetDt(Double yearAssetDt) {
        this.yearAssetDt = yearAssetDt;
    }

    public String getRegionalTotal() {
        return regionalTotal;
    }

    public void setRegionalTotal(String regionalTotal) {
        this.regionalTotal = regionalTotal;
    }

    public String getRegionalSubTotal() {
        return regionalSubTotal;
    }

    public void setRegionalSubTotal(String regionalSubTotal) {
        this.regionalSubTotal = regionalSubTotal;
    }

    public String getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(String subTotal) {
        this.subTotal = subTotal;
    }

    public String getEmpState() {
        return empState;
    }

    public void setEmpState(String empState) {
        this.empState = empState;
    }

    public String getWorkState() {
        return workState;
    }

    public void setWorkState(String workState) {
        this.workState = workState;
    }

    public String getEmploymentDt() {
        return employmentDt;
    }

    public void setEmploymentDt(String employmentDt) {
        this.employmentDt = employmentDt;
    }

    public String getRegularDt() {
        return regularDt;
    }

    public void setRegularDt(String regularDt) {
        this.regularDt = regularDt;
    }

    public String getRegionalTotalName() {
        return regionalTotalName;
    }

    public void setRegionalTotalName(String regionalTotalName) {
        this.regionalTotalName = regionalTotalName;
    }

    public String getRegionalSubTotalName() {
        return regionalSubTotalName;
    }

    public void setRegionalSubTotalName(String regionalSubTotalName) {
        this.regionalSubTotalName = regionalSubTotalName;
    }

    public String getSubTotalName() {
        return subTotalName;
    }

    public void setSubTotalName(String subTotalName) {
        this.subTotalName = subTotalName;
    }

    public Double getVolMonthDt() {
        return volMonthDt;
    }

    public void setVolMonthDt(Double volMonthDt) {
        this.volMonthDt = volMonthDt;
    }

    public Double getAssetMonthDt() {
        return assetMonthDt;
    }

    public void setAssetMonthDt(Double assetMonthDt) {
        this.assetMonthDt = assetMonthDt;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getValueRange1() {
        return valueRange1;
    }

    public void setValueRange1(String valueRange1) {
        this.valueRange1 = valueRange1;
    }

    public String getValueRange2() {
        return valueRange2;
    }

    public void setValueRange2(String valueRange2) {
        this.valueRange2 = valueRange2;
    }

    public String getValueRange3() {
        return valueRange3;
    }

    public void setValueRange3(String valueRange3) {
        this.valueRange3 = valueRange3;
    }

    public String getValueRange4() {
        return valueRange4;
    }

    public void setValueRange4(String valueRange4) {
        this.valueRange4 = valueRange4;
    }

    public String getValueRange5() {
        return valueRange5;
    }

    public void setValueRange5(String valueRange5) {
        this.valueRange5 = valueRange5;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Double getScale() {
        return scale;
    }

    public void setScale(Double scale) {
        this.scale = scale;
    }

    public String getIsBigV() {
        return isBigV;
    }

    public void setIsBigV(String isBigV) {
        this.isBigV = isBigV;
    }

    public String getIs20wCust() {
        return is20wCust;
    }

    public void setIs20wCust(String is20wCust) {
        this.is20wCust = is20wCust;
    }

    public Double getMultiple() {
        return multiple;
    }

    public void setMultiple(Double multiple) {
        this.multiple = multiple;
    }

    public String getCustno() {
        return custno;
    }

    public void setCustno(String custno) {
        this.custno = custno;
    }

    public String getManageConscode() {
        return manageConscode;
    }

    public void setManageConscode(String manageConscode) {
        this.manageConscode = manageConscode;
    }

    public String getManageConsname() {
        return manageConsname;
    }

    public void setManageConsname(String manageConsname) {
        this.manageConsname = manageConsname;
    }

    public String getManFlag() {
        return manFlag;
    }

    public void setManFlag(String manFlag) {
        this.manFlag = manFlag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
