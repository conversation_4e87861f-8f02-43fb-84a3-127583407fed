package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CsTask implements Serializable {

	private static final long serialVersionUID = 1L;

	private String userIds; // 处理人

	private String taskType; // 任务类型

	private Integer taskNum; // 处理数量数量

	private Integer handleNum; // 处理数量

	private Double handleRate = 0d; // 处理率

	private Integer callNum; // 拨打数量

	private Double callRate = 0d; // 拨打率

	private Integer linkedNum; // 接通数量

	private Double linkedRate = 0d; // 接通率

	private Integer assignNum; // 分配数量

	private Double assignRate = 0d; // 分配率

	private int noService; //空号/暂停服务客户数量

	private int existConstant; //已有投顾客户数量

	private int notHandle; //未处理的客户数量

	public String getUserIds() {
		return userIds;
	}

	public void setUserIds(String userIds) {
		this.userIds = userIds;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	public Integer getTaskNum() {
		return taskNum;
	}

	public void setTaskNum(Integer taskNum) {
		this.taskNum = taskNum;
	}

	public Integer getHandleNum() {
		return handleNum;
	}

	public void setHandleNum(Integer handleNum) {
		this.handleNum = handleNum;
	}

	public Double getHandleRate() {
		return handleRate;
	}

	public void setHandleRate(Double handleRate) {
		this.handleRate = handleRate;
	}

	public Integer getCallNum() {
		return callNum;
	}

	public void setCallNum(Integer callNum) {
		this.callNum = callNum;
	}

	public Double getCallRate() {
		return callRate;
	}

	public void setCallRate(Double callRate) {
		this.callRate = callRate;
	}

	public Integer getLinkedNum() {
		return linkedNum;
	}

	public void setLinkedNum(Integer linkedNum) {
		this.linkedNum = linkedNum;
	}

	public Double getLinkedRate() {
		return linkedRate;
	}

	public void setLinkedRate(Double linkedRate) {
		this.linkedRate = linkedRate;
	}

	public Integer getAssignNum() {
		return assignNum;
	}

	public void setAssignNum(Integer assignNum) {
		this.assignNum = assignNum;
	}

	public Double getAssignRate() {
		return assignRate;
	}

	public void setAssignRate(Double assignRate) {
		this.assignRate = assignRate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public int getNoService() {
		return noService;
	}

	public void setNoService(int noService) {
		this.noService = noService;
	}

	public int getExistConstant() {
		return existConstant;
	}

	public void setExistConstant(int existConstant) {
		this.existConstant = existConstant;
	}

	public int getNotHandle() {
		return notHandle;
	}

	public void setNotHandle(int notHandle) {
		this.notHandle = notHandle;
	}
}
