package com.hb.centerdb.domain.reward;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2020/9/11 17:59
 * @Description 
 * @Version 1.0
 */


/**
 * 配置来源系数
 */

public class CmPrpSourceCoefficientDO implements Serializable {
    /**
     * 主键id
     */
    private BigDecimal id;

    /**
     * 来源系数起点(0.2、1)
     */
    private String startPoint;

    /**
     * 交易次数(1、2、3、4、5次及以上)
     */
    private String tradeNum;

    /**
     * 来源系数
     */
    private BigDecimal sourceCoeff;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modor;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getTradeNum() {
        return tradeNum;
    }

    public void setTradeNum(String tradeNum) {
        this.tradeNum = tradeNum;
    }

    public BigDecimal getSourceCoeff() {
        return sourceCoeff;
    }

    public void setSourceCoeff(BigDecimal sourceCoeff) {
        this.sourceCoeff = sourceCoeff;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}