package com.hb.crm.domain.report.fundnavreport;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: CustRedem.java
 * <AUTHOR>
 * @version 1.0
 * @created 20200813
 */
public class SalesVolume implements Serializable {


    private static final long serialVersionUID = -8870308104753273014L;

    private String orgCode;//部门code
    private String orgName;//部门
    private String consCode;//投顾code
	private String consName;//投顾姓名
    private String conscustNo;//投顾客户号
    private String conscustName;//投顾姓名
    private String firstPayDt; // 首次打款时间
    private BigDecimal payRmb = new BigDecimal(0);//高端打款金额
    private BigDecimal payDiscountRmb = new BigDecimal(0);//高端折标销量
    private BigDecimal innovativeDiscountRmb = new BigDecimal(0);//创新折标销量

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getConscustName() {
        return conscustName;
    }

    public void setConscustName(String conscustName) {
        this.conscustName = conscustName;
    }

    public String getFirstPayDt() {
        return firstPayDt;
    }

    public void setFirstPayDt(String firstPayDt) {
        this.firstPayDt = firstPayDt;
    }

    public BigDecimal getPayRmb() {
        return payRmb;
    }

    public void setPayRmb(BigDecimal payRmb) {
        this.payRmb = payRmb;
    }

    public BigDecimal getPayDiscountRmb() {
        return payDiscountRmb;
    }

    public void setPayDiscountRmb(BigDecimal payDiscountRmb) {
        this.payDiscountRmb = payDiscountRmb;
    }

    public BigDecimal getInnovativeDiscountRmb() {
        return innovativeDiscountRmb;
    }

    public void setInnovativeDiscountRmb(BigDecimal innovativeDiscountRmb) {
        this.innovativeDiscountRmb = innovativeDiscountRmb;
    }
}
