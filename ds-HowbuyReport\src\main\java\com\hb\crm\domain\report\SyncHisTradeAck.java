package com.hb.crm.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类SyncHisTradeAck.java
 * @version 1.0
 * @created
 */
public class SyncHisTradeAck implements Serializable {

	private static final long serialVersionUID = 1L;

	private String ackSerialNo;

	private String tradeDt;

	private String taCode;

	private String fromTaFlag;

	private String recStat;

	private String invstType;

	private String busiCode;

	private String ackDt;

	private String taSerialNo;

	private String appDt;

	private String appTm;

	private String appSerialNo;

	private String seatNo;

	private String regionCode;

	private String outletCode;

	private String fundAcctNo;

	private String fundTxAcctNo;

	private String fundCode;

	private BigDecimal appVol;

	private BigDecimal appAmt;

	private String currency;

	private BigDecimal ackAmt;

	private BigDecimal ackVol;

	private BigDecimal fee;

	private BigDecimal redmFee;

	private BigDecimal stampDuty;

	private String retCode;

	private String oriTaSerialNo;

	private String tfundAcctNo;

	private String tagentNo;

	private String tregionCode;

	private String toutletCode;

	private String tfundCode;

	private String divMode;

	private String frznCause;

	private BigDecimal interest;

	private BigDecimal interestTax;

	private BigDecimal frznAmt;

	private BigDecimal frznVol;

	private String oriAppSerialNo;

	private BigDecimal unfrznAmt;

	private BigDecimal discRateOfComm;

	private String shareClass;

	private String busiSponsor;

	private BigDecimal nav;

	private String txNetNo;

	private String tshareClass;

	private String redmStat;

	private String detailFlag;

	private String regDt;

	private String retMsg;

	private String taSerialNoTotal;

	private String protocalNo;

	private String promiseFailFlag;

	private BigDecimal gainBalance;

	private BigDecimal backFee;

	private BigDecimal taFee;

	private BigDecimal income;

	private BigDecimal invseInc;

	private BigDecimal invstIncVol;

	private BigDecimal balance;

	private String custNo;

	private BigDecimal agencyFee;

	private String tradeChan;

	private BigDecimal tackVol;

	private String downloadDt;

	private String oriSubsDt;

	private BigDecimal otherFee1;

	private BigDecimal validPeriod;

	private BigDecimal feeRate;

	private String tfundTxAcctNo;

	private BigDecimal volByInterest;

	private BigDecimal tradePrice;

	private String frznDeadline;

	private BigDecimal totalTransFee;

	private BigDecimal manRealRatio;

	private BigDecimal changeFee;

	private BigDecimal recuFee;

	private BigDecimal changeAgencyFee;

	private BigDecimal recuAgencyFee;

	private BigDecimal raiseInterest;

	private BigDecimal myAgencyFee;

	private BigDecimal myChangeAgencyFee;

	private BigDecimal myRecuAgencyFee;

	private Date syncDate = new Date();

	private String depositAcct;

	private String dateOfPdSubs;

	private String transDirect;
	
	private String fundmanabbr;

	private BigDecimal dividendRatio;

	private BigDecimal tax;

	private BigDecimal targetNav;

	private BigDecimal targetTradePrice;

	private BigDecimal otherFee2;

	private String redeInAdvanceFlag;

	private String frznMethod;

	private String redeReason;

	private String varietyCodeOfPdSubs;

	private String serialNoOfPdSubs;

	private String rationType;

	private String targetTaCode;

	private String taCustNo;

	private String rationProtoNo;

	private String beginDtOfPdSubs;

	private String endDtOfPdSubs;

	private String sendDayOfPdSubs;

	private String acceptMethod;

	private String forceRedeType;

	private String takeIncomeFlag;

	private String purposeOfPdSubs;

	private BigDecimal freqOfPdSubs;

	private String pdSubsTimeUnit;

	private BigDecimal batchNumOfPdSubs;

	private String capitalMode;

	private String detailCapticalMode;

	private BigDecimal backenloadDisc;

	private BigDecimal refundAmt;

	private BigDecimal salePercent;

	private BigDecimal achievPay;

	private BigDecimal achievCompen;

	private String adjustFlag;

	private String generalTaSerialNo;

	private BigDecimal undistriMonetaryInc;

	private String undistriMonetaryIncFlag;

	private BigDecimal breachFee;

	private BigDecimal breachFeeBackToFund;

	private BigDecimal punishFee;

	private String tradingMethod;

	private String largeBuyFlag;

	private String feeCalculator;

	private String shareRegDt;

	private String largeRedmFlag;

	private String bkRedmDt;

	private BigDecimal minFee;

	private String oriAppDt;

	private String oriAckDt;

	private String brokerCode;

	private String actCode;

	private String alterDt;

	private String combNo;

	private String divDt;

	private String xrDt;

	private BigDecimal divRatio;

	private BigDecimal totalFrznVol;

	private BigDecimal transferFee;

	private Long drawBonusUnit;

	private String dividendType;

	private BigDecimal divPerUnit;

	private String lastSubsDt;

	private BigDecimal standardFee;

	private String fundManCode;

	private String fundAttrHb;

	private String fundType;
	
	private String txCode;
	
	private Integer txCount;
	
	private Integer buyCount;	 // 认申购笔数
	
	private Integer redeemCount; // 赎回笔数
	
	private BigDecimal buyamt = new BigDecimal(0); // 认申购金额

	private BigDecimal buyfee = new BigDecimal(0); // 认申购费用

	private BigDecimal buyfeeC = new BigDecimal(0); // 认申购费用-公司分成

	private BigDecimal redeemamt = new BigDecimal(0); // 赎回金额

	private BigDecimal redeemfee = new BigDecimal(0); // 赎回费用

	private BigDecimal redeemfeeC = new BigDecimal(0); // 赎回费用-公司分成

	private BigDecimal outamt = new BigDecimal(0); // 转出金额

	private BigDecimal changefee = new BigDecimal(0); // 转换费用

	private BigDecimal changefeeC = new BigDecimal(0); // 转换费用-公司分成

	private BigDecimal inamt = new BigDecimal(0); // 转入金额

	private BigDecimal recufee = new BigDecimal(0); // 转换-补差费用

	private BigDecimal recufeeC = new BigDecimal(0); // 转换-补差费用-公司分成

	private BigDecimal feecount = new BigDecimal(0); // 费用合计

	private BigDecimal feecountC = new BigDecimal(0); // 费用-公司分成合计

	public String getAckSerialNo() {
		return ackSerialNo;
	}

	public void setAckSerialNo(String ackSerialNo) {
		this.ackSerialNo = ackSerialNo;
	}

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getTaCode() {
		return taCode;
	}

	public void setTaCode(String taCode) {
		this.taCode = taCode;
	}

	public String getFromTaFlag() {
		return fromTaFlag;
	}

	public void setFromTaFlag(String fromTaFlag) {
		this.fromTaFlag = fromTaFlag;
	}

	public String getRecStat() {
		return recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}

	public String getInvstType() {
		return invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getBusiCode() {
		return busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getAckDt() {
		return ackDt;
	}

	public void setAckDt(String ackDt) {
		this.ackDt = ackDt;
	}

	public String getTaSerialNo() {
		return taSerialNo;
	}

	public void setTaSerialNo(String taSerialNo) {
		this.taSerialNo = taSerialNo;
	}

	public String getAppDt() {
		return appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getAppTm() {
		return appTm;
	}

	public void setAppTm(String appTm) {
		this.appTm = appTm;
	}

	public String getAppSerialNo() {
		return appSerialNo;
	}

	public void setAppSerialNo(String appSerialNo) {
		this.appSerialNo = appSerialNo;
	}

	public String getSeatNo() {
		return seatNo;
	}

	public void setSeatNo(String seatNo) {
		this.seatNo = seatNo;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getOutletCode() {
		return outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getFundmanabbr() {
		return fundmanabbr;
	}

	public void setFundmanabbr(String fundmanabbr) {
		this.fundmanabbr = fundmanabbr;
	}

	public String getFundAcctNo() {
		return fundAcctNo;
	}

	public void setFundAcctNo(String fundAcctNo) {
		this.fundAcctNo = fundAcctNo;
	}

	public String getFundTxAcctNo() {
		return fundTxAcctNo;
	}

	public void setFundTxAcctNo(String fundTxAcctNo) {
		this.fundTxAcctNo = fundTxAcctNo;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public BigDecimal getAppVol() {
		return appVol;
	}

	public void setAppVol(BigDecimal appVol) {
		this.appVol = appVol;
	}

	public BigDecimal getAppAmt() {
		return appAmt;
	}

	public void setAppAmt(BigDecimal appAmt) {
		this.appAmt = appAmt;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public BigDecimal getAckAmt() {
		return ackAmt;
	}

	public void setAckAmt(BigDecimal ackAmt) {
		this.ackAmt = ackAmt;
	}

	public BigDecimal getAckVol() {
		return ackVol;
	}

	public void setAckVol(BigDecimal ackVol) {
		this.ackVol = ackVol;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public BigDecimal getRedmFee() {
		return redmFee;
	}

	public void setRedmFee(BigDecimal redmFee) {
		this.redmFee = redmFee;
	}

	public BigDecimal getStampDuty() {
		return stampDuty;
	}

	public void setStampDuty(BigDecimal stampDuty) {
		this.stampDuty = stampDuty;
	}

	public String getRetCode() {
		return retCode;
	}

	public void setRetCode(String retCode) {
		this.retCode = retCode;
	}

	public String getOriTaSerialNo() {
		return oriTaSerialNo;
	}

	public void setOriTaSerialNo(String oriTaSerialNo) {
		this.oriTaSerialNo = oriTaSerialNo;
	}

	public String getTfundAcctNo() {
		return tfundAcctNo;
	}

	public void setTfundAcctNo(String tfundAcctNo) {
		this.tfundAcctNo = tfundAcctNo;
	}

	public String getTagentNo() {
		return tagentNo;
	}

	public void setTagentNo(String tagentNo) {
		this.tagentNo = tagentNo;
	}

	public String getTregionCode() {
		return tregionCode;
	}

	public void setTregionCode(String tregionCode) {
		this.tregionCode = tregionCode;
	}

	public String getToutletCode() {
		return toutletCode;
	}

	public void setToutletCode(String toutletCode) {
		this.toutletCode = toutletCode;
	}

	public String getTfundCode() {
		return tfundCode;
	}

	public void setTfundCode(String tfundCode) {
		this.tfundCode = tfundCode;
	}

	public String getDivMode() {
		return divMode;
	}

	public void setDivMode(String divMode) {
		this.divMode = divMode;
	}

	public String getFrznCause() {
		return frznCause;
	}

	public void setFrznCause(String frznCause) {
		this.frznCause = frznCause;
	}

	public BigDecimal getInterest() {
		return interest;
	}

	public void setInterest(BigDecimal interest) {
		this.interest = interest;
	}

	public BigDecimal getInterestTax() {
		return interestTax;
	}

	public void setInterestTax(BigDecimal interestTax) {
		this.interestTax = interestTax;
	}

	public BigDecimal getFrznAmt() {
		return frznAmt;
	}

	public void setFrznAmt(BigDecimal frznAmt) {
		this.frznAmt = frznAmt;
	}

	public BigDecimal getFrznVol() {
		return frznVol;
	}

	public void setFrznVol(BigDecimal frznVol) {
		this.frznVol = frznVol;
	}

	public String getOriAppSerialNo() {
		return oriAppSerialNo;
	}

	public void setOriAppSerialNo(String oriAppSerialNo) {
		this.oriAppSerialNo = oriAppSerialNo;
	}

	public BigDecimal getUnfrznAmt() {
		return unfrznAmt;
	}

	public void setUnfrznAmt(BigDecimal unfrznAmt) {
		this.unfrznAmt = unfrznAmt;
	}

	public BigDecimal getDiscRateOfComm() {
		return discRateOfComm;
	}

	public void setDiscRateOfComm(BigDecimal discRateOfComm) {
		this.discRateOfComm = discRateOfComm;
	}

	public String getShareClass() {
		return shareClass;
	}

	public void setShareClass(String shareClass) {
		this.shareClass = shareClass;
	}

	public String getBusiSponsor() {
		return busiSponsor;
	}

	public void setBusiSponsor(String busiSponsor) {
		this.busiSponsor = busiSponsor;
	}

	public BigDecimal getNav() {
		return nav;
	}

	public void setNav(BigDecimal nav) {
		this.nav = nav;
	}

	public String getTxNetNo() {
		return txNetNo;
	}

	public void setTxNetNo(String txNetNo) {
		this.txNetNo = txNetNo;
	}

	public String getTshareClass() {
		return tshareClass;
	}

	public void setTshareClass(String tshareClass) {
		this.tshareClass = tshareClass;
	}

	public String getRedmStat() {
		return redmStat;
	}

	public void setRedmStat(String redmStat) {
		this.redmStat = redmStat;
	}

	public String getDetailFlag() {
		return detailFlag;
	}

	public void setDetailFlag(String detailFlag) {
		this.detailFlag = detailFlag;
	}

	public String getRegDt() {
		return regDt;
	}

	public void setRegDt(String regDt) {
		this.regDt = regDt;
	}

	public String getRetMsg() {
		return retMsg;
	}

	public void setRetMsg(String retMsg) {
		this.retMsg = retMsg;
	}

	public String getTaSerialNoTotal() {
		return taSerialNoTotal;
	}

	public void setTaSerialNoTotal(String taSerialNoTotal) {
		this.taSerialNoTotal = taSerialNoTotal;
	}

	public String getProtocalNo() {
		return protocalNo;
	}

	public void setProtocalNo(String protocalNo) {
		this.protocalNo = protocalNo;
	}

	public String getPromiseFailFlag() {
		return promiseFailFlag;
	}

	public void setPromiseFailFlag(String promiseFailFlag) {
		this.promiseFailFlag = promiseFailFlag;
	}

	public BigDecimal getGainBalance() {
		return gainBalance;
	}

	public void setGainBalance(BigDecimal gainBalance) {
		this.gainBalance = gainBalance;
	}

	public BigDecimal getBackFee() {
		return backFee;
	}

	public void setBackFee(BigDecimal backFee) {
		this.backFee = backFee;
	}

	public BigDecimal getTaFee() {
		return taFee;
	}

	public void setTaFee(BigDecimal taFee) {
		this.taFee = taFee;
	}

	public BigDecimal getIncome() {
		return income;
	}

	public void setIncome(BigDecimal income) {
		this.income = income;
	}

	public BigDecimal getInvseInc() {
		return invseInc;
	}

	public void setInvseInc(BigDecimal invseInc) {
		this.invseInc = invseInc;
	}

	public BigDecimal getInvstIncVol() {
		return invstIncVol;
	}

	public void setInvstIncVol(BigDecimal invstIncVol) {
		this.invstIncVol = invstIncVol;
	}

	public BigDecimal getBalance() {
		return balance;
	}

	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public BigDecimal getAgencyFee() {
		return agencyFee;
	}

	public void setAgencyFee(BigDecimal agencyFee) {
		this.agencyFee = agencyFee;
	}

	public String getTradeChan() {
		return tradeChan;
	}

	public void setTradeChan(String tradeChan) {
		this.tradeChan = tradeChan;
	}

	public BigDecimal getTackVol() {
		return tackVol;
	}

	public void setTackVol(BigDecimal tackVol) {
		this.tackVol = tackVol;
	}

	public String getDownloadDt() {
		return downloadDt;
	}

	public void setDownloadDt(String downloadDt) {
		this.downloadDt = downloadDt;
	}

	public String getOriSubsDt() {
		return oriSubsDt;
	}

	public void setOriSubsDt(String oriSubsDt) {
		this.oriSubsDt = oriSubsDt;
	}

	public BigDecimal getOtherFee1() {
		return otherFee1;
	}

	public void setOtherFee1(BigDecimal otherFee1) {
		this.otherFee1 = otherFee1;
	}

	public BigDecimal getValidPeriod() {
		return validPeriod;
	}

	public void setValidPeriod(BigDecimal validPeriod) {
		this.validPeriod = validPeriod;
	}

	public BigDecimal getFeeRate() {
		return feeRate;
	}

	public void setFeeRate(BigDecimal feeRate) {
		this.feeRate = feeRate;
	}

	public String getTfundTxAcctNo() {
		return tfundTxAcctNo;
	}

	public void setTfundTxAcctNo(String tfundTxAcctNo) {
		this.tfundTxAcctNo = tfundTxAcctNo;
	}

	public BigDecimal getVolByInterest() {
		return volByInterest;
	}

	public void setVolByInterest(BigDecimal volByInterest) {
		this.volByInterest = volByInterest;
	}

	public BigDecimal getTradePrice() {
		return tradePrice;
	}

	public void setTradePrice(BigDecimal tradePrice) {
		this.tradePrice = tradePrice;
	}

	public String getFrznDeadline() {
		return frznDeadline;
	}

	public void setFrznDeadline(String frznDeadline) {
		this.frznDeadline = frznDeadline;
	}

	public BigDecimal getTotalTransFee() {
		return totalTransFee;
	}

	public void setTotalTransFee(BigDecimal totalTransFee) {
		this.totalTransFee = totalTransFee;
	}

	public BigDecimal getManRealRatio() {
		return manRealRatio;
	}

	public void setManRealRatio(BigDecimal manRealRatio) {
		this.manRealRatio = manRealRatio;
	}

	public BigDecimal getChangeFee() {
		return changeFee;
	}

	public void setChangeFee(BigDecimal changeFee) {
		this.changeFee = changeFee;
	}

	public BigDecimal getRecuFee() {
		return recuFee;
	}

	public void setRecuFee(BigDecimal recuFee) {
		this.recuFee = recuFee;
	}

	public BigDecimal getChangeAgencyFee() {
		return changeAgencyFee;
	}

	public void setChangeAgencyFee(BigDecimal changeAgencyFee) {
		this.changeAgencyFee = changeAgencyFee;
	}

	public BigDecimal getRecuAgencyFee() {
		return recuAgencyFee;
	}

	public void setRecuAgencyFee(BigDecimal recuAgencyFee) {
		this.recuAgencyFee = recuAgencyFee;
	}

	public BigDecimal getRaiseInterest() {
		return raiseInterest;
	}

	public void setRaiseInterest(BigDecimal raiseInterest) {
		this.raiseInterest = raiseInterest;
	}

	public BigDecimal getMyAgencyFee() {
		return myAgencyFee;
	}

	public void setMyAgencyFee(BigDecimal myAgencyFee) {
		this.myAgencyFee = myAgencyFee;
	}

	public BigDecimal getMyChangeAgencyFee() {
		return myChangeAgencyFee;
	}

	public void setMyChangeAgencyFee(BigDecimal myChangeAgencyFee) {
		this.myChangeAgencyFee = myChangeAgencyFee;
	}

	public BigDecimal getMyRecuAgencyFee() {
		return myRecuAgencyFee;
	}

	public void setMyRecuAgencyFee(BigDecimal myRecuAgencyFee) {
		this.myRecuAgencyFee = myRecuAgencyFee;
	}

	public Date getSyncDate() {
		return syncDate;
	}

	public void setSyncDate(Date syncDate) {
		this.syncDate = syncDate;
	}

	public String getDepositAcct() {
		return depositAcct;
	}

	public void setDepositAcct(String depositAcct) {
		this.depositAcct = depositAcct;
	}

	public String getDateOfPdSubs() {
		return dateOfPdSubs;
	}

	public void setDateOfPdSubs(String dateOfPdSubs) {
		this.dateOfPdSubs = dateOfPdSubs;
	}

	public String getTransDirect() {
		return transDirect;
	}

	public void setTransDirect(String transDirect) {
		this.transDirect = transDirect;
	}

	public BigDecimal getDividendRatio() {
		return dividendRatio;
	}

	public void setDividendRatio(BigDecimal dividendRatio) {
		this.dividendRatio = dividendRatio;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public BigDecimal getTargetNav() {
		return targetNav;
	}

	public void setTargetNav(BigDecimal targetNav) {
		this.targetNav = targetNav;
	}

	public BigDecimal getTargetTradePrice() {
		return targetTradePrice;
	}

	public void setTargetTradePrice(BigDecimal targetTradePrice) {
		this.targetTradePrice = targetTradePrice;
	}

	public BigDecimal getOtherFee2() {
		return otherFee2;
	}

	public void setOtherFee2(BigDecimal otherFee2) {
		this.otherFee2 = otherFee2;
	}

	public String getRedeInAdvanceFlag() {
		return redeInAdvanceFlag;
	}

	public void setRedeInAdvanceFlag(String redeInAdvanceFlag) {
		this.redeInAdvanceFlag = redeInAdvanceFlag;
	}

	public String getFrznMethod() {
		return frznMethod;
	}

	public void setFrznMethod(String frznMethod) {
		this.frznMethod = frznMethod;
	}

	public String getRedeReason() {
		return redeReason;
	}

	public void setRedeReason(String redeReason) {
		this.redeReason = redeReason;
	}

	public String getVarietyCodeOfPdSubs() {
		return varietyCodeOfPdSubs;
	}

	public void setVarietyCodeOfPdSubs(String varietyCodeOfPdSubs) {
		this.varietyCodeOfPdSubs = varietyCodeOfPdSubs;
	}

	public String getSerialNoOfPdSubs() {
		return serialNoOfPdSubs;
	}

	public void setSerialNoOfPdSubs(String serialNoOfPdSubs) {
		this.serialNoOfPdSubs = serialNoOfPdSubs;
	}

	public String getRationType() {
		return rationType;
	}

	public void setRationType(String rationType) {
		this.rationType = rationType;
	}

	public String getTargetTaCode() {
		return targetTaCode;
	}

	public void setTargetTaCode(String targetTaCode) {
		this.targetTaCode = targetTaCode;
	}

	public String getTaCustNo() {
		return taCustNo;
	}

	public void setTaCustNo(String taCustNo) {
		this.taCustNo = taCustNo;
	}

	public String getRationProtoNo() {
		return rationProtoNo;
	}

	public void setRationProtoNo(String rationProtoNo) {
		this.rationProtoNo = rationProtoNo;
	}

	public String getBeginDtOfPdSubs() {
		return beginDtOfPdSubs;
	}

	public void setBeginDtOfPdSubs(String beginDtOfPdSubs) {
		this.beginDtOfPdSubs = beginDtOfPdSubs;
	}

	public String getEndDtOfPdSubs() {
		return endDtOfPdSubs;
	}

	public void setEndDtOfPdSubs(String endDtOfPdSubs) {
		this.endDtOfPdSubs = endDtOfPdSubs;
	}

	public String getSendDayOfPdSubs() {
		return sendDayOfPdSubs;
	}

	public void setSendDayOfPdSubs(String sendDayOfPdSubs) {
		this.sendDayOfPdSubs = sendDayOfPdSubs;
	}

	public String getAcceptMethod() {
		return acceptMethod;
	}

	public void setAcceptMethod(String acceptMethod) {
		this.acceptMethod = acceptMethod;
	}

	public String getForceRedeType() {
		return forceRedeType;
	}

	public void setForceRedeType(String forceRedeType) {
		this.forceRedeType = forceRedeType;
	}

	public String getTakeIncomeFlag() {
		return takeIncomeFlag;
	}

	public void setTakeIncomeFlag(String takeIncomeFlag) {
		this.takeIncomeFlag = takeIncomeFlag;
	}

	public String getPurposeOfPdSubs() {
		return purposeOfPdSubs;
	}

	public void setPurposeOfPdSubs(String purposeOfPdSubs) {
		this.purposeOfPdSubs = purposeOfPdSubs;
	}

	public BigDecimal getFreqOfPdSubs() {
		return freqOfPdSubs;
	}

	public void setFreqOfPdSubs(BigDecimal freqOfPdSubs) {
		this.freqOfPdSubs = freqOfPdSubs;
	}

	public String getPdSubsTimeUnit() {
		return pdSubsTimeUnit;
	}

	public void setPdSubsTimeUnit(String pdSubsTimeUnit) {
		this.pdSubsTimeUnit = pdSubsTimeUnit;
	}

	public BigDecimal getBatchNumOfPdSubs() {
		return batchNumOfPdSubs;
	}

	public void setBatchNumOfPdSubs(BigDecimal batchNumOfPdSubs) {
		this.batchNumOfPdSubs = batchNumOfPdSubs;
	}

	public String getCapitalMode() {
		return capitalMode;
	}

	public void setCapitalMode(String capitalMode) {
		this.capitalMode = capitalMode;
	}

	public String getDetailCapticalMode() {
		return detailCapticalMode;
	}

	public void setDetailCapticalMode(String detailCapticalMode) {
		this.detailCapticalMode = detailCapticalMode;
	}

	public BigDecimal getBackenloadDisc() {
		return backenloadDisc;
	}

	public void setBackenloadDisc(BigDecimal backenloadDisc) {
		this.backenloadDisc = backenloadDisc;
	}

	public BigDecimal getRefundAmt() {
		return refundAmt;
	}

	public void setRefundAmt(BigDecimal refundAmt) {
		this.refundAmt = refundAmt;
	}

	public BigDecimal getSalePercent() {
		return salePercent;
	}

	public void setSalePercent(BigDecimal salePercent) {
		this.salePercent = salePercent;
	}

	public BigDecimal getAchievPay() {
		return achievPay;
	}

	public void setAchievPay(BigDecimal achievPay) {
		this.achievPay = achievPay;
	}

	public BigDecimal getAchievCompen() {
		return achievCompen;
	}

	public void setAchievCompen(BigDecimal achievCompen) {
		this.achievCompen = achievCompen;
	}

	public String getAdjustFlag() {
		return adjustFlag;
	}

	public void setAdjustFlag(String adjustFlag) {
		this.adjustFlag = adjustFlag;
	}

	public String getGeneralTaSerialNo() {
		return generalTaSerialNo;
	}

	public void setGeneralTaSerialNo(String generalTaSerialNo) {
		this.generalTaSerialNo = generalTaSerialNo;
	}

	public BigDecimal getUndistriMonetaryInc() {
		return undistriMonetaryInc;
	}

	public void setUndistriMonetaryInc(BigDecimal undistriMonetaryInc) {
		this.undistriMonetaryInc = undistriMonetaryInc;
	}

	public String getUndistriMonetaryIncFlag() {
		return undistriMonetaryIncFlag;
	}

	public void setUndistriMonetaryIncFlag(String undistriMonetaryIncFlag) {
		this.undistriMonetaryIncFlag = undistriMonetaryIncFlag;
	}

	public BigDecimal getBreachFee() {
		return breachFee;
	}

	public void setBreachFee(BigDecimal breachFee) {
		this.breachFee = breachFee;
	}

	public BigDecimal getBreachFeeBackToFund() {
		return breachFeeBackToFund;
	}

	public void setBreachFeeBackToFund(BigDecimal breachFeeBackToFund) {
		this.breachFeeBackToFund = breachFeeBackToFund;
	}

	public BigDecimal getPunishFee() {
		return punishFee;
	}

	public void setPunishFee(BigDecimal punishFee) {
		this.punishFee = punishFee;
	}

	public String getTradingMethod() {
		return tradingMethod;
	}

	public void setTradingMethod(String tradingMethod) {
		this.tradingMethod = tradingMethod;
	}

	public String getLargeBuyFlag() {
		return largeBuyFlag;
	}

	public void setLargeBuyFlag(String largeBuyFlag) {
		this.largeBuyFlag = largeBuyFlag;
	}

	public String getFeeCalculator() {
		return feeCalculator;
	}

	public void setFeeCalculator(String feeCalculator) {
		this.feeCalculator = feeCalculator;
	}

	public String getShareRegDt() {
		return shareRegDt;
	}

	public void setShareRegDt(String shareRegDt) {
		this.shareRegDt = shareRegDt;
	}

	public String getLargeRedmFlag() {
		return largeRedmFlag;
	}

	public void setLargeRedmFlag(String largeRedmFlag) {
		this.largeRedmFlag = largeRedmFlag;
	}

	public String getBkRedmDt() {
		return bkRedmDt;
	}

	public void setBkRedmDt(String bkRedmDt) {
		this.bkRedmDt = bkRedmDt;
	}

	public BigDecimal getMinFee() {
		return minFee;
	}

	public void setMinFee(BigDecimal minFee) {
		this.minFee = minFee;
	}

	public String getOriAppDt() {
		return oriAppDt;
	}

	public void setOriAppDt(String oriAppDt) {
		this.oriAppDt = oriAppDt;
	}

	public String getOriAckDt() {
		return oriAckDt;
	}

	public void setOriAckDt(String oriAckDt) {
		this.oriAckDt = oriAckDt;
	}

	public String getBrokerCode() {
		return brokerCode;
	}

	public void setBrokerCode(String brokerCode) {
		this.brokerCode = brokerCode;
	}

	public String getActCode() {
		return actCode;
	}

	public void setActCode(String actCode) {
		this.actCode = actCode;
	}

	public String getAlterDt() {
		return alterDt;
	}

	public void setAlterDt(String alterDt) {
		this.alterDt = alterDt;
	}

	public String getCombNo() {
		return combNo;
	}

	public void setCombNo(String combNo) {
		this.combNo = combNo;
	}

	public String getDivDt() {
		return divDt;
	}

	public void setDivDt(String divDt) {
		this.divDt = divDt;
	}

	public String getXrDt() {
		return xrDt;
	}

	public void setXrDt(String xrDt) {
		this.xrDt = xrDt;
	}

	public BigDecimal getDivRatio() {
		return divRatio;
	}

	public void setDivRatio(BigDecimal divRatio) {
		this.divRatio = divRatio;
	}

	public BigDecimal getTotalFrznVol() {
		return totalFrznVol;
	}

	public void setTotalFrznVol(BigDecimal totalFrznVol) {
		this.totalFrznVol = totalFrznVol;
	}

	public BigDecimal getTransferFee() {
		return transferFee;
	}

	public void setTransferFee(BigDecimal transferFee) {
		this.transferFee = transferFee;
	}

	public Long getDrawBonusUnit() {
		return drawBonusUnit;
	}

	public void setDrawBonusUnit(Long drawBonusUnit) {
		this.drawBonusUnit = drawBonusUnit;
	}

	public String getDividendType() {
		return dividendType;
	}

	public void setDividendType(String dividendType) {
		this.dividendType = dividendType;
	}

	public BigDecimal getDivPerUnit() {
		return divPerUnit;
	}

	public void setDivPerUnit(BigDecimal divPerUnit) {
		this.divPerUnit = divPerUnit;
	}

	public String getLastSubsDt() {
		return lastSubsDt;
	}

	public void setLastSubsDt(String lastSubsDt) {
		this.lastSubsDt = lastSubsDt;
	}

	public BigDecimal getStandardFee() {
		return standardFee;
	}

	public void setStandardFee(BigDecimal standardFee) {
		this.standardFee = standardFee;
	}

	public String getFundManCode() {
		return fundManCode;
	}

	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}

	public String getFundAttrHb() {
		return fundAttrHb;
	}

	public void setFundAttrHb(String fundAttrHb) {
		this.fundAttrHb = fundAttrHb;
	}

	public String getFundType() {
		return fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}
	
	public String getTxCode() {
		return txCode;
	}

	public void setTxCode(String txCode) {
		this.txCode = txCode;
	}

	public Integer getTxCount() {
		return txCount;
	}

	public void setTxCount(Integer txCount) {
		this.txCount = txCount;
	}

	public Integer getBuyCount() {
		return buyCount;
	}

	public void setBuyCount(Integer buyCount) {
		this.buyCount = buyCount;
	}

	public Integer getRedeemCount() {
		return redeemCount;
	}

	public void setRedeemCount(Integer redeemCount) {
		this.redeemCount = redeemCount;
	}

	public BigDecimal getBuyamt() {
		return buyamt;
	}

	public void setBuyamt(BigDecimal buyamt) {
		this.buyamt = buyamt;
	}

	public BigDecimal getBuyfee() {
		return buyfee;
	}

	public void setBuyfee(BigDecimal buyfee) {
		this.buyfee = buyfee;
	}

	public BigDecimal getBuyfeeC() {
		return buyfeeC;
	}

	public void setBuyfeeC(BigDecimal buyfeeC) {
		this.buyfeeC = buyfeeC;
	}

	public BigDecimal getRedeemamt() {
		return redeemamt;
	}

	public void setRedeemamt(BigDecimal redeemamt) {
		this.redeemamt = redeemamt;
	}

	public BigDecimal getRedeemfee() {
		return redeemfee;
	}

	public void setRedeemfee(BigDecimal redeemfee) {
		this.redeemfee = redeemfee;
	}

	public BigDecimal getRedeemfeeC() {
		return redeemfeeC;
	}

	public void setRedeemfeeC(BigDecimal redeemfeeC) {
		this.redeemfeeC = redeemfeeC;
	}

	public BigDecimal getOutamt() {
		return outamt;
	}

	public void setOutamt(BigDecimal outamt) {
		this.outamt = outamt;
	}

	public BigDecimal getChangefee() {
		return changefee;
	}

	public void setChangefee(BigDecimal changefee) {
		this.changefee = changefee;
	}

	public BigDecimal getChangefeeC() {
		return changefeeC;
	}

	public void setChangefeeC(BigDecimal changefeeC) {
		this.changefeeC = changefeeC;
	}

	public BigDecimal getInamt() {
		return inamt;
	}

	public void setInamt(BigDecimal inamt) {
		this.inamt = inamt;
	}

	public BigDecimal getRecufee() {
		return recufee;
	}

	public void setRecufee(BigDecimal recufee) {
		this.recufee = recufee;
	}

	public BigDecimal getRecufeeC() {
		return recufeeC;
	}

	public void setRecufeeC(BigDecimal recufeeC) {
		this.recufeeC = recufeeC;
	}

	public BigDecimal getFeecount() {
		return feecount;
	}

	public void setFeecount(BigDecimal feecount) {
		this.feecount = feecount;
	}

	public BigDecimal getFeecountC() {
		return feecountC;
	}

	public void setFeecountC(BigDecimal feecountC) {
		this.feecountC = feecountC;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
