package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类PlatFormLogin.java
 * <AUTHOR>
 * @version 1.0
 * @created 20170726
 */
public class PlatFormLogin implements Serializable {

	private static final long serialVersionUID = 1L;

	private String firstLoginDt; // 首次登陆掌机时间
	private String consCustNo;// 投顾客户号
	private String custName;// 客户名称
	private String consName;// 所属投顾
	private String teamCode;// 所属团队
    private String u2name;// 区域
	private String outletCode;// 所属部门
	private String firstSourceName;// 客户来源（一级）
	private String isCjName;// 是否成交
	private String sDt;// 首次分配时间
	private String firstTradeDt;// 首次成交时间
    private String regDate;// 一账通注册时间
    private String regOutletName;// 一账通注册渠道
    private String startdtLatest;// 最近分配时间


	public String getFirstLoginDt() {
		return firstLoginDt;
	}

	public void setFirstLoginDt(String firstLoginDt) {
		this.firstLoginDt = firstLoginDt;
	}

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getTeamCode() {
		return teamCode;
	}

	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}

	public String getOutletCode() {
		return outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getFirstSourceName() {
		return firstSourceName;
	}

	public void setFirstSourceName(String firstSourceName) {
		this.firstSourceName = firstSourceName;
	}

	public String getIsCjName() {
		return isCjName;
	}

	public void setIsCjName(String isCjName) {
		this.isCjName = isCjName;
	}

	public String getsDt() {
		return sDt;
	}

	public void setsDt(String sDt) {
		this.sDt = sDt;
	}

	public String getFirstTradeDt() {
		return firstTradeDt;
	}

	public void setFirstTradeDt(String firstTradeDt) {
		this.firstTradeDt = firstTradeDt;
	}

    public String getRegDate() {
        return regDate;
    }

    public void setRegDate(String regDate) {
        this.regDate = regDate;
    }

    public String getRegOutletName() {
        return regOutletName;
    }

    public void setRegOutletName(String regOutletName) {
        this.regOutletName = regOutletName;
    }

    public String getStartdtLatest() {
        return startdtLatest;
    }

    public void setStartdtLatest(String startdtLatest) {
        this.startdtLatest = startdtLatest;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
