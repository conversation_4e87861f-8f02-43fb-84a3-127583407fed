package com.hb.crm.domain.callout;

import java.io.Serializable;
/**
 *  客服任务
 * <AUTHOR>
 *
 */
public class CsTaskAssignDay implements Serializable {
	private static final long serialVersionUID = 1L;
	private long taskId;
	private long waitId;
	private String userId ;
	private int  distributeMode;
	private String distributeDate;
	private int  handleFlag;
	private String orderDate;  //预约时间
	private String handleDate;  //处理时间
	private int  calloutStatus;	//呼出状态
	
	public long getTaskId() {
		return taskId;
	}
	public void setTaskId(long taskId) {
		this.taskId = taskId;
	}
	public long getWaitId() {
		return waitId;
	}
	public void setWaitId(long waitId) {
		this.waitId = waitId;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public int getDistributeMode() {
		return distributeMode;
	}
	public void setDistributeMode(int distributeMode) {
		this.distributeMode = distributeMode;
	}
	public String getDistributeDate() {
		return distributeDate;
	}
	public void setDistributeDate(String distributeDate) {
		this.distributeDate = distributeDate;
	}
	public int getHandleFlag() {
		return handleFlag;
	}
	public void setHandleFlag(int handleFlag) {
		this.handleFlag = handleFlag;
	}
	public String getOrderDate() {
		return orderDate;
	}
	public void setOrderDate(String orderDate) {
		this.orderDate = orderDate;
	}
	public String getHandleDate() {
		return handleDate;
	}
	public void setHandleDate(String handleDate) {
		this.handleDate = handleDate;
	}
	public int getCalloutStatus() {
		return calloutStatus;
	}
	public void setCalloutStatus(int calloutStatus) {
		this.calloutStatus = calloutStatus;
	}
	
}
