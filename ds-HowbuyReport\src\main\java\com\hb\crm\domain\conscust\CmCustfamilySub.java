package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类CmCustfamilySub.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmCustfamilySub implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;
	
	private String custname;
	
	private String familycode;
	
	private String familyname;
	
	private String creator;
	
	private String credt;
	
	private String remark;
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getFamilycode() {
		return this.familycode;
	}

	public void setFamilycode(String familycode) {
		this.familycode = familycode;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getCustname() {
	return custname;
}

public void setCustname(String custname) {
	this.custname = custname;
}

public String getFamilyname() {
	return familyname;
}

public void setFamilyname(String familyname) {
	this.familyname = familyname;
}

public String getRemark() {
	return remark;
}

public void setRemark(String remark) {
	this.remark = remark;
}
}
