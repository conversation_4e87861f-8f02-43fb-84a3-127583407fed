/**
 * 
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 拜访方式.
 */
public enum VisitType {
	/**
	 * 电话
	 */
	Call("1"),
	/**
	 * 见面
	 */
	Talk("2"),
	/**
	 * 参会
	 */
	Meetting("3"),
	/**
	 * 短信
	 */
	Message("4"),
	/**
	 * 邮件
	 */
	Email("5");
	/**
	 * 来访.
	 *//*
	Welcome("0"),
	*//**
	 * 来电.
	 *//*
	CallIn("1"),
	*//**
	 * 电子邮件.
	 *//*
	Email("2"),
	*//**
	 * 短信.
	 *//*
	Sms("3"),
	*//**
	 * 上门.
	 *//*
	Visit("4"),
	*//**
	 * 去电.
	 *//*
	Tel("5")
	;*/
	private String value;

    public String getValue() {
        return value;
    }

    VisitType(String value) {
        this.value = value;
    }
 public final static Map<VisitType, String> VisitTypeEnumMAP;
    
    static{

    	VisitTypeEnumMAP = new EnumMap<VisitType, String>(VisitType.class);
    	VisitTypeEnumMAP.put(VisitType.Call,"电话");
    	VisitTypeEnumMAP.put(VisitType.Talk,"见面");
    	VisitTypeEnumMAP.put(VisitType.Meetting,"参会");
    	VisitTypeEnumMAP.put(VisitType.Message,"短信");
    	VisitTypeEnumMAP.put(VisitType.Email,"邮件");
//    	VisitTypeEnumMAP.put(VisitType.Welcome, "来访");
//    	VisitTypeEnumMAP.put(VisitType.CallIn, "来电");
//    	VisitTypeEnumMAP.put(VisitType.Email, "电子邮件");
//    	VisitTypeEnumMAP.put(VisitType.Sms, "短信");
//    	VisitTypeEnumMAP.put(VisitType.Visit, "去访");
//    	VisitTypeEnumMAP.put(VisitType.Tel, "去电");
    }
    /**
     * 跟据value返回枚举对应的key
     * 
     * @param value
     * @return NotifyErrorCode
     */
    public static VisitType getEnumMAPKey(String value) {
    	VisitType tmpKey = null;
        for (VisitType tmpEnum : VisitType.values()) {
            if (tmpEnum.value.equals(value)) {
                tmpKey = tmpEnum;
                break;
            }
        }
        return tmpKey;
    }
    /**
     * 返回VisitType对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final String value) {
        return VisitType.VisitTypeEnumMAP.get(
        		VisitType.getEnumMAPKey(value));
    }
    public static String getEnumDesc(final VisitType value) {
        return VisitType.VisitTypeEnumMAP.get(
        		value);
    }
}
