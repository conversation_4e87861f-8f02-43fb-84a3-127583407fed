package com.hb.crm.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类SyncStockTotalVO.java
 * @version 1.0
 */
public class SyncStockTotalVO implements Serializable {
	private static final long serialVersionUID = 1L;
	private String tradeDt; // 日期
	private String fundCode;// 基金代码
	private String fundType; // 基金类型
	private String fundManCode; // 基金公司
	private String fundNameCode; // 基金名称
	private BigDecimal marketAmt = new BigDecimal(0); // 基金市值
	private BigDecimal balanceVol = new BigDecimal(0); // 基金份额
	private BigDecimal custCount = new BigDecimal(0); // 客户数
	private BigDecimal marketAmtZero = new BigDecimal(0); // 金额（股票型）
	private BigDecimal custCountZero = new BigDecimal(0); // 客户数（股票型）
	private BigDecimal marketAmtOne = new BigDecimal(0); // 金额（混合型）
	private BigDecimal custCountOne = new BigDecimal(0); // 客户数（混合型）
	private BigDecimal marketAmtTwo = new BigDecimal(0); // 金额（债券型）
	private BigDecimal custCountTwo = new BigDecimal(0); // 客户数（债券型）
	private BigDecimal marketAmtThree = new BigDecimal(0); // 金额（货币型）
	private BigDecimal custCountThree = new BigDecimal(0); // 客户数（货币型）
	private BigDecimal marketAmtFour = new BigDecimal(0); // 金额（QDII）
	private BigDecimal custCountFour = new BigDecimal(0); // 客户数（QDII）
	private BigDecimal marketAmtFive = new BigDecimal(0); // 金额（封闭式）
	private BigDecimal custCountFive = new BigDecimal(0); // 客户数（封闭式）
	private BigDecimal marketAmtSix = new BigDecimal(0); // 金额（结构型）
	private BigDecimal custCountSix = new BigDecimal(0); // 客户数（结构型）
	private BigDecimal marketAmtSeven = new BigDecimal(0); // 金额（一对多）
	private BigDecimal custCountSeven = new BigDecimal(0); // 客户数（一对多）
	private BigDecimal marketAmtEight = new BigDecimal(0); // 金额（储蓄罐）
	private BigDecimal custCountEight = new BigDecimal(0); // 客户数（储蓄罐）
	private BigDecimal totalAmt = new BigDecimal(0); // 公募总计金额
	private BigDecimal totalCustCount = new BigDecimal(0); // 公募总计金额

	public String getTradeDt() {
		return tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundType() {
		return fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}

	public String getFundManCode() {
		return fundManCode;
	}

	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}

	public String getFundNameCode() {
		return fundNameCode;
	}

	public void setFundNameCode(String fundNameCode) {
		this.fundNameCode = fundNameCode;
	}

	public BigDecimal getMarketAmt() {
		return marketAmt;
	}

	public void setMarketAmt(BigDecimal marketAmt) {
		this.marketAmt = marketAmt;
	}

	public BigDecimal getBalanceVol() {
		return balanceVol;
	}

	public void setBalanceVol(BigDecimal balanceVol) {
		this.balanceVol = balanceVol;
	}

	public BigDecimal getCustCount() {
		return custCount;
	}

	public void setCustCount(BigDecimal custCount) {
		this.custCount = custCount;
	}

	public BigDecimal getMarketAmtZero() {
		return marketAmtZero;
	}

	public void setMarketAmtZero(BigDecimal marketAmtZero) {
		this.marketAmtZero = marketAmtZero;
	}

	public BigDecimal getCustCountZero() {
		return custCountZero;
	}

	public void setCustCountZero(BigDecimal custCountZero) {
		this.custCountZero = custCountZero;
	}

	public BigDecimal getMarketAmtOne() {
		return marketAmtOne;
	}

	public void setMarketAmtOne(BigDecimal marketAmtOne) {
		this.marketAmtOne = marketAmtOne;
	}

	public BigDecimal getCustCountOne() {
		return custCountOne;
	}

	public void setCustCountOne(BigDecimal custCountOne) {
		this.custCountOne = custCountOne;
	}

	public BigDecimal getMarketAmtTwo() {
		return marketAmtTwo;
	}

	public void setMarketAmtTwo(BigDecimal marketAmtTwo) {
		this.marketAmtTwo = marketAmtTwo;
	}

	public BigDecimal getCustCountTwo() {
		return custCountTwo;
	}

	public void setCustCountTwo(BigDecimal custCountTwo) {
		this.custCountTwo = custCountTwo;
	}

	public BigDecimal getMarketAmtThree() {
		return marketAmtThree;
	}

	public void setMarketAmtThree(BigDecimal marketAmtThree) {
		this.marketAmtThree = marketAmtThree;
	}

	public BigDecimal getCustCountThree() {
		return custCountThree;
	}

	public void setCustCountThree(BigDecimal custCountThree) {
		this.custCountThree = custCountThree;
	}

	public BigDecimal getMarketAmtFour() {
		return marketAmtFour;
	}

	public void setMarketAmtFour(BigDecimal marketAmtFour) {
		this.marketAmtFour = marketAmtFour;
	}

	public BigDecimal getCustCountFour() {
		return custCountFour;
	}

	public void setCustCountFour(BigDecimal custCountFour) {
		this.custCountFour = custCountFour;
	}

	public BigDecimal getMarketAmtFive() {
		return marketAmtFive;
	}

	public void setMarketAmtFive(BigDecimal marketAmtFive) {
		this.marketAmtFive = marketAmtFive;
	}

	public BigDecimal getCustCountFive() {
		return custCountFive;
	}

	public void setCustCountFive(BigDecimal custCountFive) {
		this.custCountFive = custCountFive;
	}

	public BigDecimal getMarketAmtSix() {
		return marketAmtSix;
	}

	public void setMarketAmtSix(BigDecimal marketAmtSix) {
		this.marketAmtSix = marketAmtSix;
	}

	public BigDecimal getCustCountSix() {
		return custCountSix;
	}

	public void setCustCountSix(BigDecimal custCountSix) {
		this.custCountSix = custCountSix;
	}

	public BigDecimal getMarketAmtSeven() {
		return marketAmtSeven;
	}

	public void setMarketAmtSeven(BigDecimal marketAmtSeven) {
		this.marketAmtSeven = marketAmtSeven;
	}

	public BigDecimal getCustCountSeven() {
		return custCountSeven;
	}

	public void setCustCountSeven(BigDecimal custCountSeven) {
		this.custCountSeven = custCountSeven;
	}
	
	public BigDecimal getMarketAmtEight() {
		return marketAmtEight;
	}

	public void setMarketAmtEight(BigDecimal marketAmtEight) {
		this.marketAmtEight = marketAmtEight;
	}

	public BigDecimal getCustCountEight() {
		return custCountEight;
	}

	public void setCustCountEight(BigDecimal custCountEight) {
		this.custCountEight = custCountEight;
	}

	public BigDecimal getTotalAmt() {
		return totalAmt;
	}

	public void setTotalAmt(BigDecimal totalAmt) {
		this.totalAmt = totalAmt;
	}

	public BigDecimal getTotalCustCount() {
		return totalCustCount;
	}

	public void setTotalCustCount(BigDecimal totalCustCount) {
		this.totalCustCount = totalCustCount;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
