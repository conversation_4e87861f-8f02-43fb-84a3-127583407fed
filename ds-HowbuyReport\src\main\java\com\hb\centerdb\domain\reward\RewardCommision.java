package com.hb.centerdb.domain.reward;

import java.io.Serializable;

/**
 * @Description: 实体类RewardCommision.java
 * @version 1.0
 * @created 
 */
public class RewardCommision implements Serializable {

    private static final long serialVersionUID = 1L;

    private String conscustNo; // 投顾客户号

    private String custNo;//客户号
    private String custName;//客户名称

	private String consCode;//投顾
	
	private String consName;//投顾
    private String orgCode;//部门code
    private String orgName;//部门名称

    private String u1name;//中心
    private String u2name;//区域
    private String u3name;//分公司

    private String fundtype;//产品类型
    private String fundAttr;//投资基金简称
    private String fundCode;//基金code

    private String busiCode;//费用类型code
    private String busiName;//费用类型
    private String tradeDt;//交易日期
    private String tradeType;//交易方式

    private Double ackAmtRmb=0d;//交易确认金额
    private Double commissionFee=0d;//好买费用收入
    private Double hbcost=0d;//好买实际成本

    private Double svcIncome=0d;//分配基数

    private Double commissionRate=0d;//佣金比例
    private Double commission=0d;//佣金

    private String isBigV;//是否大v
    private String is20wCust;//20w客户

    private Double multiple=0d;//除倍数

    private Double accureCommission=0d;//实际佣金

    private String startDt;//分配日期
    private String firstDictname;//一级来源
    private String secondDictname;//二级来源
    private String sourcetype;//客户类型

    private Double manageCoeff=0d;//管理系数
    private Double manageCommission= 0d;//管理系数佣金


    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getU1name() {
        return u1name;
    }

    public void setU1name(String u1name) {
        this.u1name = u1name;
    }

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getU3name() {
        return u3name;
    }

    public void setU3name(String u3name) {
        this.u3name = u3name;
    }

    public String getFundtype() {
        return fundtype;
    }

    public void setFundtype(String fundtype) {
        this.fundtype = fundtype;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getBusiName() {
        return busiName;
    }

    public void setBusiName(String busiName) {
        this.busiName = busiName;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public Double getAckAmtRmb() {
        return ackAmtRmb;
    }

    public void setAckAmtRmb(Double ackAmtRmb) {
        this.ackAmtRmb = ackAmtRmb;
    }

    public Double getCommissionFee() {
        return commissionFee;
    }

    public void setCommissionFee(Double commissionFee) {
        this.commissionFee = commissionFee;
    }

    public Double getHbcost() {
        return hbcost;
    }

    public void setHbcost(Double hbcost) {
        this.hbcost = hbcost;
    }

    public Double getSvcIncome() {
        return svcIncome;
    }

    public void setSvcIncome(Double svcIncome) {
        this.svcIncome = svcIncome;
    }

    public Double getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(Double commissionRate) {
        this.commissionRate = commissionRate;
    }

    public Double getCommission() {
        return commission;
    }

    public void setCommission(Double commission) {
        this.commission = commission;
    }

    public String getIsBigV() {
        return isBigV;
    }

    public void setIsBigV(String isBigV) {
        this.isBigV = isBigV;
    }

    public String getIs20wCust() {
        return is20wCust;
    }

    public void setIs20wCust(String is20wCust) {
        this.is20wCust = is20wCust;
    }

    public Double getMultiple() {
        return multiple;
    }

    public void setMultiple(Double multiple) {
        this.multiple = multiple;
    }

    public Double getAccureCommission() {
        return accureCommission;
    }

    public void setAccureCommission(Double accureCommission) {
        this.accureCommission = accureCommission;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getFirstDictname() {
        return firstDictname;
    }

    public void setFirstDictname(String firstDictname) {
        this.firstDictname = firstDictname;
    }

    public String getSecondDictname() {
        return secondDictname;
    }

    public void setSecondDictname(String secondDictname) {
        this.secondDictname = secondDictname;
    }

    public String getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(String sourcetype) {
        this.sourcetype = sourcetype;
    }

    public Double getManageCoeff() {
        return manageCoeff;
    }

    public void setManageCoeff(Double manageCoeff) {
        this.manageCoeff = manageCoeff;
    }

    public Double getManageCommission() {
        return manageCommission;
    }

    public void setManageCommission(Double manageCommission) {
        this.manageCommission = manageCommission;
    }
}
