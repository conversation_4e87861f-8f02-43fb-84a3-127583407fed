package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类IncomeSettle.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class IncomeSettle implements Serializable {

private static final long serialVersionUID = 1L;

	
	private String fundManCode;//基金公司代码
	
	private String fundManName;//基金公司名称
	
	private String fundCode;//基金代码
	
	private String fundName;//基金名称
	
	private String fundType;//基金类型
	
	private String fundTypeName;//基金类型名称
	
	private String incomeType;//收入类目
	
	private Double incomeAmt=0d;//金额
	
	private String startDate ; //开始日期
	
	private String endDate ;//结束日期
	
	private String sort; //排序字段
	
	
	public String getFundManCode() {
		return fundManCode;
	}




	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}





	public String getIncomeType() {
		return incomeType;
	}




	public void setIncomeType(String incomeType) {
		this.incomeType = incomeType;
	}




	public String getFundManName() {
		return fundManName;
	}





	public String getStartDate() {
		return startDate;
	}




	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}




	public String getEndDate() {
		return endDate;
	}




	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}




	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}





	public String getFundCode() {
		return fundCode;
	}





	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}





	public String getFundName() {
		return fundName;
	}





	public void setFundName(String fundName) {
		this.fundName = fundName;
	}





	public String getFundType() {
		return fundType;
	}





	public void setFundType(String fundType) {
		this.fundType = fundType;
	}





	public String getFundTypeName() {
		return fundTypeName;
	}





	public void setFundTypeName(String fundTypeName) {
		this.fundTypeName = fundTypeName;
	}




	public Double getIncomeAmt() {
		return incomeAmt;
	}




	public void setIncomeAmt(Double incomeAmt) {
		this.incomeAmt = incomeAmt;
	}




	public String getSort() {
		return sort;
	}




	public void setSort(String sort) {
		this.sort = sort;
	}




	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
