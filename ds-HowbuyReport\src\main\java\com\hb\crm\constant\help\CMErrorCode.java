/**
 * 
 */
package com.hb.crm.constant.help;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public enum CMErrorCode {
	/**
	 * 没有申请验证.
	 */
	VerifyNoApply("3002"),

	/**
	 * 验证码错误.
	 */
	VerifyCodeError("3003"),
	/**
	 * 少于手机的重发时间间隔.
	 */
	LessthanMobileResendInterval("3010"),
	/**
	 * 少于email的重发时间间隔.
	 */
	LessthanEmaiResendInterval("3011"),
	/**
	 * 超过手机的重发次数.
	 */
	OverMobileResendTimes("3012"),
	/**
	 * 超过email的重发次数.
	 */
	OverEmailResendTimes("3013"),
	/**
	 * 超过email有效期.
	 */
	OverEmailValidateTime("3014"),
	/**
	 * 超过mobile有效期.
	 */
	OverMobilelValidateTime("3015"),
	/**
	 * 超过手机最多允许错误次数.
	 */
	OverMobileMaxErrorTimes("3016"),
	/**
	 * 没有对此email申请校验的权限.
	 */
	NoRegisterEmail("3017"),
	/**
	 * 没有对此mobile申请校验的权限.
	 */
	NoRegisterMobile("3018"),
	/**
	 * 此email已经申请验证.
	 */
	EmailExistsApplyVerify("3019"),
	/**
	 * 已经校验过了.
	 */
	EmailHasVerify("3020"),
	/**
	 * 此手机已经申请验证.
	 */
	MobileExistsApplyVerify("3021"),
	/**
	 * 手机已经校验过了.
	 */
	MobileHasVerify("3022"),
	/**
	 * 没有申请找回密码.
	 */
	GetLoginPwdNoApply("3023"),
	/**
	 * 已经存在申请通过email找回密码.
	 */
	GetLoginPwdExistsApplyByEmail("3024"),
	/**
	 * 已经存在申请通过mobile找回密码.
	 */
	GetLoginPwdExistsApplyByMobile("3025"),
	/**
	 * 证件号码已经存在.
	 */
	IDTypeAndNoExists("3026"),
	/**
	 * 客户号不存在.
	 */
	CustNoExists("3027"),
	/**
	 * 没有申请找回密码.
	 */
	FindPasswdUnApply("3028"),
	/**
	 * 用户不存在.
	 */
	UserNotExists("3030"),
	/**
	 * 用户密码错误.
	 */
	UserPasswdError("3031"),
	/**
	 * 不支持此交易渠道.
	 */
	UnSpportTradeChan("3032"),
	/**
	 * 激活验证码错误.
	 */
	VrfyCodeError("3033"),
	/**
	 * 客户已经激活
	 */
	CustHasActivate("3034"),
	/**
	 * 修改后的mobile与原mobile相同.
	 */
	ChangeMobileEqual("3035"),
	/**
	 * 修改后的email与原email相同.
	 */
	ChangeEmailEqual("3035"),
	/**
	 * 客户用户名存在.
	 */
	CustUserNameExist("3036"),
	/**
	 * 客户交易信息不存在.
	 */
	CustTxNotExists("3037"),
	
	/**
	 * 密码修改失败.
	 */
	ChangePasswdError("3038"),
	/**
	 * 设置密码错误.
	 */
	SetPasswdError("3039"),
	
	/**
	 * 交易账户已经成功激活过，不能重复激活.
	 */
	CustTradeHasActive("3040"),
	
	/**
	 * 客户网上用户没有激活.
	 */
	CustNoActivate("3041"),
	/**
	 * 客户已经申请手机验证.
	 */
	CustHadAppMobileVrfy("3042"),
	/**
     * 客户已经申请Email验证.
     */
	CustHadAppEmailVrfy("3043"),
	/**
	 * 交易账户没有激活.
	 */
	CustTradeNotActive("3044"),
	
	/**
	 * 您的帐号未设置安全问题
	 */
	CustAQUnset("3045"),
	;
	
	
	private String value;

    public String getValue() {
        return value;
    }

    CMErrorCode(String value) {
        this.value = value;
    }
public final static Map<CMErrorCode, String> CMErrorCodeEnumMAP;
    
    static{

    	CMErrorCodeEnumMAP = new EnumMap<CMErrorCode, String>(CMErrorCode.class);
    	CMErrorCodeEnumMAP.put(CMErrorCode.VerifyNoApply, "没有申请验证");
    	CMErrorCodeEnumMAP.put(CMErrorCode.VerifyCodeError, "email验证链接无效");
    	CMErrorCodeEnumMAP.put(CMErrorCode.LessthanMobileResendInterval, "少于手机的重发时间间隔");
    	CMErrorCodeEnumMAP.put(CMErrorCode.LessthanEmaiResendInterval, "少于email的重发时间间隔");
    	CMErrorCodeEnumMAP.put(CMErrorCode.OverMobileResendTimes, "超过手机的重发次数");
    	CMErrorCodeEnumMAP.put(CMErrorCode.OverEmailResendTimes, "超过email的重发次数");
    	CMErrorCodeEnumMAP.put(CMErrorCode.OverEmailValidateTime, "超过email有效期");
    	CMErrorCodeEnumMAP.put(CMErrorCode.OverMobilelValidateTime, "超过mobile有效期");
    	CMErrorCodeEnumMAP.put(CMErrorCode.OverMobileMaxErrorTimes, "超过手机最多允许错误次数");
    	
    	CMErrorCodeEnumMAP.put(CMErrorCode.NoRegisterEmail, "没有对此email申请校验的权限");
    	CMErrorCodeEnumMAP.put(CMErrorCode.NoRegisterMobile, "没有对此mobile申请校验的权限");
    	CMErrorCodeEnumMAP.put(CMErrorCode.MobileExistsApplyVerify, "此手机已经申请验证");
    	CMErrorCodeEnumMAP.put(CMErrorCode.MobileHasVerify, "手机已经校验过了");
    	CMErrorCodeEnumMAP.put(CMErrorCode.EmailExistsApplyVerify, "此email已经申请验证");
    	CMErrorCodeEnumMAP.put(CMErrorCode.EmailHasVerify, "已经校验过了");
    	CMErrorCodeEnumMAP.put(CMErrorCode.GetLoginPwdExistsApplyByEmail, "已经存在申请通过email找回密码");
    	CMErrorCodeEnumMAP.put(CMErrorCode.GetLoginPwdExistsApplyByMobile, "已经存在申请通过mobile找回密码");
    	CMErrorCodeEnumMAP.put(CMErrorCode.GetLoginPwdNoApply, "没有申请找回密码或者链接已经失效");
    	CMErrorCodeEnumMAP.put(CMErrorCode.IDTypeAndNoExists, "证件号码已经存在");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustNoExists, "客户号不存在");
    	CMErrorCodeEnumMAP.put(CMErrorCode.UserNotExists, "用户不存在");
    	CMErrorCodeEnumMAP.put(CMErrorCode.UserPasswdError, "用户密码错误");
    	CMErrorCodeEnumMAP.put(CMErrorCode.UnSpportTradeChan, "不支持此交易渠道");
    	CMErrorCodeEnumMAP.put(CMErrorCode.VrfyCodeError, "激活验证码错误");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustHasActivate, "客户已经激活");
    	CMErrorCodeEnumMAP.put(CMErrorCode.ChangeMobileEqual, "修改后的mobile与原mobile相同");
    	CMErrorCodeEnumMAP.put(CMErrorCode.ChangeEmailEqual, "修改后的email与原email相同");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustUserNameExist, "客户用户名存在");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustTxNotExists, "客户交易信息不存在");
    	CMErrorCodeEnumMAP.put(CMErrorCode.ChangePasswdError, "密码修改失败");
    	CMErrorCodeEnumMAP.put(CMErrorCode.SetPasswdError, "设置密码错误");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustTradeHasActive, "交易账户已经成功激活过，不能重复激活");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustNoActivate, "客户网上用户没有激活");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustHadAppMobileVrfy, "客户已经申请手机验证");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustHadAppEmailVrfy, "客户已经申请Email验证");
    	CMErrorCodeEnumMAP.put(CMErrorCode.CustAQUnset, "您的帐号未设置安全问题");
    }
    /**
     * 跟据value返回枚举对应的key
     * 
     * @param value
     * @return NotifyErrorCode
     */
    public static CMErrorCode getEnumMAPKey(String value) {
    	CMErrorCode tmpKey = null;
        for (CMErrorCode tmpEnum : CMErrorCode.values()) {
            if (tmpEnum.value.equals(value)) {
                tmpKey = tmpEnum;
                break;
            }
        }
        return tmpKey;
    }
    /**
     * 返回NotifyErrorCode对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final String value) {
        return CMErrorCode.CMErrorCodeEnumMAP.get(
        		CMErrorCode.getEnumMAPKey(value));
    }
    public static String getEnumDesc(final CMErrorCode value) {
        return CMErrorCode.CMErrorCodeEnumMAP.get(value);
    }
    public static void main(String[] args ) {
    	System.out.println(CMErrorCode
							.getEnumDesc(CMErrorCode.GetLoginPwdNoApply
									));
    }
}
