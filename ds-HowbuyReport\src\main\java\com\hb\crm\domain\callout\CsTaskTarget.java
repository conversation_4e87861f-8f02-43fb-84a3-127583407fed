package com.hb.crm.domain.callout;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 客户任务指标
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CsTaskTarget implements Serializable {

private static final long serialVersionUID = 1L;

	private Integer serialNo;
	
	private String conscode;
	
	private Integer targetCount;
	
	private String recStat;
	
	private String creator;
	
	private String credt;
	
	private String modifier;
	
	private String moddt;
	
	public Integer getSerialNo() {
		return this.serialNo;
	}

	public void setSerialNo(Integer serialNo) {
		this.serialNo = serialNo;
	}
	
	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	
	public Integer getTargetCount() {
		return this.targetCount;
	}

	public void setTargetCount(Integer targetCount) {
		this.targetCount = targetCount;
	}
	
	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}
	
    public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCredt() {
		return credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModdt() {
		return moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
