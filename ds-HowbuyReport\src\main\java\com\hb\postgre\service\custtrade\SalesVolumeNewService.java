package com.hb.postgre.service.custtrade;

import com.hb.centerdb.domain.report.GdTurnover;
import com.hb.crm.domain.report.fundnavreport.SalesVolume;
import com.hb.crm.tools.PageData;
import com.hb.postgre.domain.trade.CustTradeNew;
import com.hb.postgre.domain.trade.DmGmNetIncreaseSum;
import com.hb.postgre.domain.trade.GdTurnoverNew;

import java.util.List;
import java.util.Map;

public interface SalesVolumeNewService {

	/**
	 * 营销季报表
	 * @param param
	 * @return
	 */
	public List<SalesVolume> listSalesVolumeReport(Map<String, String> param);

    /**
     * 营销季报表分页
     * @param param
     * @return
     */
    public PageData<SalesVolume> listSalesVolumeReportByPage(Map<String, String> param);

    /**
     * 净申购报表
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listNetAppIncreaseReport(Map<String,String> param);

    /**
     * 公募净申购报表
     * @param param
     * @return
     */
    public List<DmGmNetIncreaseSum> listPubFundNetAppIncreaseReport(Map<String,String> param);

    /**
     * 公募转托管报表
     * @param param
     * @return
     */
    public List<Map<String,Object>> listPubFundTransferReport(Map<String,String> param);

    /**
     * 公募净申购明细报表
     * @param param
     * @return
     */
    public PageData<CustTradeNew> listPubFundNetAppIncreaseDtlReportByPage(Map<String, String> param);
    public List<CustTradeNew> listPubFundNetAppIncreaseDtlReport(Map<String, String> param);

    /**
     * 公募净申购明细报表总数查询
     * @param param
     * @return
     */
    public long countPubFundNetAppIncreaseDtlReport(Map<String, String> param);

    /**
     * 公募转托管明细报表
     * @param param
     * @return
     */
    public PageData<CustTradeNew> listPubFundTransferReportDtlReportByPage(Map<String, String> param);
    public List<CustTradeNew> listPubFundTransferReportDtlReport(Map<String, String> param);


    /**
     * 净申购投顾报表
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listConscodeNetAppIncreaseReport(Map<String,String> param);


    /**
     * @Description:净申购投顾 客户明细报表 分页
     * @param Map
     * @return List<Map<String,Object>>
     */
    public PageData<GdTurnoverNew> listCustConscodeNetIncreaseDtlReportByPage(Map<String, String> param);

    /**
     * 净申购投顾报表 客户明细
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listCustConscodeNetIncreaseDtlReport(Map<String,String> param);

    /**
     * @Description:净申购投顾 产品 明细报表 分页
     * @param Map
     * @return List<Map<String,Object>>
     */
    public PageData<GdTurnoverNew> listCustConscodeNetIncreaseProductDtlReportByPage(Map<String, String> param);

    /**
     * 净申购投顾报表 产品明细
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listCustConscodeNetIncreaseProductDtlReport(Map<String,String> param);

    /**
     * @Description:净申购投顾 交易明细报表 分页
     * @param Map
     * @return List<Map<String,Object>>
     */
    public PageData<GdTurnoverNew> listCustConscodeNetIncreaseTradeDtlReportByPage(Map<String, String> param);

    /**
     * 净申购投顾报表 交易明细
     * @param param
     * @return
     */
    public List<GdTurnoverNew> listCustConscodeNetIncreaseTradeDtlReport(Map<String,String> param);
	
}
