package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Vipverify.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Vipverify implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;
	
	private String verifycode;
	
	private String verifystatus;
	
	private String credt;
	
	private String activedt;
	
	private String activetm;
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getVerifycode() {
		return this.verifycode;
	}

	public void setVerifycode(String verifycode) {
		this.verifycode = verifycode;
	}
	
	public String getVerifystatus() {
		return this.verifystatus;
	}

	public void setVerifystatus(String verifystatus) {
		this.verifystatus = verifystatus;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getActivedt() {
		return this.activedt;
	}

	public void setActivedt(String activedt) {
		this.activedt = activedt;
	}
	
	public String getActivetm() {
		return this.activetm;
	}

	public void setActivetm(String activetm) {
		this.activetm = activetm;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
