package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class SvcFee implements Serializable {

private static final long serialVersionUID = 1L;
	
	private String fofConsCode;//FOF投管人
	
	private String custName;//客户名称
	
	private String custNo;//客户号	
	
	private String consCode;//投顾
	
	private String consName;//投顾
	
	private String orgName;//部门名称
	
	private String fundTxAcctNo;//基金交易账号
	
	private String fundType;//基金类型
	
	private String fundAttr;//投资基金简称
	
	private String fundCode;//基金代码
		
	private Double avgBal=0d;//日均资产

    private Double avgBalSum=0d;//查询范围内总资产
	
	private Double svcFee=0d;//服务费
	
	private Double tailcommisionFee=0d;//尾佣
	
	private Double svcIncome=0d;//分配基数
	
	private String telNo;//投顾分机	
	
	private String startDt;//分配日期
	
	private String firstDictname;//一级来源
	
	private String secondDictname;//二级来源
	
	private Double sourceCoefficient=0d;//来源系数
	
	private String teamCode;//

    private String year;//年度

    private String quarter;//季度

    private String outletcode;//部门
	
	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getOutleCode() {
		return outleCode;
	}

	public void setOutleCode(String outleCode) {
		this.outleCode = outleCode;
	}



	private String outleCode;//
	
	public String getTeamCode() {
		return teamCode;
	}

	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}

	public String getFofConsCode() {
		return fofConsCode;
	}


	public void setFofConsCode(String fofConsCode) {
		this.fofConsCode = fofConsCode;
	}

	public String getCustName() {
		return custName;
	}



	public void setCustName(String custName) {
		this.custName = custName;
	}



	public String getCustNo() {
		return custNo;
	}



	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}



	public String getConsCode() {
		return consCode;
	}



	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}


	public String getOrgName() {
		return orgName;
	}



	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}



	public String getFundTxAcctNo() {
		return fundTxAcctNo;
	}



	public void setFundTxAcctNo(String fundTxAcctNo) {
		this.fundTxAcctNo = fundTxAcctNo;
	}

	public String getFundType() {
		return fundType;
	}



	public void setFundType(String fundType) {
		this.fundType = fundType;
	}



	public String getFundAttr() {
		return fundAttr;
	}



	public void setFundAttr(String fundAttr) {
		this.fundAttr = fundAttr;
	}



	public String getFundCode() {
		return fundCode;
	}



	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}


	public String getTelNo() {
		return telNo;
	}

	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}

	public String getStartDt() {
		return startDt;
	}

	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}

	public String getFirstDictname() {
		return firstDictname;
	}

	public void setFirstDictname(String firstDictname) {
		this.firstDictname = firstDictname;
	}

	public String getSecondDictname() {
		return secondDictname;
	}

	public Double getAvgBal() {
		return avgBal;
	}

	public void setAvgBal(Double avgBal) {
		this.avgBal = avgBal;
	}

	public Double getSvcFee() {
		return svcFee;
	}

	public void setSvcFee(Double svcFee) {
		this.svcFee = svcFee;
	}

	public Double getTailcommisionFee() {
		return tailcommisionFee;
	}

	public void setTailcommisionFee(Double tailcommisionFee) {
		this.tailcommisionFee = tailcommisionFee;
	}

	public Double getSvcIncome() {
		return svcIncome;
	}

	public void setSvcIncome(Double svcIncome) {
		this.svcIncome = svcIncome;
	}

	public void setSecondDictname(String secondDictname) {
		this.secondDictname = secondDictname;
	}



	public Double getSourceCoefficient() {
		return sourceCoefficient;
	}



	public void setSourceCoefficient(Double sourceCoefficient) {
		this.sourceCoefficient = sourceCoefficient;
	}

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getQuarter() {
        return quarter;
    }

    public void setQuarter(String quarter) {
        this.quarter = quarter;
    }

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public Double getAvgBalSum() {
        return avgBalSum;
    }

    public void setAvgBalSum(Double avgBalSum) {
        this.avgBalSum = avgBalSum;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
