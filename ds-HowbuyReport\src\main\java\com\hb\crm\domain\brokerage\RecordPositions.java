package com.hb.crm.domain.brokerage;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 持仓记录
 * <AUTHOR>
 *
 */
public class RecordPositions  implements Serializable {

	private static final long serialVersionUID = 1L;

	//QueryQSAcctBalResponse
	private String fundCode;//基金代码
	private String fundAbbr;//基金简称
	private String fundAttrHb;//好买基金简称
	private String fundType;//基金类型
	private BigDecimal expectYearRate;//预期年化
	private BigDecimal estimateRate;//预估收益
	private BigDecimal currentCost;//本金
	private String nextOpenDt;//下一开放日
	//sgztQsTransResp
	private BigDecimal sgztAmt;//申购在途
	//shztQsTransResp
	private BigDecimal shztAmt;//赎回在途
	//QueryQSAcctBalResponse resp
	private BigDecimal totalAmt;//市值
	private String bankName;//银行名称
	private String bankRegionName;//分行名称
	private String bankAcct;//银行卡号
	private BigDecimal availVol;//持有份额
	private BigDecimal balanceVol;//可用份额
	private BigDecimal currentAmt;//当前市值
	
	public String getFundCode() {
		return fundCode;
	}
	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}
	public String getFundAbbr() {
		return fundAbbr;
	}
	public void setFundAbbr(String fundAbbr) {
		this.fundAbbr = fundAbbr;
	}
	public String getFundAttrHb() {
		return fundAttrHb;
	}
	public void setFundAttrHb(String fundAttrHb) {
		this.fundAttrHb = fundAttrHb;
	}
	public String getFundType() {
		return fundType;
	}
	public void setFundType(String fundType) {
		this.fundType = fundType;
	}
	public BigDecimal getExpectYearRate() {
		return expectYearRate;
	}
	public void setExpectYearRate(BigDecimal expectYearRate) {
		this.expectYearRate = expectYearRate;
	}
	public BigDecimal getEstimateRate() {
		return estimateRate;
	}
	public void setEstimateRate(BigDecimal estimateRate) {
		this.estimateRate = estimateRate;
	}
	public BigDecimal getCurrentCost() {
		return currentCost;
	}
	public void setCurrentCost(BigDecimal currentCost) {
		this.currentCost = currentCost;
	}
	public String getNextOpenDt() {
		return nextOpenDt;
	}
	public void setNextOpenDt(String nextOpenDt) {
		this.nextOpenDt = nextOpenDt;
	}
	public BigDecimal getSgztAmt() {
		return sgztAmt;
	}
	public void setSgztAmt(BigDecimal sgztAmt) {
		this.sgztAmt = sgztAmt;
	}
	public BigDecimal getShztAmt() {
		return shztAmt;
	}
	public void setShztAmt(BigDecimal shztAmt) {
		this.shztAmt = shztAmt;
	}
	public BigDecimal getTotalAmt() {
		return totalAmt;
	}
	public void setTotalAmt(BigDecimal totalAmt) {
		this.totalAmt = totalAmt;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankRegionName() {
		return bankRegionName;
	}
	public void setBankRegionName(String bankRegionName) {
		this.bankRegionName = bankRegionName;
	}
	public String getBankAcct() {
		return bankAcct;
	}
	public void setBankAcct(String bankAcct) {
		this.bankAcct = bankAcct;
	}
	public BigDecimal getAvailVol() {
		return availVol;
	}
	public void setAvailVol(BigDecimal availVol) {
		this.availVol = availVol;
	}
	public BigDecimal getBalanceVol() {
		return balanceVol;
	}
	public void setBalanceVol(BigDecimal balanceVol) {
		this.balanceVol = balanceVol;
	}
	public BigDecimal getCurrentAmt() {
		return currentAmt;
	}
	public void setCurrentAmt(BigDecimal currentAmt) {
		this.currentAmt = currentAmt;
	}
}
