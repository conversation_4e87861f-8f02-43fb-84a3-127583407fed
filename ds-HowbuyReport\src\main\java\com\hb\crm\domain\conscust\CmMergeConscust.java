package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by jingya.xu on 2017/1/10.
 */
public class CmMergeConscust implements Serializable {
    private static final long serialVersionUID = -5871079276434433611L;
    /*  ID */
    private String id;

    private String conscuststr;

    private String custname;

    private String mobilestr;

    private String telnostr;

    private String emailstr;

    private String addressstr;

    private String status;



    private String creator;



    private String modtor;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConscuststr() {
        return conscuststr;
    }

    public void setConscuststr(String conscuststr) {
        this.conscuststr = conscuststr;
    }

    public String getCustname() {
        return custname;
    }

    public void setCustname(String custname) {
        this.custname = custname;
    }

    public String getMobilestr() {
        return mobilestr;
    }

    public void setMobilestr(String mobilestr) {
        this.mobilestr = mobilestr;
    }

    public String getTelnostr() {
        return telnostr;
    }

    public void setTelnostr(String telnostr) {
        this.telnostr = telnostr;
    }

    public String getEmailstr() {
        return emailstr;
    }

    public void setEmailstr(String emailstr) {
        this.emailstr = emailstr;
    }

    public String getAddressstr() {
        return addressstr;
    }

    public void setAddressstr(String addressstr) {
        this.addressstr = addressstr;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }



    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModtor() {
        return modtor;
    }

    public void setModtor(String modtor) {
        this.modtor = modtor;
    }
}
