/**
 * 2011-03-07 chirs.song	增加Vip
 */
package com.hb.crm.constant.help;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 产品信息.
 */
public enum WebCustFreeProdEnum {
	/**
	 * 诊断.
	 */
	Diagnosis(1, false),
	/**
	 * 账簿.
	 */
	Account(2, false),
	/**
	 * 贵宾中心
	 */
	Vip(3, false)
	;
	private int value;
	
	private boolean needCheck;

    public int getValue() {
        return value;
    }
    public boolean getNeedCheck() {
    	return needCheck;
    }
    WebCustFreeProdEnum(int value, boolean needCheck) {
        this.value = value;
        this.needCheck = needCheck;
    }
    public final static Map<Integer, String> productDescMAP;//产品描述
    public final static Map<Integer, Boolean> productSpecMAP;//产品特性
    
    static{

    	productDescMAP = new HashMap<Integer, String>();
    	productSpecMAP = new HashMap<Integer, Boolean>();
    	
    	productDescMAP.put(1, "诊断");
    	productDescMAP.put(2, "账簿");
    	productDescMAP.put(3, "贵宾中心");
    	
    	productSpecMAP.put(1, false);
    	productSpecMAP.put(2, false);
    	productSpecMAP.put(3, false);
    }
    
    /**
     * 返回对应的描述.
     * @param value int.
     * @return String
     */
    public static String getEnumDesc(final int value) {
        return WebCustFreeProdEnum.productDescMAP.get(
        		value);
    }
    public static boolean getProductSpec(final int value) {
    	return WebCustFreeProdEnum.productSpecMAP.get(
        		value);
    }

}
