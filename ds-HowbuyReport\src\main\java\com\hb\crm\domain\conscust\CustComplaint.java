package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

public class CustComplaint implements Serializable {

	private static final long serialVersionUID = 1L;
	
	 /** identifier field */
    private String complaintno = " ";

    /** nullable persistent field */
    private String custname = " ";

    /** nullable persistent field */
    private String custphone = " ";

    /** nullable persistent field */
    private String custemail = " ";

    /** nullable persistent field */
    private String custtype = " ";

    /** nullable persistent field */
    private String complainttype = " ";

    /** nullable persistent field */
    private String complaintdt = " ";

    /** nullable persistent field */
    private String complaintway = " ";

    /** nullable persistent field */
    private String complaintlevel = " ";

    /** nullable persistent field */
    private String complainttitle = " ";

    /** nullable persistent field */
    private String complaintcontent = " ";

    /** nullable persistent field */
    private String replyway = " ";

    /** nullable persistent field */
    private String callreplydt = " ";

    /** nullable persistent field */
    private String isreplied = " ";

    /** nullable persistent field */
    private String replydt = " ";

    /** nullable persistent field */
    private String replyman = " ";

    /** nullable persistent field */
    private String replycontent = " ";

    /** nullable persistent field */
    private String custsatisfylevel = " ";
    
    private String creator="" ; 

    /** nullable persistent field */
    private Date stimestamp = new Timestamp(System.currentTimeMillis());

    /** full constructor */
    

    /** default constructor */
    public CustComplaint() {
    }

    public CustComplaint(String complaintno, String custname, String custphone,
			String custemail, String custtype, String complainttype,
			String complaintdt, String complaintway, String complaintlevel,
			String complainttitle, String complaintcontent, String replyway,
			String callreplydt, String isreplied, String replydt,
			String replyman, String replycontent, String custsatisfylevel,
			 Date stimestamp,String creator) {
		super();
		this.complaintno = complaintno;
		this.custname = custname;
		this.custphone = custphone;
		this.custemail = custemail;
		this.custtype = custtype;
		this.complainttype = complainttype;
		this.complaintdt = complaintdt;
		this.complaintway = complaintway;
		this.complaintlevel = complaintlevel;
		this.complainttitle = complainttitle;
		this.complaintcontent = complaintcontent;
		this.replyway = replyway;
		this.callreplydt = callreplydt;
		this.isreplied = isreplied;
		this.replydt = replydt;
		this.replyman = replyman;
		this.replycontent = replycontent;
		this.custsatisfylevel = custsatisfylevel;
		this.creator = creator;
		this.stimestamp = stimestamp;
	}

	public String getComplaintno() {
		return complaintno;
	}

	public void setComplaintno(String complaintno) {
		this.complaintno = complaintno;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getCustphone() {
		return custphone;
	}

	public void setCustphone(String custphone) {
		this.custphone = custphone;
	}

	public String getCustemail() {
		return custemail;
	}

	public void setCustemail(String custemail) {
		this.custemail = custemail;
	}

	public String getCusttype() {
		return custtype;
	}

	public void setCusttype(String custtype) {
		this.custtype = custtype;
	}

	public String getComplainttype() {
		return complainttype;
	}

	public void setComplainttype(String complainttype) {
		this.complainttype = complainttype;
	}

	public String getComplaintdt() {
		return complaintdt;
	}

	public void setComplaintdt(String complaintdt) {
		this.complaintdt = complaintdt;
	}

	public String getComplaintway() {
		return complaintway;
	}

	public void setComplaintway(String complaintway) {
		this.complaintway = complaintway;
	}

	public String getComplaintlevel() {
		return complaintlevel;
	}

	public void setComplaintlevel(String complaintlevel) {
		this.complaintlevel = complaintlevel;
	}

	public String getComplainttitle() {
		return complainttitle;
	}

	public void setComplainttitle(String complainttitle) {
		this.complainttitle = complainttitle;
	}

	public String getComplaintcontent() {
		return complaintcontent;
	}

	public void setComplaintcontent(String complaintcontent) {
		this.complaintcontent = complaintcontent;
	}

	public String getReplyway() {
		return replyway;
	}

	public void setReplyway(String replyway) {
		this.replyway = replyway;
	}

	public String getCallreplydt() {
		return callreplydt;
	}

	public void setCallreplydt(String callreplydt) {
		this.callreplydt = callreplydt;
	}

	public String getIsreplied() {
		return isreplied;
	}

	public void setIsreplied(String isreplied) {
		this.isreplied = isreplied;
	}

	public String getReplydt() {
		return replydt;
	}

	public void setReplydt(String replydt) {
		this.replydt = replydt;
	}

	public String getReplyman() {
		return replyman;
	}

	public void setReplyman(String replyman) {
		this.replyman = replyman;
	}

	public String getReplycontent() {
		return replycontent;
	}

	public void setReplycontent(String replycontent) {
		this.replycontent = replycontent;
	}

	public String getCustsatisfylevel() {
		return custsatisfylevel;
	}

	public void setCustsatisfylevel(String custsatisfylevel) {
		this.custsatisfylevel = custsatisfylevel;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getStimestamp() {
		return stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}

	

}
