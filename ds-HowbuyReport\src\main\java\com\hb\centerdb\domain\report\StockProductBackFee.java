package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class StockProductBackFee implements Serializable {


    private static final long serialVersionUID = -1622038173938615192L;

    private String fundCode;//基金代码
    private String fundAttr;//产品名称
    private String manager;//管理人
    private String fundType;//产品分类
    private String ageSubject;//协议主体
    private String unfundName;//底层产品名称
    private Double buyAmt =0d;//销量(认缴)
    private String feeType;//费用类型
    private String mfeeRate;//结算频率
    private String finSdt;//结算起始日
    private String finEdt;//结算结束日
    private String settleDt;//结算日期
    private Double feeRate =0d;//费率
    private Double feeTax=0d;//结算金额（含税）
    private Double fee=0d;//结算金额（不含税）
    private Double feeTax1=0d;//切分金额（含税）
    private Double fee1=0d;//切分金额（不含税）
    private String subject;//切分主体
    private String u1Name;//中心
    private String u2Name;//部门1
    private String u3Name;//部门2
    private String remark;//备注
    private String buyDt;//交易确认日期
    private String isFof;//是否fof
    private String hboneNo;//一账通号
	private String custName;//客户姓名
    private String consname;//投顾
    private String conscustNo;//投顾客户号


    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getAgeSubject() {
        return ageSubject;
    }

    public void setAgeSubject(String ageSubject) {
        this.ageSubject = ageSubject;
    }

    public String getUnfundName() {
        return unfundName;
    }

    public void setUnfundName(String unfundName) {
        this.unfundName = unfundName;
    }

    public Double getBuyAmt() {
        return buyAmt;
    }

    public void setBuyAmt(Double buyAmt) {
        this.buyAmt = buyAmt;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getMfeeRate() {
        return mfeeRate;
    }

    public void setMfeeRate(String mfeeRate) {
        this.mfeeRate = mfeeRate;
    }

    public String getFinSdt() {
        return finSdt;
    }

    public void setFinSdt(String finSdt) {
        this.finSdt = finSdt;
    }

    public String getFinEdt() {
        return finEdt;
    }

    public void setFinEdt(String finEdt) {
        this.finEdt = finEdt;
    }

    public String getSettleDt() {
        return settleDt;
    }

    public void setSettleDt(String settleDt) {
        this.settleDt = settleDt;
    }

    public Double getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(Double feeRate) {
        this.feeRate = feeRate;
    }

    public Double getFeeTax() {
        return feeTax;
    }

    public void setFeeTax(Double feeTax) {
        this.feeTax = feeTax;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public Double getFeeTax1() {
        return feeTax1;
    }

    public void setFeeTax1(Double feeTax1) {
        this.feeTax1 = feeTax1;
    }

    public Double getFee1() {
        return fee1;
    }

    public void setFee1(Double fee1) {
        this.fee1 = fee1;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getU3Name() {
        return u3Name;
    }

    public void setU3Name(String u3Name) {
        this.u3Name = u3Name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBuyDt() {
        return buyDt;
    }

    public void setBuyDt(String buyDt) {
        this.buyDt = buyDt;
    }

    public String getIsFof() {
        return isFof;
    }

    public void setIsFof(String isFof) {
        this.isFof = isFof;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConscustNo() {
        return conscustNo;
    }

    public void setConscustNo(String conscustNo) {
        this.conscustNo = conscustNo;
    }
}
