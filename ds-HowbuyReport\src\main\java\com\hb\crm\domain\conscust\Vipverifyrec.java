package com.hb.crm.domain.conscust;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类Vipverifyrec.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Vipverifyrec implements Serializable {

private static final long serialVersionUID = 1L;

	private String appserialno;
	
	private String tradedt;
	
	private String appcode;
	
	private String conscustno;
	
	private String txcode;
	
	private String tradechan;
	
	private String verifycode;
	
	private String creator;
	
	private String checker;
	
	private Date stimestamp;
	
	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}
	
	public String getTradedt() {
		return this.tradedt;
	}

	public void setTradedt(String tradedt) {
		this.tradedt = tradedt;
	}
	
	public String getAppcode() {
		return this.appcode;
	}

	public void setAppcode(String appcode) {
		this.appcode = appcode;
	}
	
	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	
	public String getTxcode() {
		return this.txcode;
	}

	public void setTxcode(String txcode) {
		this.txcode = txcode;
	}
	
	public String getTradechan() {
		return this.tradechan;
	}

	public void setTradechan(String tradechan) {
		this.tradechan = tradechan;
	}
	
	public String getVerifycode() {
		return this.verifycode;
	}

	public void setVerifycode(String verifycode) {
		this.verifycode = verifycode;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public Date getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
