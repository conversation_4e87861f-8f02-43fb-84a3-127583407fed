package com.hb.crm.constant;

import java.io.Serializable;

@SuppressWarnings("serial")
public class StaticVar  implements Serializable {
    public final static String ROLE_CP = "ROLE_CP";
    public final static String DONOTUPDATE = "DONOT_UPDATE";
    public final static String NO_MESSAGE = "no message";
    public final static String ERROR_USER = "";
    public final static String ERROR_PASSWORD = "";
    public final static String ERROR_SESSION = "";
    public static int DEFAULT_PAGE_SISE = 10;

    /**
     * 表示CRM系统6.0
     */
    public static String CRM_SYSTEM = "6";

    public static final String JEDIS_YUNDA_GIS_MYBATIS_GLOBAL = "jedis_yunda_gis_mybatis_global";
    public static final String JEDIS_POOL_SPRING_KEY = "jedisPool";


    public static final String SESSION_USER_KEY = "loginUser";

    public final static String CODE_SUCCESS = "0";
    public final static String CODE_ERROR = "1";
    public final static String CODE_FATAL = "2";
    public final static String CODE_EXCEPTION= "3";
    public final static String CODE_TIMEOUT = "T";

    /**
     * //记录状态正常，使用中
     */
    public final static String RECSTAT_USED = "0";
    /**
     * //复核标志，通过
     */
    public final static String CHECKFLAG_PASS = "4";

    /**
     * 负责人状态 0-在职 1-离职
     */
    public final static String MANAGERSTATUS_IN = "0";
    public final static String MANAGERSTATUS_OUT = "1";

    /**
     * 负责人职位1-总监、副总监 3-助理4-团队长、组长
     */
    public final static String MANAGERROLE_ORGLEADER = "1";
    public final static String MANAGERROLE_ORGASS = "3";
    public final static String MANAGERROLE_TEAMLEADER = "4";

    /**
     * 字符分割符号
     */
    public final static String SPLIT_SIGN = "^";

    /**
     * 投顾状态1:正常；0：删除
     */
    public final static String CONSSTATUS_NORMAL = "1";
    public final static String CONSSTATUS_DEL = "0";

    /**
     * 组织类型
     */
    public final static String ORGTYPE_TEAM = "1";
    public final static String ORGTYPE_OUTLET = "0";

    /**
     * 组级别（0：个人级分组；1：部门级分组）
     */
    public final static String GROUP_LEVEL_PERSION = "0";
    public final static String GROUP_LEVEL_ORG = "1";

    /**
     * 是否投顾
     */
    public final static String ISCONS_YES = "0";
    public final static String ISCONS_NO = "1";

    /**
     * 权限类型"1";//角色菜单权限
     * "2";//用户菜单权限
     * "3";//角色操作权限
     * "4";//角色数据权限
     */
    public final static String AUTH_TYPE_ROLE_MENU = "1";
    public final static String AUTH_TYPE_USER_MENU = "2";
    public final static String AUTH_TYPE_ROLE_OPT = "3";
    public final static String AUTH_TYPE_ROLE_DATARANGE = "4";

    /**
     * 产品持有状态"0"; //正常持有  "1"; //已经卖出
     */
    public final static String PRODUCT_HOLD_STATUS_NORMAL = "0";
    public final static String PRODUCT_HOLD_STATUS_SALE = "1";

    /**
     * 交易确认状态"1";//已确认 "0";//未确认
     */
    public final static String TRADE_STATE_YES = "1";
    public final static String TRADE_STATE_NO = "0";

    /**
     * 交易录入审核状态"0";//未录入"1";//已录入"2";//已审核通过"3";//审核未通过"4";//作废
     */
    public final static String TRADE_INPUT_HASNO_INPUT = "0";
    public final static String TRADE_INPUT_HAS_INPUT = "1";
    public final static String TRADE_INPUT_PASS_AUDIT = "2";
    public final static String TRADE_INPUT_NOTPASS_AUDIT = "3";
    public final static String TRADE_INPUT_DISCARD = "4";


    /**
     * 投顾客户状态
     */
    public final static String CONSCUST_STATUS_NORMAL = "0";
    public final static String CONSCUST_STATUS_DEL = "1";

    /**
     * 产品预约交易类型
     */
    public final static String PREBOOK_TRADE_TYPE_BUY = "1";
    public final static String PREBOOK_TRADE_TYPE_APPEND = "2";
    public final static String PREBOOK_TRADE_TYPE_SALE = "3";

    /**
     * 合同申请状态"1";//未申请"2";//已申请"3";//已确认"4";//审核未通过
     */
    public final static String CONTRACT_STATES_NOT_APPLY = "1";
    public final static String CONTRACT_STATES_HAS_APPLY = "2";
    public final static String CONTRACT_STATES_HAS_CONFIRM = "3";
    public final static String CONTRACT_STATES_AUDIT_REFUSE = "4";

    /**
     * 预约状态"1";//未确认"2";//已确认"3";//申请撤销"4";//已撤销"5";//打回
     */
    public final static String PREBOOK_STATES_NOT_CONFIRM = "1";
    public final static String PREBOOK_STATES_HAS_CONFIRM = "2";
    public final static String PREBOOK_STATES_APPLY_CANCEL = "3";
    public final static String PREBOOK_STATES_HAS_CANCEL = "4";
    public final static String PREBOOK_STATES_RETURN = "5";

    /**
     * 打款状态"1";//未打款"2";//已打款"3";//到账确认"4";//退款
     */
    public final static String PAY_STATES_NOT_PAY = "1";
    public final static String PAY_STATES_HAS_PAY = "2";
    public final static String PAY_STATES_HAS_CONFIRM = "3";
    public final static String PAY_STATES_RETURN_PAY = "4";

    /**
     * "0";//未支付"1";//已支付未对账"2";//已支付已对账
     */
    public final static String ONLINE_PAY_STATES_NOT_PAY = "0";
    public final static String ONLINE_PAY_STATES_HAS_PAY = "1";
    public final static String ONLINE_PAY_STATES_HAS_CONFIRM = "2";

    /**
     * 折扣状态"1";//未申请 "2";//已申请"3";//分总批准"4";//分总不批准"5";//销助审核通过"6";//销助审核不通过"7";//总监批准"8";//总监不批准"9";//销助提交财务"a";//财务已发放"0";//打回
     */
    public final static String DISCOUNT_STATES_NOT_APPLY = "1";
    public final static String DISCOUNT_STATES_HAS_APPLY = "2";
    public final static String DISCOUNT_STATES_FZ_PERMIT = "3";
    public final static String DISCOUNT_STATES_FZ_NOTPERMIT = "4";
    public final static String DISCOUNT_STATES_XZ_AUDIT_PASS = "5";
    public final static String DISCOUNT_STATES_XZ_AUDIT_NOTPASS = "6";
    public final static String DISCOUNT_STATES_ZJ_PERMIT = "7";
    public final static String DISCOUNT_STATES_ZJ_NOTPERMIT = "8";
    public final static String DISCOUNT_STATES_HAS_PAY = "9";
    public final static String DISCOUNT_STATES_FIN_HAS_PAY = "a";
    public final static String DISCOUNT_STATES_RETURN = "0";

    /**
     * MGM状态"1";//未申请 "2";//已申请"3";//分总批准"4";//分总不批准"5";//销助审核通过"6";//销助审核不通过"7";//总监批准"8";//总监不批准"9";//销助提交财务"a";//财务已发放"0";//打回
     */
    public final static String MGM_STATES_NOT_APPLY = "1";
    public final static String MGM_STATES_HAS_APPLY = "2";
    public final static String MGM_STATES_FZ_PERMIT = "3";
    public final static String MGM_STATES_FZ_NOTPERMIT = "4";
    public final static String MGM_STATES_XZ_AUDIT_PASS = "5";
    public final static String MGM_STATES_XZ_AUDIT_NOTPASS = "6";
    public final static String MGM_STATES_ZJ_PERMIT = "7";
    public final static String MGM_STATES_ZJ_NOTPERMIT = "8";
    public final static String MGM_STATES_HAS_PAY = "9";
    public final static String MGM_STATES_FIN_HAS_PAY = "a";
    public final static String MGM_STATES_RETURN = "0";

    /**
     * 数字字符常量
     */
    public final static String ONE="1";
    public final static String TWO="2";
    public final static String THREE="3";
    public final static String FOUR="4";
    public final static String FIVE="5";
    public final static String SIX="6";
    public final static String SEVEEN="7";
    public final static String EIGHT="8";
    public final static String NINE="9";
    public final static String ZERO="0";

    /**
     * 方法常量
     */
    public final static String ADD="添加";
    public final static String EDIT="修改";
    public final static String DEL="删除";
    public final static String EDIT_DO="edit";
    /**
     * "1";//常量重载"2";//权限重载"3";//组织架构重载"4";//基金信息重载"5";//接口数据重载"6";//监管基金重载
     */
    public final static String RELOAD_CACHE_CONSTANT = "1";
    public final static String RELOAD_CACHE_AUTH = "2";
    public final static String RELOAD_CACHE_ORGINATION = "3";
    public final static String RELOAD_CACHE_JJXX = "4";
    public final static String RELOAD_CACHE_HTTP = "5";
    public final static String RELOAD_CACHE_JGJJ = "6";

    /**
     * 数据广度
     * "11"; //所有客户（包括未分配）
     * "12";//所有客户（不包括未分配）
     * "13";//所属组织架构及下属子部门客户
     * "14";//所属团队客户
     * "15";//分配给自己客户
     * "16";//所属高管客户及分配给自己客户
     * "17";//高管客户
     */
    public final static String DATARANGE_GD_ALL = "11";
    public final static String DATARANGE_GD_ALL_NOWFP = "12";
    public final static String DATARANGE_GD_OUTLET = "13";
    public final static String DATARANGE_GD_TEARM = "14";
    public final static String DATARANGE_GD_SELF = "15";
    public final static String DATARANGE_GD_SELFANDSER = "16";
    public final static String DATARANGE_GD_SER = "17";

    public final static String ROLE_LEVEL_CEO = "0";
    public final static String ROLE_LEVEL_MANAGER = "1";
    public final static String ROLE_LEVEL_CONS = "2";
    public final static String ROLE_LEVEL_CUSTSERVER = "3";
    public final static String ROLE_LEVEL_OTHER = "9";

    /**
     * "1";//同步基金账户信息数据
     * "2";//账户基金余额
     * "3";//基金交易账户信息
     * "4";//CP05客户银行账户信息
     * "5";//H12交易确认历史
     * "6";//分销账户基金余额表
     * "7";//分销账户基金余额收益表
     */
    public final static String ETL_AC_FUND_ACCT = "1";
    public final static String ETL_AC_FUND_ACCT_BAL = "2";
    public final static String ETL_AC_FUND_TX_ACCT = "3";
    public final static String ETL_CP_CUST_BANK_ACCT_INFO = "4";
    public final static String ETL_HIS_TRADE_ACK = "5";
    public final static String ETL_AC_DIS_FUND_ACCT_BAL = "6";
    public final static String ETL_AC_DIS_FUND_BAL_INCOME = "7";

    /**
     * 签到部门
     */
    public final static String REMARK_CS = "cs";
    public final static String REMARK_OT = "ot";

    /**
     * 加密类型
     */
    public final static String ENCRYPT_MOBILE = "1";
    public final static String ENCRYPT_PHONE = "2";
    public final static String ENCRYPT_EMAIL = "3";
    public final static String ENCRYPT_ADDRESS = "4";
    public final static String ENCRYPT_BANKNO = "5";
    public final static String ENCRYPT_IDNO = "6";

    /**
     * 是否入会"1";//已入会"2";//未入会
     */
    public final static String IS_JOIN_CLUBS_HASIN = "1";
    public final static String IS_JOIN_CLUBS_NOTIN = "2";

    /**
     * 是否签署贵宾服务手册风险提示函"1";//已签署"2";//未签署
     */
    public final static String IS_RISK_TIPS_HASSIGN = "1";
    public final static String IS_RISK_TIPS_NOTSIGN = "2";

    /**
     * 交易录入审核状态"0";//未录入"1";//已录入"2";//已审核通过"3";//审核未通过"4";//作废
     */
    public final static String TRADE_INPUT_STATES_NOTSIGN = "0";
    public final static String TRADE_INPUT_STATES_HASSIGN = "1";
    public final static String TRADE_INPUT_STATES_AUDITPASS = "2";
    public final static String TRADE_INPUT_STATES_AUDITREJECT = "3";
    public final static String TRADE_INPUT_STATES_DISCARD = "4";

    /**
     * 业务类别 "120";//购买"122";//追加"124";//赎回"142";//强制赎回"143";//分红"144";//强制调增"145";//强制调减"151";//产品到期"187";//到期赎回"999";//私募股权回款
     */
    public final static String BUSINESS_CODE_BUY = "120";
    public final static String BUSINESS_CODE_APPEND = "122";
    public final static String BUSINESS_CODE_SALE = "124";
    public final static String BUSINESS_CODE_QZSH = "142";
    public final static String BUSINESS_CODE_FH = "143";
    public final static String BUSINESS_CODE_QZTZ = "144";
    public final static String BUSINESS_CODE_QZTJ = "145";
    public final static String BUSINESS_CODE_CPDQ = "151";
    public final static String BUSINESS_CODE_DQSH = "187";
    public final static String BUSINESS_CODE_SMGQHK = "999";

    /**
     * 交易审核标志"0";//不需要"1";//准备"2";//等待"3";//不通过"4";//通过"5";//驳回"9";//取消
     */
    public final static String TX_CHECK_FLAG_NOREQUIRE = "0";
    public final static String TX_CHECK_FLAG_PREPARE = "1";
    public final static String TX_CHECK_FLAG_WAITING = "2";
    public final static String TX_CHECK_FLAG_REFUSE = "3";
    public final static String TX_CHECK_FLAG_PASS = "4";
    public final static String TX_CHECK_FLAG_REJECT = "5";
    public final static String TX_CHECK_FLAG_CANCELED = "9";

    /**
     * 是否是投顾客户和网站客户的主关联"1";//主关联"0";//非主关联
     */
    public final static String IS_R_WEB_PRIMARY = "1";
    public final static String IS_R_WEB_SECONDARY = "0";

    /**
     * 投资者类型 "0";//机构客户"1";//个人客户 "2";//产品"9";//基金客户
     */
    public final static String INVST_TYPE_ORG = "0";
    public final static String INVST_TYPE_PERSONAL = "1";
    public final static String INVST_TYPE_PRODUCT = "2";
    public final static String INVST_TYPE_FUND = "9";
    /**
     * 投顾自动分配的时候，当前总数和初始化额度的比率
     */
    public final static double CURR_INIT_TARGETNUM_RATE = 1.3;

    /**
     * //投顾客户产品长度
     */
    public final static int CONSCUSTPRODUCT_LEN = 16;
    /**
     * //产品的前置字符串，不允许是空
     */
    public final static char PREFIX_CONSCUSTPROD = '0';

    /**
     * 是否标识
     */
    public final static String YES_OR_NO_YES = "1";
    public final static String YES_OR_NO_NO = "0";

    public final static String BNT_TYPE_ADD = "add";
    public final static String BNT_TYPE_EDIT = "edit";
    public final static String BNT_TYPE_DEL = "del";
    public final static String BNT_TYPE_INPUT = "input";
    public final static String BNT_TYPE_OUTPUT = "output";
    public final static String BNT_TYPE_NORMAL = "normal";
    public final static String BNT_TYPE_RELOAD = "reload";
    public final static String BNT_TYPE_OK = "ok";

    /**
     * 数据深度
     * "21"; //所有客户明文显示
     * "22";//所有客户的联系信息加密显示
     * "23";//所属客户明文，不归属自己客户的联系信息加密显示
     * "24";//所属客户明文，所属高管客户明文，不归属自己客户加密显示
     * "25";//所属客户明文，所属高管客户加密，不归属自己客户明文显示
     * "26";//所属客户加密，不归属自己客户明文显示
     * "27";//所属客户加密，所属高管客户明文，不归属自己客户加密显示
     *  "28";//所属客户加密，所属高管客户加密，不归属自己客户明文显示
     */
    public final static String DATARANGE_SD_ALL_SHOW = "21";
    public final static String DATARANGE_SD_ALL_ENCRY = "22";
    public final static String DATARANGE_SD_MY_SHOW = "23";
    public final static String DATARANGE_SD_MY_AND_MGR_SHOW = "24";
    public final static String DATARANGE_SD_MY_AND_NOTMY_SHOW = "25";
    public final static String DATARANGE_SD_NOTMY_AND_MGR_SHOW = "26";
    public final static String DATARANGE_SD_MGR_SHOW = "27";
    public final static String DATARANGE_SD_NOTMY_SHOW = "28";

    public final static String CUST_CONTRACT_PHONE = "1";
    public final static String CUST_CONTRACT_MOBILE = "2";
    public final static String CUST_CONTRACT_EMAIL = "3";
    public final static String CUST_CONTRACT_ADDRESS = "4";
    public final static String CUST_CONTRACT_LINKPHONE = "5";
    public final static String CUST_CONTRACT_LINKMOBILE = "6";
    public final static String CUST_CONTRACT_LINKEMAIL = "7";
    public final static String CUST_CONTRACT_LINKADDRESS = "8";
    public final static String CUST_CONTRACT_MOBILE2 = "9";
    public final static String CUST_CONTRACT_WECHATCODE = "10";

    /**
     * 是
     */
    public final static String CUST_SPECIALL_YES = "1";
    /**
     * 否
     */
    public final static String CUST_SPECIALL_NO = "0";

    /**
     * 客户订阅的服务类型手机短信
     */
    public final static String CUST_SERVICE_MSG = "001";
    /**
     * 客户订阅的服务类型邮件
     */
    public final static String CUST_SERVICE_EMAIL = "002";

    /**
     * IC部门员工新添的客户审核状态 0:待审核;1:审核通过;2:审核不通过
     */
    public final static String IC_CHECK_FLAG_PENDING = "0";
    public final static String IC_CHECK_FLAG_YES = "1";
    public final static String IC_CHECK_FLAG_NO = "2";

    /**
     * "0";//邮件未发送 "1";//邮件发送成功"2";//邮件发送失败
     */
    public final static String SMG_SENDFLAG_NOTSEND = "0";
    public final static String SMG_SENDFLAG_HASSEND = "1";
    public final static String SMG_SENDFLAG_SENDERROR = "2";

    /**
     * //存在一账通
     */
    public final static String IC_CHECK_HBONE_NO = "2";

    /**
     * 入会申请审核标志(0 : 未审核)
     */
    public final static String JOIN_CLUB_FLAG_NOT = "0";
    /**
     * 入会申请审核标志(1: 审核通过)
     */
    public final static String JOIN_CLUB_FLAG_OK = "1";
    /**
     * 入会申请审核标志(2 : 审核未通过)
     */
    public final static String JOIN_CLUB_FLAG_FAIL = "2";

    /**
     * 入会申请错误标志(0 :错误 )
     * */
    public final static String JOIN_CLUB_ERROR_FLAG_ERROR ="0";

    /**
     * 入会申请错误标志(1 : 正确)
     * */
    public final static String JOIN_CLUB_ERROR_FLAG_OK ="1";

    /**
     * 入会问卷类型012
     */
    public final static String CUST_DOC_SATISF_5 = "012";

    /**
     * 客户档案有效标志
     */
    public final static String CUST_DOC_ACTIVE_FLAG_YES = "0";
    public final static String CUST_DOC_ACTIVE_FLAG_NO = "0";

    /**
     * 16位全零和10位全零
     */
    public final static String ZERO_16 = "0000000000000000";
    public final static String ZERO_10 = "0000000000";

    /**
     * 下单状态"1"：未下单"2"：下单成功"3"：下单失败"4"：冷静期中
     */
    public final static String ORDER_STATE_NO = "1";
    public final static String ORDER_STATE_YES = "2";
    public final static String ORDER_STATE_FAIL = "3";
    public final static String ORDER_STATE_CALM = "4";

    public final static String [] CAN_SEE_ALL_REPORT_ROLES = new String[]{"ROLE_PD_HEAD","ROLE_PD_HEAD_ASSISTANT","ROLE_PD_ASSISTANT","ROLE_PD_ASSISTANT_II","ROLE_FIN","ROLE_PS_HEAD","ROLE_PS","ROLE_PS_LEADER"};
    public final static String [] CAN_SEE_ADDR_ROLES = new String[]{"ROLE_PS_HEAD","ROLE_TRAIN_SERVICE","ROLE_PS_LEADER","ROLE_PS"};
    /**
     * 家庭账户审核状态0：未审核；1：审核通过；2：审核未通过
     */
    public final static String FAMILY_CHECK_FLAG_NO = "0";
    public final static String FAMILY_CHECK_FLAG_PASS = "1";
    public final static String FAMILY_CHECK_FLAG_NOTPASS = "2";

    /**
     * 标签对象种类"1"：标签对象种类:投顾"2"：标签对象种类:渠道"3"：标签对象种类:客户
     */
    public final static String TAG_TYPE_CODE_INVESTMENT_ADVISER = "1";
    public final static String TAG_TYPE_CODE_CHANNEL = "2";
    public final static String TAG_TYPE_CODE_CUSTOMER = "3";

    /**
     * 标签状态默认值  0:有效，1:无效
     */
    public static final String RECSTAT_DEFUALT = "0";
    public static final String RECSTAT_DEL = "1";

    /**
     * 投顾职级分类
     */
    public static final String consultant_level = "1";//投顾职级
    public static final String consultant_manage_level = "2";//投顾管理职级
    public static final String consultant_back_level ="3";//投顾中后台职级
    public static final String consultant_grade ="4";//投顾中后台职级


    /**
     * 公募合作渠道
     */
    public static final  String OUTLET_CODE = "OUTLET_CODE";
    /**
     * 合作类型
     */
    public static final  String OUTLET_CODE_HZLX = "OUTLET_CODE_HZLX";

    /**
     * 常量是否有效 0:有效;1:无效
     */
    public static final String ISVALID_TRUE = "0";
    public static final String ISVALID_FALSE = "1";

    /**
     * 隐藏 0否   1是
     */
    public static final String TAG_HIDDEN_FALSE = "0";
    public static final String TAG_HIDDEN_TRUE = "1";

    /**
     * "1":自动化;"0":只关联
     */
    public static final String IS_AUTO_YES = "1";
    public static final String IS_AUTO_NO = "0";

    /**
     * //网站
     */
    public static final String IS_ONLINE_WEB = "1";

    /**
     * //柜台
     */
    public static final String IS_ONLINE_GUITAI = "0";

    /**
     * 产品投顾角色
     */
    public static final String PC_TG = "ROLE_PC_SIC";
    /**
     * 系统管理员
     */
    public static final String ROLE_SUPERVISOR = "ROLE_SUPERVISOR";
    /**
     * 售后负责任
     */
    public static final String ROLE_PS_LEADER = "ROLE_PS_LEADER";
    /**
     * 售后作业岗
     */
    public static final String ROLE_PS = "ROLE_PS";

    /**
     * 在途理财师
     */
    public static final String ROLE_SIC_TEMP = "ROLE_SIC_TEMP";

    /**
     * 客户标签1:未接通2:无效（空机/停机） 3: 疑似同行 4:有意向
     */
    public static final String CUST_MARK_WJT = "1";
    public static final String CUST_MARK_WX = "2";
    public static final String CUST_MARK_TH = "3";
    public static final String CUST_MARK_YYX = "4";

    /**
     * 是否分次CALL产品 1：是；0：否
     */
    public static final String FCCL_YES = "1";
    public static final String FCCL_NO = "0";

    /**
     * 1：代销；2：直销；3：直转代
     */
    public static final String SFMSJG_DX = "1";
    public static final String SFMSJG_ZX = "2";
    public static final String SFMSJG_ZZD = "3";

    /**
     * 是否电子成单1：是；0：否
     */
    public static final String SFDZCD_YES = "1";
    public static final String SFDZCD = "0";

    /**
     * //纸质成单
     */
    public static final String PAPER_ORDER = "1";
    /**
     * //电子成单
     */
    public static final String ONLINE_ORDER = "2";

    /**
     * 交易业务码"1120":认购;"1122":申购;"1124"：赎回"1129"：修改分红方式
     */
    public static final String BUSICODE_BUY = "1120";
    public static final String BUSICODE_APPEND = "1122";
    public static final String BUSICODE_SALE = "1124";
    public static final String BUSICODE_MODDIV = "1129";

    /**
     * 订单状态"1:申请成功;2:部分确认3:确认成功4:确认失败5:自行撤销6:强制取消
     */
    public static final String ORDERSTAT_APPLY = "1";
    public static final String ORDERSTAT_PARTCONFIRM = "2";
    public static final String ORDERSTAT_ALLCONFIRM = "3";
    public static final String ORDERSTAT_FAILCONFIRM = "4";
    public static final String ORDERSTAT_SELFCANCEL = "5";
    public static final String ORDERSTAT_FORCECANCEL = "6";

    /**
     * 新折扣状态1:未申请;2:已申请;3:初审通过4:初审不通过5:终审通过6:终审不通过7:已撤销
     */
    public final static String DISCOUNT_NSTATES_NOT_APPLY = "1";
    public final static String DISCOUNT_NSTATES_HAS_APPLY = "2";
    public final static String DISCOUNT_NSTATES_CS_PASS = "3";
    public final static String DISCOUNT_NSTATES_CS_NOPASS = "4";
    public final static String DISCOUNT_NSTATES_ZS_PASS = "5";
    public final static String DISCOUNT_NSTATES_ZS_NOPASS = "6";
    public final static String DISCOUNT_NSTATES_CANCEL = "7";

    /**
     * 折扣类型4:正常折扣； 5:活动竞赛；8大单定制
     */
    public final static String DISCOUNT_TYPE_NORMAL = "4";
    public final static String DISCOUNT_TYPE_ACTIVITY = "5";
    public final static String DISCOUNT_TYPE_DADAN = "8";

    /**
     * 折扣形式-现金
     */
    public final static String DISCOUNT_STYLE_CASH = "2";
    /**
     * //折扣方式-直接少汇
     */
    public final static String DISCOUNT_WAY_ZJSH = "1";
    /**
     * //是否提供发票-否
     */
    public final static String DISCOUNT_ISREFUNDS_NO="1";

    /**
     * 预约日志1，预约确认2，打款到账确认3，交易确认4，购买金额更新5，退款6，撤销
     */
    public static final String PREBOOK_LOG_PRECOMFIRM = "1";
    public static final String PREBOOK_LOG_PAYCHECK = "2";
    public final static String PREBOOK_LOG_TRADECONFIRM = "3";
    public final static String PREBOOK_LOG_CHANGEBUYAMT = "4";
    public final static String PREBOOK_LOG_REFUND = "5";
    public final static String PREBOOK_LOG_CANCEL = "6";

    public final static String ISDEL_YES = "1";
    public final static String ISDEL_NO = "0";

    /**
     * 订单审核状态"1";//审核通过"0";//审核退回
     */
    public final static String CHECK_ORDER_PASS = "1";
    public final static String CHECK_ORDER_RETURN = "0";

    /**
     * 好买产品线
     */
    public static final String HMCPX_CASH = "0";
    public static final String HMCPX_ZQ = "1";
    public static final String HMCPX_GD = "2";
    public static final String HMCPX_DC = "3";
    public static final String HMCPX_GP = "4";
    public static final String HMCPX_PEVC = "5";
    public static final String HMCPX_FDC = "6";
    public static final String HMCPX_OTHER = "7";

    public static final String ZJL_BQD = "1";
    public static final String ZJL_WZJ = "2";
    public static final String ZJL_500 = "3";
    public static final String ZJL_500_1000 = "4";
    public static final String ZJL_1000 = "5";

    public static final String KHYX_BQD = "1";
    public static final String KHYX_WYX = "2";
    public static final String KHYX_YYX = "3";

    public static final String CUST_LEVEL_ZY = "1";
    public static final String CUST_LEVEL_PT = "2";
    public static final String CUST_LEVEL_WX = "3";

    /**
     * //连续两次打了客户停机
     */
    public static final String TWO_KHTJ_YES = "1";

    /**
     * //连续两次打了同业竞争
     */
    public static final String TWO_TYJZ_YES = "1";

    public static final String IC_THK = "tonghang_cbk";
    public static final String CH_THK = "HBC_THK";
    public static final String QKSLK = "qkslk";

    public static final String GDCJKH = "1";

    /**
     * //IC财富三部
     */
    public static final String IC_CFSB_ORGCODE = "618001999";

    /**
     * //IC下的二分体系
     */
    public static final String IC_EFTX_ORGCODE = "1000000180";

    /**
     * HOWBUY
     */
    public static final String HOWBUY_ORGCODE = "0";

    /**
     * //IC
     */
    public static final String IC_ORGCODE = "1";

    /**
     * //CH
     */
    public static final String CH_ORGCODE = "10";

    /**
     * 仍持有产品类型
     */
    public final static String HAS_PRODTYPE_GP = "1";
    public final static String HAS_PRODTYPE_GQ = "2";
    public final static String HAS_PRODTYPE_DC = "3";
    public final static String HAS_PRODTYPE_ZQ = "4";
    public final static String HAS_PRODTYPE_GS = "5";
    public final static String HAS_PRODTYPE_FDC = "6";
    public final static String HAS_PRODTYPE_XJ = "7";
    public final static String HAS_PRODTYPE_QT = "8";

    /**
     * "1";//未邮寄"2";//部分邮寄"3";//全部邮寄"4";//无需邮寄
     */
    public final static String MAIL_ALLNO = "1";
    public final static String MAIL_PARTNO = "2";
    public final static String MAIL_ALLYES = "3";
    public final static String MAIL_NONEED = "4";

    /**
     *"1";//未签收"2";//部分签收"3";//全部签收"4";//无需签收
     */
    public final static String SIGN_ALLNO = "1";
    public final static String SIGN_PARTNO = "2";
    public final static String SIGN_ALLYES = "3";
    public final static String SIGN_NONEED = "4";

    /**
     * //是否邮寄-1是;0否
     */
    public final static String ISMAIL_YES = "1";
    public final static String ISMAIL_NO = "0";

    /**
     * 是否签收-1是;0否
     */
    public final static String ISSIGN_YES = "1";
    public final static String ISSIGN_NO = "0";


    /**
     * 交易属性"01";//非交易"02";//交易-购买"03";//交易-赎回"04";//交易-撤单"05";//交易-份额转让"06";//交易-修改分红方式
     */
    public final static String BUSIPOR_NOTRADE = "01";
    public final static String BUSIPOR_TRADE_BUY = "02";
    public final static String BUSIPOR_TRADE_SALE = "03";
    public final static String BUSIPOR_TRADE_CANCEL = "04";
    public final static String BUSIPOR_TRADE_TRANSF = "05";
    public final static String BUSIPOR_TRADE_DIVID = "06";

    /**
     * 是否后补1是;0否
     */
    public final static String ISLATER_YES = "1";
    public final static String ISLATER_NO = "0";

    /**
     * 订单最终状态"1";//在途"2";//通过"3";//作废
     */
    public final static String LASTSTAT_ORDER_ONWAY = "1";
    public final static String LASTSTAT_ORDER_PASS = "2";
    public final static String LASTSTAT_ORDER_INVALID = "3";

    /**
     * 当前审核状态
     * "1";//待分部审核
     * "2";//分部退回
     * "5";//分部已审核
     * "6";//OP初审退回
     * "7";//OP初审通过
     * "8";//OP复审退回
     * "9";//审核通过
     * "10";//作废
     * "13";//失效
     */
    public final static String CURSTAT_WAIT_CHECK = "1";
    public final static String CURSTAT_DEPT_RETURN = "2";
    public final static String CURSTAT_DEPT_PASS = "5";
    public final static String CURSTAT_OP_RETURN = "6";
    public final static String CURSTAT_OP_PASS = "7";
    public final static String CURSTAT_OPR_RETURN = "8";
    public final static String CURSTAT_ORDER_PASS = "9";
    public final static String CURSTAT_ORDER_INVALID = "10";
    public final static String CURSTAT_ORDER_ISDEL = "13";

    /**
     * 当前审核部门"FBSH";//分部审核"OPCS";//OP初审"OPFS";//OP复审
     */
    public final static String CUR_DEPT_FBSH = "FBSH";
    public final static String CUR_DEPT_OPCS = "OPCS";
    public final static String CUR_DEPT_OPFS = "OPFS";

    /**
     * 是否进入OP"1";//是"0";//否"2";//空值
     */
    public final static String COUNTERISOP_YES = "1";
    public final static String COUNTERISOP_NO = "0";
    public final static String COUNTERISOP_NULL = "2";

    /**
     * 业务区域"1";//大陆"2";//香港
     */
    public final static String COUNTER_BUSIAREA_DL = "1";
    public final static String COUNTER_BUSIAREA_HK = "2";

    /**
     * 业务类型
     * 01:开户
     02:默认回款方式设置
     03:添卡
     04:身份信息变更
     05:手机信息变更
     06:银行卡信息变更
     07:投资者类别变更（仅限专业投资者）
     08:协议回款方式变更
     09:CRS信息变更
     10:专业投资者认定
     11:好买通讯方式补充
     12:修改分红方式
     13:非有限合伙类产品认申购
     14:有限合伙类产品认申购
     15:追加
     16:赎回或逾期赎回
     17:撤单
     18:份额转让
     19:补充协议上传
     20:后续call款
     21:风险测评
     27:汉和19期赎回收益
     */
    public final static String COUNTER_BUSIID_01 = "01";
    public final static String COUNTER_BUSIID_02 = "02";
    public final static String COUNTER_BUSIID_03 = "03";
    public final static String COUNTER_BUSIID_04 = "04";
    public final static String COUNTER_BUSIID_05 = "05";
    public final static String COUNTER_BUSIID_06 = "06";
    public final static String COUNTER_BUSIID_07 = "07";
    public final static String COUNTER_BUSIID_08 = "08";
    public final static String COUNTER_BUSIID_09 = "09";
    public final static String COUNTER_BUSIID_10 = "10";
    public final static String COUNTER_BUSIID_11 = "11";
    public final static String COUNTER_BUSIID_12 = "12";
    public final static String COUNTER_BUSIID_13 = "13";
    public final static String COUNTER_BUSIID_14 = "14";
    public final static String COUNTER_BUSIID_15 = "15";
    public final static String COUNTER_BUSIID_16 = "16";
    public final static String COUNTER_BUSIID_17 = "17";
    public final static String COUNTER_BUSIID_18 = "18";
    public final static String COUNTER_BUSIID_19 = "19";
    public final static String COUNTER_BUSIID_20 = "20";
    public final static String COUNTER_BUSIID_21 = "21";
    public final static String COUNTER_BUSIID_27 = "27";


    public final static String HB_DISCODE = "HB000A001";
    public final static String HB_DISABBR = "好买";

    public final static String UPDATE_PRE_CANCEL = "cancel";
    public final static String UPDATE_PRE_APPLYPAY = "applypay";
    public final static String UPDATE_PRE_CONFIRMPAY = "confirmpay";
    public final static String UPDATE_PRE_CONFIRMPREBOOK = "confirmprebook";

    public final static String REPEATBUY_YES = "1";

    /**
     * 折扣操作记录"1";//申请"2";//修改"3";//初审通过"4";//终审通过"5";//初审驳回"6";//终审驳回"7";//终审修改"8";//撤销折扣
     */
    public final static String DISCOUNT_OPT_APPLY = "1";
    public final static String DISCOUNT_OPT_MODIFY = "2";
    public final static String DISCOUNT_OPT_FIRST_AUDIT_PASS = "3";
    public final static String DISCOUNT_OPT_END_AUDIT_PASS = "4";
    public final static String DISCOUNT_OPT_FIRST_AUDIT_REJECT = "5";
    public final static String DISCOUNT_OPT_END_AUDIT_REJECT = "6";
    public final static String DISCOUNT_OPT_AUDIT_MODIFY = "7";
    public final static String DISCOUNT_OPT_CANCELDISCOUNT = "8";


    /**
     * "1";//私募股权回款"2";//分红"3";//强制调减"4";//到期赎回"5";//强行调增"6";//强制赎回
     */
    public final static String LOAD_TRADETYPE_RETURN = "1";
    public final static String LOAD_TRADETYPE_DIV = "2";
    public final static String LOAD_TRADETYPE_DUMP = "3";
    public final static String LOAD_TRADETYPE_DQSH = "4";
    public final static String LOAD_TRADETYPE_QXTZ = "5";
    public final static String LOAD_TRADETYPE_QZSH = "6";

    /**
     * "1";//分红或股权回款发送短信"0";//分红或股权回款不发送短信
     */
    public static final String TRADE_SENDMESSAGE_OK = "1";
    public static final String TRADE_SENDMESSAGE_NO = "0";

    /**
     * 1:有效；0：删除
     */
    public static final String INSUR_ISDEL_NO = "1";
    public static final String INSUR_ISDEL_YES = "0";

    /**
     * 创新产品业务类型 1:国内；2：海外-香港；3：海外-美国
     */
    public static final String INSUR_BUSITYPE_GN = "1";
    public static final String INSUR_BUSITYPE_HW_HK = "2";
    public static final String INSUR_BUSITYPE_HW_USA = "3";

    /**
     * 创新产品性质 1:主险;2:附加险
     */
    public static final String INSUR_PROPER_MAIN = "1";
    public static final String INSUR_PROPER_ATTR = "2";

    /**
     * 创新产品投保关系 0：本人；1:父母;2:配偶;3:子女;4:祖孙;5:其他
     */
    public static final String INSUR_RELATION_ME = "0";
    public static final String INSUR_RELATION_FM = "1";
    public static final String INSUR_RELATION_PO = "2";
    public static final String INSUR_RELATION_ZN = "3";
    public static final String INSUR_RELATION_ZS = "4";
    public static final String INSUR_RELATION_OTHER = "5";

    /**
     * 创新产品预约状态1：未确认；2：已确认；3：已撤销
     */
    public static final String INSUR_PRESTATE_NOTCONFORM = "1";
    public static final String INSUR_PRESTATE_CONFORM = "2";
    public static final String INSUR_PRESTATE_CANCEL = "3";

    /**
     * 创新产品核保状态1:待签单;2:核保中;3:核保通过;4:延保/拒保;5:退保（冷静期内）;6:正常退保
     */
    public static final String INSUR_STATE_DQD = "1";
    public static final String INSUR_STATE_HBZ = "2";
    public static final String INSUR_STATE_HBTG = "3";
    public static final String INSUR_STATE_YB = "4";
    public static final String INSUR_STATE_TBL = "5";
    public static final String INSUR_STATE_TB = "6";

    /**
     * 创新产品复核状态1:待复核;2:已复核
     */
    public static final String INSUR_CHECHSTATE_NOTCHECK = "1";
    public static final String INSUR_CHECHSTATE_HASCHECK = "2";

    /**
     * 创新产品财务结算状态 1:待结算;2:已开票;3:已结算
     */
    public static final String INSUR_FINSTATE_DJS = "1";
    public static final String INSUR_FINSTATE_YKP = "2";
    public static final String INSUR_FINSTATE_YJS = "3";

    /**
     * 创新产品类型1:储蓄型;2:重疾险;3:寿险;4:意外险;5:医疗险
     */
    public static final String INSUR_PRODTYPE_CX = "1";
    public static final String INSUR_PRODTYPE_ZJ = "2";
    public static final String INSUR_PRODTYPE_SX = "3";
    public static final String INSUR_PRODTYPE_YW = "4";
    public static final String INSUR_PRODTYPE_YL = "5";

    /**
     * 创新产品附件类型1:计划书;2:申请单;3:回执或小票
     */
    public static final String INSUR_FILETYPE_JHS = "1";
    public static final String INSUR_FILETYPE_SQD = "2";
    public static final String INSUR_FILETYPE_HZXP = "3";

    /**
     * 创新产品缴费状态1:已缴费;2:部分缴费;3:未缴费
     */
    public static final String INSUR_PAYSTAT_HASPAY = "1";
    public static final String INSUR_PAYSTAT_PARTPAY = "2";
    public static final String INSUR_PAYSTAT_NOTPAY = "3";

    /**
     * 创新产品缴费状态1:已缴费;2:未缴费;31:未缴费（宽限期）;32:未缴费（逾期）;33:未缴费（终止）
     */
    public static final String INSUR_PAYSTATALL_HASPAY = "1";
    public static final String INSUR_PAYSTATALL_NOTPAY = "3";
    public static final String INSUR_PAYSTATALL_NOTPAY_EXTEND = "31";
    public static final String INSUR_PAYSTATALL_NOTPAY_DUE_ = "32";
    public static final String INSUR_PAYSTATALL_NOTPAY_END = "33";

    /**
     * 创新产品是否变为退保 1是；0否
     */
    public static final String INSUR_ISCHANGERETURN_YES = "1";
    public static final String INSUR_ISCHANGERETURN_NO = "0";

    /**
     * 创新产品预约产品到期状态-正常
     */
    public static final String INSUR_EXPIRE_STATE_NORMAL = "1";
    /**
     * 创新产品预约产品到期状态-终止
     */
    public static final String INSUR_EXPIRE_STATE_END = "0";
    /**
     * 创新产品预约产品到期状态-暂停
     */
    public static final String INSUR_EXPIRE_STATE_STOP = "0";

    /**
     * 资产配置记录1:有效;0:删除
     */
    public static final String ASSECT_ISDEL_NO = "1";
    public static final String ASSECT_ISDEL_YES = "0";

    /**
     * 创新产品佣金率类型1：公司；2：投顾
     */
    public static final String INSUR_RATIO_TYPE_COMP = "1";
    public static final String INSUR_RATIO_TYPE_TG = "2";

    /**
     * 资产配置流动性类型:1-现金;2--银行存款
     */
    public static final String ASSECT_FLUIDTYPE_XJ = "1";
    public static final String ASSECT_FLUIDTYPE_YHCK = "2";

    /**
     * 资产配置可投资资产类型1:公募基金;2:私募基金;3:股票;4:银行理财;5:债券;99:其他
     */
    public static final String ASSECT_INVESTTYPE_GMJJ = "1";
    public static final String ASSECT_INVESTTYPE_SMJJ = "2";
    public static final String ASSECT_INVESTTYPE_GP = "3";
    public static final String ASSECT_INVESTTYPE_YHLC = "4";
    public static final String ASSECT_INVESTTYPE_ZQ = "5";
    public static final String ASSECT_INVESTTYPE_OTHER = "99";

    /**
     * 资产配置可投资产品类型1:股票型;2:类固收;3:管理期货;4:股权;5:其他;8:市场中性;9:复合策略
     */
    public static final String ASSECT_PRDTYPE_GP = "1";
    public static final String ASSECT_PRDTYPE_DC = "3";
    public static final String ASSECT_PRDTYPE_LGS = "2";
    public static final String ASSECT_PRDTYPE_GQ = "4";
    public static final String ASSECT_PRDTYPE_OTHER = "5";
    public static final String ASSECT_PRDTYPE_SCZX = "8";
    public static final String ASSECT_PRDTYPE_FHCL = "9";

    /**
     * 资产配置产品投资区域1国内;0海外
     */
    public static final String ASSECT_INVESTAREA_IN = "1";
    public static final String ASSECT_INVESTAREA_OUT = "0";

    /**
     * 资产配置是否投资资产标志1:是2：否
     */
    public static final String ASSECT_ISINVEST_YES = "1";
    public static final String ASSECT_ISINVEST_NO = "2";
    /**
     * /资产配置还款方式1:分期;2:次性还本付息
     */
    public static final String ASSECT_RETURNTYPE_FQ = "1";
    public static final String ASSECT_RETURNTYPE_YCX = "2";

    /**
     * 资产配置收入类型1:工作收入;99:其他收入
     */
    public static final String ASSECT_INCOMETYPE_WORK = "1";
    public static final String ASSECT_INCOMETYPE_OTHER = "99";

    /**
     * 资产配置支出类型1:日常生活支出;2:理财性支出;99:其他支出
     */
    public static final String ASSECT_PAYTYPE_RCSH = "1";
    public static final String ASSECT_PAYTYPE_LCX = "2";
    public static final String ASSECT_PAYTYPE_OTHER = "99";

    /**
     * 资产配置保险类型-储蓄型险
     */
    public static final String ASSECT_ENSURETYPE_CXX = "1";
    /**
     * 资产配置保险类型-重疾险
     */
    public static final String ASSECT_ENSURETYPE_ZJX = "2";
    /**
     * //资产配置保险类型-寿险
     */
    public static final String ASSECT_ENSURETYPE_SX = "3";
    /**
     * //资产配置保险类型-意外险
     */
    public static final String ASSECT_ENSURETYPE_YWX = "3";
    /**
     * //资产配置保险类型-医疗险
     */
    public static final String ASSECT_ENSURETYPE_YLX = "3";
    /**
     * //资产配置保险类型-其他保险
     */
    public static final String ASSECT_ENSURETYPE_OTHER = "99";

    /**
     * //资产配置被保险人类型-本人
     */
    public static final String ASSECT_PROFITTYPE_ME = "1";
    /**
     * //资产配置被保险人类型-家庭其他成员
     */
    public static final String ASSECT_PROFITTYPE_OTHER = "99";

    /**
     * //资产配置保费缴纳方式-定期
     */
    public static final String ASSECT_ENSUREPAYTYPE_DQ = "1";
    /**
     * //资产配置保费缴纳方式-趸交
     */
    public static final String ASSECT_ENSUREPAYTYPE_DJ = "2";

    /**
     * //资产配置目标类型-退休
     */
    public static final String ASSECT_TARGETTYPE_TX = "1";
    /**
     * //资产配置目标类型-购房
     */
    public static final String ASSECT_TARGETTYPE_GF = "2";
    /**
     * //资产配置目标类型-子女教育
     */
    public static final String ASSECT_TARGETTYPE_ZNJY = "3";
    /**
     * //资产配置目标类型-其他
     */
    public static final String ASSECT_TARGETTYPE_OTHER = "99";

    /**
     * 资产配置是否贷款标志-1:是;0:否
     */
    public static final String ASSECT_ISLOAN_YES = "1";
    public static final String ASSECT_ISLOAN_NO = "0";

    /**
     * 资产配置负债类型1:贷款;2:其他借款
     */
    public static final String ASSECT_DEBTTYPE_LOAN = "1";
    public static final String ASSECT_DEBTTYPE_BORROW = "2";

    /**
     * //报告操作日志下载
     */
    public static final String REPORTFUND_OPT_DOWN = "1";
    /**
     * //报告操作日志预览
     */
    public static final String REPORTFUND_OPT_VIEW = "2";

    /**
     * 划转原因：1：重复客户2：日常划转；3：离职划转；4：在途新人入职；5：客户投诉；6：Leads分配；7:20万手动划转；8：深潜；9：其他（客服）10：分享链接99：其他
     */
    public static final String TRANSF_REASON_CFKH ="1";
    public static final String TRANSF_REASON_RCHZ ="2";
    public static final String TRANSF_REASON_LZHZ ="3";
    public static final String TRANSF_REASON_ZTXRRZ ="4";
    public static final String TRANSF_REASON_KHTS ="5";
    public static final String TRANSF_REASON_LEADSFP ="6";
    public static final String TRANSF_REASON_ESWSDHZ ="7";
    public static final String TRANSF_REASON_SQ ="8";
    public static final String TRANSF_REASON_OTHERKF ="9";
    public static final String TRANSF_REASON_SHARE ="10";
    public static final String TRANSF_REASON_OTHER ="99";

    /**
     * //关键字客服模块
     */
    public static final String  KEYWORD_CS="01";

    /**
     * //关键字资产配置模块
     */
    public static final String  KEYWORD_ASSET="02";

    /**
     * 份额转让状态：1：未确认；2：已撤销；3：已确认
     */
    public static final String TRANSFERSTAT_NOCOMFIRM = "1";
    public static final String TRANSFERSTAT_CANCEL = "2";
    public static final String TRANSFERSTAT_HASCOMFIRM = "3";


    /**
     * //中台业务类型-分红
     */
    public static final String MBUSICODE_FH = "1143";
    /**
     * //中台业务类型-私募股权回款
     */
    public static final String MBUSICODE_SMGQHK = "1999";

    /**
     * 资产证明状态 "1";//有效"0";//无效
     */
    public static final String ASSET_CERT_YES = "1";
    public static final String ASSET_CERT_NO = "0";

    /**
     * 常规回访处理类型1待处理2再处理3已处理
     */
    public static final String REVIEW_DEALSTATE_WAIT = "1";
    public static final String REVIEW_DEALSTATE_REPEAT = "2";
    public static final String REVIEW_DEALSTATE_END = "3";

    /**
     * 常规回访是否接通1是;2否
     */
    public static final String REVIEW_ISCONNECT_Y = "1";
    public static final String REVIEW_ISCONNECT_N = "2";

    /**
     * 常规回访客户类型（1零存量客户、2高端存量客户、3潜在客户、4赎回客户）
     */
    public static final String REVIEW_CUSTTYPE_LCL = "1";
    public static final String REVIEW_CUSTTYPE_GDCL = "2";
    public static final String REVIEW_CUSTTYPE_QZ = "3";
    public static final String REVIEW_CUSTTYPE_SH = "4";

    /**
     * 常规回访是否问题件（1否、2是（非sop）、3是（类sop））
     */
    public static final String REVIEW_ISWRONG_NO = "1";
    public static final String REVIEW_ISWRONG_YES_NOTSOP = "2";
    public static final String REVIEW_ISWRONG_YES_CLASSSOP = "3";

    /**
     * 常规回访是否反馈（1是、2否）
     */
    public static final String REVIEW_ISFEEDBACK_YES = "1";
    public static final String REVIEW_ISFEEDBACK_NO = "2";

    /**
     * 新规回访是否完成"1";//完成"0";//未完成
     */
    public static final String VISIT_RESULT_COMPLET = "1";
    public static final String VISIT_RESULT_NOT_COMPLET = "0";

    /**
     * 日志类型//新规回访沟通记录
     */
    public static final String OPT_VISIT_RECORD = "1";
    /**
     * //分次CALL修改认缴金额
     */
    public static final String OPT_MANYCALL_UPT_TOTALAMT = "2";
    /**
     * //分次CALL份额转让修改认缴金额
     */
    public static final String OPT_MANYCALL_TRANSF_TOTALAMT = "3";
    /**
     * //好豆兑换货基
     */
    public static final String OPT_EXCHANGE_HAODOU = "4";
    /**
     * //投顾反馈
     */
    public static final String OPT_CONS_FEDBACK = "5";
    /**
     * //修改为无需双录
     */
    public static final String OPT_NOT_NEED_DOUBLE_TRADE = "6";


    /**
     * 双录回访处理标识
     * "0";//无需处理
     * "1";//待处理
     * "2";//审核通过
     * "3";//已处理待审核
     * "4";//审核不通过
     */
    public static final String DOUBLETRADE_HANDLEFLAG_NONEED = "0";
    public static final String DOUBLETRADE_HANDLEFLAG_NOHANDLE = "1";
    public static final String DOUBLETRADE_HANDLEFLAG_AUDITPASS = "2";
    public static final String DOUBLETRADE_HANDLEFLAG_HANDLED = "3";
    public static final String DOUBLETRADE_HANDLEFLAG_AUDITREJECT = "4";

    /**
     * 份额转让 新增分次call类型 "1";//补录分次CALL壳 "2";//新增实缴
     */
    public static final String TRANSFER_FCCLTYPE_REPAIR = "1";
    public static final String TRANSFER_FCCLTYPE_ADDNEW = "2";

    /**
     * 1代表 日常回访；
     2代表 客户归属；
     3代表 客户来源；
     4代表 投顾离职回访；
     5代表 专项回访；
     6代表 Leads回访
     */
    public static final String REVIEW_TYPE_RCHF = "1";
    public static final String REVIEW_TYPE_KHGS = "2";
    public static final String REVIEW_TYPE_KHLY = "3";
    public static final String REVIEW_TYPE_TGLZHF = "4";
    public static final String REVIEW_TYPE_ZXHF = "5";
    public static final String REVIEW_TYPE_LEADS = "6";

    /**
     * 变更后银行卡类型"1";//新卡；"2";//当前客户已绑定的银行卡
     */
    public static final String COUNTER_CHANGE_BANK_NEW = "1";
    public static final String COUNTER_CHANGE_BANK_OLD = "2";

    /**
     * na产品费用类型"1";//应收费用"2";//实收费用
     */
    public static final String NA_FEETYPE_RECEIVEFEE = "1";
    public static final String NA_FEETYPE_PAYFEE = "2";

    /**
     * 消息推送内容类型"1";//标题"2";//内容
     */
    public static final String PUSH_MSG_CONTENTTYPE_TITLE="1";
    public static final String PUSH_MSG_CONTENTTYPE_CONTENT="2";
    /**
     * 消息类型：
     * 1:产品分红
     * 2:产品到期
     * 3:团队产品到期
     * 4:客户生日
     * 5:到账通知
     * 6:净值通知
     * 7:预约通知
     * 8:资料更新
     * 9:交易类型 = 强赎
     * 10:交易类型 = 红利发放
     * 11:缴费提醒-投顾消息中心
     * 12:定制发送-投顾消息中心
     * 13:其他-投顾消息中心
     */
    public static final String MSG_TYPE_CPFH = "1";
    public static final String MSG_TYPE_CPDQ = "2";
    public static final String MSG_TYPE_TDDQ = "3";
    public static final String MSG_TYPE_KHSR = "4";
    public static final String MSG_TYPE_DZTZ = "5";
    public static final String MSG_TYPE_JZTZ = "6";
    public static final String MSG_TYPE_YYTZ = "7";
    public static final String MSG_TYPE_ZLGX = "8";
    public static final String MSG_TYPE_QS = "9";
    public static final String MSG_TYPE_HLFF = "10";
    public static final String MSG_PAYMENT_REMINDER = "11";
    public static final String MSG_TYPE_CUSTOM = "12";
    public static final String MSG_TYPE_OTHER = "13";

    /**
     * 消息推送状态：0待发送1已发送2已取消
     */
    public static final String MSG_PUSHFLAG_TOBESEND = "0";
    public static final String MSG_PUSHFLAG_HASSEND = "1";
    public static final String MSG_PUSHFLAG_HASCANCEL = "2";

    /**
     * NA产品收费类型 好买收费
     */
    public static final String NA_FEE_TYPE_HOWBUY="10201";
    /**
     * NA产品收费类型 管理人收费
     */
    public static final String NA_FEE_TYPE_MANAGE="0";

    /**
     * OTHER("1", "其他"), SIMU("2", "私募"),ASSERT_MANAGER("3", "资管");
     */
    public static final String PRODUCT_TYPE_OTHER = "1";
    public static final String PRODUCT_TYPE_SIMU = "2";
    public static final String PRODUCT_TYPE_ASSERT_MANAGER = "3";

    /**
     * 中台订单付款状态2，到账确认；5、退款；11、冻结成功；12、冻结失败；16、冻结支付中；17、冻结支付成功；18、冻结支付失败
     */
    public static final String TX_PMT_HASPAY = "2";
    public static final String TX_PMT_RETURNPAY = "5";
    public static final String TX_PMT_FREEZE_SUCCESS = "11";
    public static final String TX_PMT_FREEZE_FAIL = "12";
    public static final String TX_PMT_FREEZE_PAYING = "16";
    public static final String TX_PMT_FREEZE_PAYED = "17";
    public static final String TX_PMT_FREEZE_PAYFAIL = "18";

    /**
     * 双录特殊产品配置是否需要"1";//需要"2";//不需要
     */
    public static final String DOUBLE_TRADE_BLACK_NEED = "1";
    public static final String DOUBLE_TRADE_BLACK_NO_NEED = "2";

    /**
     * 产品风险等级
     */
    public static final String PRODUCT_RISK_R1 = "1";
    public static final String PRODUCT_RISK_R2 = "2";
    public static final String PRODUCT_RISK_R3 = "3";
    public static final String PRODUCT_RISK_R4 = "4";
    public static final String PRODUCT_RISK_R5 = "5";

    public static final String ZT_SUCCESS_FLAG = "0000000";

    public static final String CURRENCY_RMB = "156";

    public static final String AUTO_COMFIRM_PREBOOK = "投顾";

    /**
     * 1是 0否
     */
    public static final String YES = "1";
    public static final String NO = "0";


    /**
     * 性别1：男；0：女
     */
    public static final String GENDER_MAN = "1";
    public static final String GENDER_WOMEN = "0";

    /**
     * 保险-家庭成员与本人关系
     * 0:本人
     * 1：配偶
     * 2:父亲
     * 3：母亲
     * 4:配偶父亲
     * 5:配偶母亲
     * 6:子女
     */
    public static final String INSURE_RELATION_OWN = "0";
    public static final String INSURE_RELATION_SPOUSE = "1";
    public static final String INSURE_RELATION_FATHER = "2";
    public static final String INSURE_RELATION_MOTHER = "3";
    public static final String INSURE_RELATION_SPOUSE_FATHER = "4";
    public static final String INSURE_RELATION_SPOUSE_MOTHER = "5";
    public static final String INSURE_RELATION_CHILD = "6";

    /**
     * 保险-工作状态
     * "1";//未参加工作
     * "2";//正在工作
     * "3";//已退休
     */
    public static final String INSURE_UN_WORK = "1";
    public static final String INSURE_WORK = "2";
    public static final String INSURE_RETIRE = "3";

    /**
     * 敏感信息脱敏：下载标志1已下载、0未下载
     */
    public static final String SENSITIVE_USEFLAG_YES="1";
    public static final String SENSITIVE_USEFLAG_NO="0";

    /**
     * 敏感信息脱敏：脱敏主体1投顾客户号、2一账通号、3密文
     */
    public static final String SENSITIVE_SOURCE_TYPE_CUSTNO="1";
    public static final String SENSITIVE_SOURCE_TYPE_HBONENO="2";
    public static final String SENSITIVE_SOURCE_TYPE_TEXT="3";

    /**
     * 敏感信息脱敏：脱敏主体1姓名；2证件号码；3手机号；4邮箱；5固定电话；6地址；7银行卡号；8移动设备号
     */
    public static final String SENSITIVE_INFO_TYPE_CUSTNAME="1";
    public static final String SENSITIVE_INFO_TYPE_IDNO="2";
    public static final String SENSITIVE_INFO_TYPE_MOBILE="3";
    public static final String SENSITIVE_INFO_TYPE_EMAIL="4";
    public static final String SENSITIVE_INFO_TYPE_TELNO="5";
    public static final String SENSITIVE_INFO_TYPE_ADDR="6";
    public static final String SENSITIVE_INFO_TYPE_BANKNO="7";
    public static final String SENSITIVE_INFO_TYPE_YDSBH="8";

    /**
     * 消息管理类型 0-主类 1-子类
     */
    public static final String MSG_DATA_TYPE_MASTER = "0";
    public static final String MSG_DATA_TYPE_SUB = "1";
    /**
     * 消息管理类型是否启用 0-停用 1-启用
     */
    public static final String MSG_TYPE_USE_DISABLE = "0";
    public static final String MSG_TYPE_USE_ENABLE = "1";

    /**
     * 消息推送通道1、PC端；2、微信
     */
    public static final String PUSH_MSG_CHANNEL_PC = "1";
    public static final String PUSH_MSG_CHANNEL_WECHAT = "2";

    /**
     * 推送消息账户类型1、一账通号；2、投顾号
     */
    public static final String PUSH_MSG_ACCOUNT_CUST="1";
    public static final String PUSH_MSG_ACCOUNT_CONS="2";

    /**
     * 消息推送1、人工；2、系统
     */
    public static final String PUSH_MSG_SYS = "2";
    public static final String PUSH_MSG_PERSON = "1";

    public static final String ID_TYPE_SFZ = "0";

    /**
     * 汇总方式
     * "2";//按月
     * "3";//按日
     */
    public static final String EXPANSION_MONTH = "2";
    public static final String EXPANSION_DAY = "3";

    /**
     * 新规任务回访状态
     * "1"; // 待客服处理
     * "5"; // 待投顾处理
     * "2"; // 已处理
     */
    public static final String HANDLE_FLAG_WAIT_DEAL = "1";
    public static final String HANDLE_FLAG_WAIT_CONSULT_DEAL = "5";
    public static final String HANDLE_FLAG_HAS_DEAL = "2";
    /**
     * 已访状态
     * "0"; // 未回访
     * "1"; // 已一访
     * "2"; // 已二访
     * "3"; // 已三访
     */
    public static final String HAS_VISIT_STATUS_NOT_VISIT = "0";
    public static final String HAS_VISIT_STATUS_FIRST_VISIT = "1";
    public static final String HAS_VISIT_STATUS_SECOND_VISIT = "2";
    public static final String HAS_VISIT_STATUS_THIRD_VISIT = "3";

    /**
     * 1国内;0海外;10国内海外
     */
    public static final String IS_OVERSEAS_GN = "1";
    public static final String IS_OVERSEAS_HW = "0";
    public static final String IS_OVERSEAS_GN_HW = "10";

    /**
     * 包含外部资产方式
     * "0";//不包含
     * "1";//仅crm
     * "2";//仅APP
     * "3";//都包含
     */
    public static final String EXTERNAL_BALANCE_NO = "0";
    public static final String EXTERNAL_BALANCE_CRM = "1";
    public static final String EXTERNAL_BALANCE_APP = "2";
    public static final String EXTERNAL_BALANCE_ALL = "3";

    /**
     * 花名册常量 1:待审核；2：审核通过；3审核不通过
     */
    public static final String HMC_CHECK_WAIT = "1";
    public static final String HMC_CHECK_PASS = "2";
    public static final String HMC_CHECK_NOTPASS = "3";

    /**
     * 0:外扣；1：内扣
     */
    public static final String FEECALMODE_OUT = "0";
    public static final String FEECALMODE_IN = "1";

    /**
     * 包含外部资产方式
     * "0";//生效
     * "1";//解除
     * "2";//CRM状态，审核中
     * "3";//CRM状态，作废
     * "4";//CRM状态，解除中
     */
    public static final String RELATION_ADD = "0";
    public static final String RELATION_REMOVE = "1";
    public static final String RELATION_CRM_ADD = "2";
    public static final String RELATION_CRM_REMOVE = "3";
    public static final String RELATION_CRM_REMOVEING = "4";

    /**
     * 来源类型1：公司资源；2：投顾资源;3:公司-leads;4:公司-20w;5:自购;6:公司资源（划转）;7:公司资源（重复）;8:投顾资源（划转-潜在）;9:投顾资源（划转-成交）;10:投顾资源（潜在-重复）;11:投顾资源（成交-重复）
     */
    public static final String RESOURCE_TYPE_GSZY = "1";
    public static final String RESOURCE_TYPE_TGZY = "2";
    public static final String RESOURCE_TYPE_GSLEADS = "3";
    public static final String RESOURCE_TYPE_GS20W = "4";
    public static final String RESOURCE_TYPE_ZG = "5";
    public static final String RESOURCE_TYPE_GSZY_HZ = "6";
    public static final String RESOURCE_TYPE_GSZY_CF = "7";
    public static final String RESOURCE_TYPE_TGZY_HZ_QZ = "8";
    public static final String RESOURCE_TYPE_TGZY_HZ_CJ = "9";
    public static final String RESOURCE_TYPE_TGZY_QZ_CF = "10";
    public static final String RESOURCE_TYPE_TGZY_CJ_CF = "11";

    /**
     * 第一来源1：公司资源；2：投顾资源;
     */
    public static final String FIRST_SOURCE_TYPE_GSZY = "1";
    public static final String FIRST_SOURCE_TYPE_TGZY = "2";

    /**
     * 客户成交状态，1：成交；0：潜在
     */
    public static final String CUST_TRADE_TYPE_CJ = "1";
    public static final String CUST_TRADE_TYPE_QZ = "0";


    public static final String STR_TRUE="true";
    public static final String STR_FALSE="false";
    public static final String STR_OTHER="other";
    public static final String STR_UNKNOWN="unknown";

    /**
     * 中台定投状态01：待执行；02：执行中；03：主动终止；04：被动终止；05：定投结束
     */
    public static final String FIXED_STATE_ZT_DZX = "01";
    public static final String FIXED_STATE_ZT_ZXZ = "02";
    public static final String FIXED_STATE_ZT_ZDZZ = "03";
    public static final String FIXED_STATE_ZT_BDZZ = "04";
    public static final String FIXED_STATE_ZT_DTJS = "05";

    /**
     * CRM定投状态1待处理、2已生成定投计划、3已执行、4定投主动终止、5定投被动终止、6定投结束、9已撤销
     */
    public static final String FIXED_STATE_DZX = "1";
    public static final String FIXED_STATE_YSCDTJX = "2";
    public static final String FIXED_STATE_YZX = "3";
    public static final String FIXED_STATE_DTZDZZ = "4";
    public static final String FIXED_STATE_DTBDZZ = "5";
    public static final String FIXED_STATE_DTJS = "6";
    public static final String FIXED_STATE_DTCX = "9";

    /**
     * 重复客户划转处理状态   7-不申请 6-驳回-非重复客户 1-待处理 2-已处理-驳回 3-已处理-同意 4-处理中 5-已处理-重复申请无效
     */
    public static final String TRANSF_OPSTATUS_WAIT = "1";
    public static final String TRANSF_OPSTATUS_CANCEL = "2";
    public static final String TRANSF_OPSTATUS_AGREE = "3";
    public static final String TRANSF_OPSTATUS_DEAL = "4";
    public static final String TRANSF_OPSTATUS_HANDLED = "5";
    public static final String TRANSF_OPSTATUS_REJECT = "6";
    public static final String TRANSF_OPSTATUS_NONEED = "7";

    /**
     * 是否申请划转 0-关闭/不申请  1-申请划转
     */
    public static final String CRM_TRANSF_NO = "0";
    public static final String CRM_TRANSF_YES = "1";

    /**
     * 1	回访  2	协商  3	规则  4	放弃追踪
     */
    public static final String TRANSF_OPTYPE_RETURN = "1";
    public static final String TRANSF_OPTYPE_CONSULT = "2";
    public static final String TRANSF_OPTYPE_RULE = "3";
    public static final String TRANSF_OPTYPE_GIVEUP = "4";

    /**
     * 1申请划转 0关闭
     */
    public static final String CRM_LABEL_YES = "1";
    public static final String CRM_LABEL_NO = "0";

    /**
     * 1高端存量 2高端0存量，9个月内有交易 3曾经分配给申请投顾 4首次分配日期》6个月
     */
    public static final String CRM_HIGH = "1";
    public static final String CRM_HIGH_ZERO = "2";
    public static final String CRM_EQOLD_CONSCODE = "3";
    public static final String CRM_GREATER_SIX = "4";


    /**
     * 0：未处理；1：已处理；2：需人工处理；3：无需处理
     */
    public static final String HBONE_ABNORMAL_NOT_HANDLE = "0";
    public static final String HBONE_ABNORMAL_HAS_HANDLE = "1";
    public static final String HBONE_ABNORMAL_NEED_PERSON = "2";
    public static final String HBONE_ABNORMAL_NO_NEED = "3";

    public static final String SUCCESS_FOURZORE = "0000";

    /**
     * 柜台线上化-归档类型
     * "1";//未归档"2";//部分归档"3";//全部归档"4";//无需归档
     */
    public final static String ARCH_ALLNO = "1";
    public final static String ARCH_PARTNO = "2";
    public final static String ARCH_ALLYES = "3";
    public final static String ARCH_NONEED = "4";

    /**
     * //是否邮寄-1是;0否
     public final static String ISSIGN_YES = "1";
     public final static String ISSIGN_NO = "0";

     /**
     * 是否归档-1是;0否
     */
    public final static String ISARCH_YES = "1";
    public final static String ISARCH_NO = "0";

    /**
     * 资料管理-业务类型常量
     */
    /** 定投新增 */
    public static final String BUSI_INVEST_FIXED = "41";
    /** 定投终止 */
    public static final String BUSI_INVEST_CANCEL = "42";
	
}
