package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类BankSupervise.java
 * <AUTHOR> ********
 * @version 1.0
 * @created
 */
public class BankSuperviseNew implements Serializable {

	private static final long serialVersionUID = 1L;

	private String sysMonth; // 查询月份

	private Integer orderID; // 排序号

	private String qrySdt; // 自然月开始日期

	private String qryEdt; // 自然月结束日期

	private String regDisCode; // 开户机构

	private String fundType; // 基金类型

    private String hmcpx; // 好买产品线

	private Double floatAmt; // 浮动汇率金额

	private Double feeRate; // 费率

	private Double floatFee; // 浮动费用

	private Double fixedNum; // 固定笔数

	private Double fixedFee; // 固定费用

	private String redeemFeeType; // 出款分档 1. 0-5W 2.5W及以上

	private Double redeemNum; // 出款笔数

	private Double redeemFee; // 提现费用

	public String getSysMonth() {
		return sysMonth;
	}



	public void setSysMonth(String sysMonth) {
		this.sysMonth = sysMonth;
	}



	public Integer getOrderID() {
		return orderID;
	}



	public void setOrderID(Integer orderID) {
		this.orderID = orderID;
	}



	public String getQrySdt() {
		return qrySdt;
	}



	public void setQrySdt(String qrySdt) {
		this.qrySdt = qrySdt;
	}



	public String getQryEdt() {
		return qryEdt;
	}



	public void setQryEdt(String qryEdt) {
		this.qryEdt = qryEdt;
	}



	public String getRegDisCode() {
		return regDisCode;
	}



	public void setRegDisCode(String regDisCode) {
		this.regDisCode = regDisCode;
	}



	public String getFundType() {
		return fundType;
	}



	public void setFundType(String fundType) {
		this.fundType = fundType;
	}



	public Double getFloatAmt() {
		return floatAmt;
	}



	public void setFloatAmt(Double floatAmt) {
		this.floatAmt = floatAmt;
	}



	public Double getFeeRate() {
		return feeRate;
	}



	public void setFeeRate(Double feeRate) {
		this.feeRate = feeRate;
	}



	public Double getFloatFee() {
		return floatFee;
	}



	public void setFloatFee(Double floatFee) {
		this.floatFee = floatFee;
	}



	public Double getFixedNum() {
		return fixedNum;
	}



	public void setFixedNum(Double fixedNum) {
		this.fixedNum = fixedNum;
	}



	public Double getFixedFee() {
		return fixedFee;
	}



	public void setFixedFee(Double fixedFee) {
		this.fixedFee = fixedFee;
	}



	public String getRedeemFeeType() {
		return redeemFeeType;
	}



	public void setRedeemFeeType(String redeemFeeType) {
		this.redeemFeeType = redeemFeeType;
	}



	public Double getRedeemNum() {
		return redeemNum;
	}



	public void setRedeemNum(Double redeemNum) {
		this.redeemNum = redeemNum;
	}



	public Double getRedeemFee() {
		return redeemFee;
	}



	public void setRedeemFee(Double redeemFee) {
		this.redeemFee = redeemFee;
	}


    public String getHmcpx() {
        return hmcpx;
    }

    public void setHmcpx(String hmcpx) {
        this.hmcpx = hmcpx;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
