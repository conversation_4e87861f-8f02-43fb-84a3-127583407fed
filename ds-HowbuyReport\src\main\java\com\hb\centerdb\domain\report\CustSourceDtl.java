package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类ZSReport.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustSourceDtl implements Serializable {

	private static final long serialVersionUID = 1L;
    private String outletcode; // 部门code
	private String outletname; // 部门
	private String consname; // 投顾姓名
	private String concustno;// 投顾客户号
	private String concustname;// 客户姓名
	private String assignDate;// 客户分配时间
	private String assignState;// 客户分配状态
	private String isGd;// 是否高端成交
	private String currentState;// 客户当前状态
	private String currentStand;// 客户当前情况
	private String custSource;// 客户来源

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public String getOutletname() {
        return outletname;
    }

    public void setOutletname(String outletname) {
        this.outletname = outletname;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConcustno() {
        return concustno;
    }

    public void setConcustno(String concustno) {
        this.concustno = concustno;
    }

    public String getConcustname() {
        return concustname;
    }

    public void setConcustname(String concustname) {
        this.concustname = concustname;
    }

    public String getAssignDate() {
        return assignDate;
    }

    public void setAssignDate(String assignDate) {
        this.assignDate = assignDate;
    }

    public String getAssignState() {
        return assignState;
    }

    public void setAssignState(String assignState) {
        this.assignState = assignState;
    }

    public String getIsGd() {
        return isGd;
    }

    public void setIsGd(String isGd) {
        this.isGd = isGd;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getCurrentStand() {
        return currentStand;
    }

    public void setCurrentStand(String currentStand) {
        this.currentStand = currentStand;
    }

    public String getCustSource() {
        return custSource;
    }

    public void setCustSource(String custSource) {
        this.custSource = custSource;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
