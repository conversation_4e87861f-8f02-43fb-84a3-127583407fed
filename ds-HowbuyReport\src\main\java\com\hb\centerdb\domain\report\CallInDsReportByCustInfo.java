package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;

public class CallInDsReportByCustInfo implements Serializable {


    private static final long serialVersionUID = 7307830745347807404L;
    private String outletName ;
    private String u2name ;  //区域
	private String consName ; 
	private String consCustNo ; 
	private String consCustName ; 
	private String assignDt ; //分配日期
	private String assignTm ; //分配日期
	private String firstContactDt ; // 首次电话联系日期
	private String firstContactTm ; // 首次电话联系时间
	private BigDecimal contactDay = new BigDecimal(0); //电话联系周期
	private String isContackFlag = "0"; //是否有电话联系,1-是,0-否
	private String firstMeetDt ; //首次见面日期
	private BigDecimal meetDay = new BigDecimal(0) ; //见面周期
	private String isMeetFlag = "0"; //是否有会面,1-是,0-否
	private String firstTradeDt ; //首次成交日期
    private String firstTradeCons ; //首次成交投顾
    private String firstTradeOrg ; //首次成交部门
	private BigDecimal tradeDay = new BigDecimal(0); //成交周期
	private String isTradeFlag = "0";//是否有成交,1-是,0-否
	
	private String istimedeal;
	
	private BigDecimal firstMonthTrade = new BigDecimal(0); //首月成交数
	private BigDecimal threeMonthTrade = new BigDecimal(0); //三月成交数
	private BigDecimal sixMonthTrade = new BigDecimal(0); 	//六月成交数
	
	private String source ;
	
	public String getOutletName() {
		return outletName;
	}
	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}

    public String getU2name() {
        return u2name;
    }

    public void setU2name(String u2name) {
        this.u2name = u2name;
    }

    public String getConsName() {
		return consName;
	}
	public String getConsCustName() {
		return consCustName;
	}
	public void setConsCustName(String consCustName) {
		this.consCustName = consCustName;
	}
	public String getAssignDt() {
		return assignDt;
	}
	public void setAssignDt(String assignDt) {
		this.assignDt = assignDt;
	}
	public String getAssignTm() {
		return assignTm;
	}
	public void setAssignTm(String assignTm) {
		this.assignTm = assignTm;
	}
	public String getFirstContactDt() {
		return firstContactDt;
	}
	public void setFirstContactDt(String firstContactDt) {
		this.firstContactDt = firstContactDt;
	}
	public String getFirstContactTm() {
		return firstContactTm;
	}
	public void setFirstContactTm(String firstContactTm) {
		this.firstContactTm = firstContactTm;
	}
	public String getIsContackFlag() {
		return isContackFlag;
	}
	public void setIsContackFlag(String isContackFlag) {
		this.isContackFlag = isContackFlag;
	}
	public String getFirstMeetDt() {
		return firstMeetDt;
	}
	public void setFirstMeetDt(String firstMeetDt) {
		this.firstMeetDt = firstMeetDt;
	}
	public String getIsMeetFlag() {
		return isMeetFlag;
	}
	public void setIsMeetFlag(String isMeetFlag) {
		this.isMeetFlag = isMeetFlag;
	}
	public String getFirstTradeDt() {
		return firstTradeDt;
	}
	public void setFirstTradeDt(String firstTradeDt) {
		this.firstTradeDt = firstTradeDt;
	}
	public BigDecimal getContactDay() {
		return contactDay;
	}
	public void setContactDay(BigDecimal contactDay) {
		this.contactDay = contactDay;
	}
	public BigDecimal getMeetDay() {
		return meetDay;
	}
	public void setMeetDay(BigDecimal meetDay) {
		this.meetDay = meetDay;
	}
	public BigDecimal getTradeDay() {
		return tradeDay;
	}
	public void setTradeDay(BigDecimal tradeDay) {
		this.tradeDay = tradeDay;
	}
	public String getIsTradeFlag() {
		return isTradeFlag;
	}
	public void setIsTradeFlag(String isTradeFlag) {
		this.isTradeFlag = isTradeFlag;
	}
	public BigDecimal getFirstMonthTrade() {
		return firstMonthTrade;
	}
	public void setFirstMonthTrade(BigDecimal firstMonthTrade) {
		this.firstMonthTrade = firstMonthTrade;
	}
	public BigDecimal getThreeMonthTrade() {
		return threeMonthTrade;
	}
	public void setThreeMonthTrade(BigDecimal threeMonthTrade) {
		this.threeMonthTrade = threeMonthTrade;
	}
	public BigDecimal getSixMonthTrade() {
		return sixMonthTrade;
	}
	public void setSixMonthTrade(BigDecimal sixMonthTrade) {
		this.sixMonthTrade = sixMonthTrade;
	}
	
	public String getConsCustNo() {
		return consCustNo;
	}
	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}
	public void setConsName(String consName) {
		this.consName = consName;
	}
	public String getSource() {
		if("CALL".equals(source)){
			return "呼入";
		}else if("WEB".equals(source)){
			return "网站留痕";
		}else if("ifeng".equals(source)){
			return "凤凰";
		}else if("RSWAJUE".equals(source)){
			return "RS转高端-挖掘";
		}else if("RSKEHU".equals(source)){
			return "RS转高端-客户";
		}else{
			return source;
		}
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getIstimedeal() {
		return istimedeal;
	}
	public void setIstimedeal(String istimedeal) {
		this.istimedeal = istimedeal;
	}

    public String getFirstTradeCons() {
        return firstTradeCons;
    }

    public void setFirstTradeCons(String firstTradeCons) {
        this.firstTradeCons = firstTradeCons;
    }

    public String getFirstTradeOrg() {
        return firstTradeOrg;
    }

    public void setFirstTradeOrg(String firstTradeOrg) {
        this.firstTradeOrg = firstTradeOrg;
    }
}
