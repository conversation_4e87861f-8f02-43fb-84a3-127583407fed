/**
 * 2013-02-28 <PERSON>.He 邮件服务分两级
 */
package com.hb.crm.constant.help;


/**
 * <AUTHOR>
 *
 */
public class TxConstant extends Constant {

	public static final String APPCODE_FD = "20";
	public static final String APPCODE_BANK = "21";//bank交易的appcode
	public static final String APPCODE_FDB = "25";//前台调用基金数据库
	public static final String APPCODE_CM = "30";
	public static final String APPCODE_CRM = "40";
	public static final String APPCODE_CP = "10";
	public static final String APPCODE_NOTIFYCATION = "50";
	// 2013-02-28 Matthew.He 邮件服务分两级
	public static final String APPCODE_NOTIFYCATION_SERVER = "51"; //发送服务第二级(邮件)
	public static final String APPCODE_WEB = "60";
	public static final String APPCODE_SMG = "70";
	/**
	 * 新版订阅应用代码
	 */
	public static final String APPCODE_BOOK = "80";
	
	/**
	 * web端FDB应用代码
	 */
	public static final String APPCODE_WEB_FDB = "90";
	
	/**
	 * 交易活动应用代码
	 */
	public static final String APPCODE_TRADE_HUODONG = "100";
	
	

	public static final String OPERCODE_DIRECT = "00";		// 自助
	public static final String OPERCODE_SAVE = "01";		// 保存
	public static final String OPERCODE_SUBMIT = "02";		// 提交
	public static final String OPERCODE_WITHDRAW = "03";	// 撤销提交
	public static final String OPERCODE_CANCEL = "04";		// 废除
	public static final String OPERCODE_REJECT = "05";		// 驳回
	public static final String OPERCODE_APPROVE = "06";		// 审核通过
	public static final String OPERCODE_DISAPPROVE = "07";	// 审核不通过


	public static final String PAPER_ORDER = "1";//纸质成单
	public static final String ONLINE_ORDER = "2";//电子成单
	public static final String NOPAPER_ORDER="3";//无纸化成单
	public static final String ABONOMAL_ORDER = "4";//异常流程

	public static final String HOPE_ONLINE_TRADE = "1"; //电子交易
	public static final String HOPE_PRIVATE_TRADE = "2"; //线下交易

	public static final String ZT_COUNTER = "1"; //中台柜台
	public static final String ZT_ONLINE = "2"; //中台 电子成单
	public static final String ZT_NONEPAPER = "3"; //中台 无纸化


}
