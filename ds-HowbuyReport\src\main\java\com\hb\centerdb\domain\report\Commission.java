package com.hb.centerdb.domain.report;

import java.io.Serializable;


/**
 * @Description: 实体类Commission.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Commission implements Serializable {

private static final long serialVersionUID = 1L;

	private String taCode; //ta代码
	
	private String taName; //ta名称
	
	private String fundManCode;//基金公司代码
	
	private String fundManName;//基金公司名称
	
	private String fundCode;//基金代码
	
	private String fundName;//基金名称
	
	private String fundType;//基金类型
	
	private String fundTypeName;//基金类型名称
	
	private Double ipoFee=0d;//认购费
	
	private Double applyFee=0d;//申购费
	
	private Double redeemFee=0d;//赎回费
	
	private Double transferFee=0d;//转换费
	
	private Double backApplyFee=0d;//后端认/申购费
	
	private Double backSpotApplyFee=0d;//后端垫付认/申购费
	
	private Double totleFee=0d;//总手续费

	public String getTaCode() {
		return taCode;
	}



	public void setTaCode(String taCode) {
		this.taCode = taCode;
	}



	public String getTaName() {
		return taName;
	}



	public void setTaName(String taName) {
		this.taName = taName;
	}



	public String getFundManCode() {
		return fundManCode;
	}



	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}



	public String getFundManName() {
		return fundManName;
	}



	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}



	public String getFundCode() {
		return fundCode;
	}



	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}



	public String getFundName() {
		return fundName;
	}



	public void setFundName(String fundName) {
		this.fundName = fundName;
	}



	public String getFundType() {
		return fundType;
	}



	public void setFundType(String fundType) {
		this.fundType = fundType;
	}



	public String getFundTypeName() {
		return fundTypeName;
	}



	public void setFundTypeName(String fundTypeName) {
		this.fundTypeName = fundTypeName;
	}



	public Double getIpoFee() {
		return ipoFee;
	}



	public void setIpoFee(Double ipoFee) {
		this.ipoFee = ipoFee;
	}



	public Double getApplyFee() {
		return applyFee;
	}



	public void setApplyFee(Double applyFee) {
		this.applyFee = applyFee;
	}



	public Double getRedeemFee() {
		return redeemFee;
	}



	public void setRedeemFee(Double redeemFee) {
		this.redeemFee = redeemFee;
	}



	public Double getTransferFee() {
		return transferFee;
	}



	public void setTransferFee(Double transferFee) {
		this.transferFee = transferFee;
	}



	public Double getBackApplyFee() {
		return backApplyFee;
	}



	public void setBackApplyFee(Double backApplyFee) {
		this.backApplyFee = backApplyFee;
	}



	public Double getBackSpotApplyFee() {
		return backSpotApplyFee;
	}



	public void setBackSpotApplyFee(Double backSpotApplyFee) {
		this.backSpotApplyFee = backSpotApplyFee;
	}



	public Double getTotleFee() {
		return totleFee;
	}



	public void setTotleFee(Double totleFee) {
		this.totleFee = totleFee;
	}



	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
