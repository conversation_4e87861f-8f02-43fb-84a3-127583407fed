/**
 * Create at 2010-4-23 by <PERSON>
 *
 * Copyright 2008, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd. 
 * All right reserved.
 *
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT 
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.hb.crm.constant.help;

/**
 * 投顾客户和网站客户是否已建立关联
 *
 * <AUTHOR>
 * @create 2010-4-23
 */
public enum IsRelated {

	/**
	 * 主关联
	 */
	Related("1"),
	/**
	 * 非主关联
	 */
	Unrelated("0")
	;
	
	private String value;

    public String getValue() {
        return value;
    }
    
    IsRelated(String value) {
    	this.value = value;
    }

}
