package com.hb.crm.domain.prosale;

import java.io.Serializable;


/**
 * @Description: 实体类Custprivatefund.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class Custprivatefund implements Serializable {

private static final long serialVersionUID = 1L;

	private String custno;
	
	private String fundcode;
	
	private Double balancevol = 0.0;
	
	private Double totalackamt = 0.0;
	
	private String tradedt;
	
	private Double nav = 0.0;
	
	private Double totalcost = 0.0;
	
	private Double profit = 0.0;
	
	private Double profitper = 0.0;
	
	private String recstat;
	
	private String checkflag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String credt;
	
	private String moddt;
	
	private String conscustrid;
	
	private String mgrcode;
	
	private Double divideamt = 0.0;
	
	private Double profited = 0.0;
	
	private Double divamt = 0.0;//当前份额已实现分红
	
	//客户持有展示时需要的字段
	private String fundname;//基金名称
	private String company;//基金公司
	private String hbzl;//货币种类
	private String rCustName;//联系人
	private Double sz;//市值
	private String pj;//评级
	private String pjbd;//评级变动
	private Double perCast;//当前单位成本
	
	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}
	
	public String getFundcode() {
		return this.fundcode;
	}

	public void setFundcode(String fundcode) {
		this.fundcode = fundcode;
	}
	
	public Double getBalancevol() {
		return this.balancevol;
	}

	public void setBalancevol(Double balancevol) {
		this.balancevol = balancevol;
	}
	
	public Double getTotalackamt() {
		return this.totalackamt;
	}

	public void setTotalackamt(Double totalackamt) {
		this.totalackamt = totalackamt;
	}
	
	public String getTradedt() {
		return this.tradedt;
	}

	public void setTradedt(String tradedt) {
		this.tradedt = tradedt;
	}
	
	public Double getNav() {
		return this.nav;
	}

	public void setNav(Double nav) {
		this.nav = nav;
	}
	
	public Double getTotalcost() {
		return this.totalcost;
	}

	public void setTotalcost(Double totalcost) {
		this.totalcost = totalcost;
	}
	
	public Double getProfit() {
		return this.profit;
	}

	public void setProfit(Double profit) {
		this.profit = profit;
	}
	
	public Double getProfitper() {
		return this.profitper;
	}

	public void setProfitper(Double profitper) {
		this.profitper = profitper;
	}
	
	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}
	
	public String getCheckflag() {
		return this.checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}
	
	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	
	public String getConscustrid() {
		return this.conscustrid;
	}

	public void setConscustrid(String conscustrid) {
		this.conscustrid = conscustrid;
	}
	
	public String getMgrcode() {
		return this.mgrcode;
	}

	public void setMgrcode(String mgrcode) {
		this.mgrcode = mgrcode;
	}
	
	public Double getDivideamt() {
		return this.divideamt;
	}

	public void setDivideamt(Double divideamt) {
		this.divideamt = divideamt;
	}
	
	public Double getProfited() {
		return this.profited;
	}

	public void setProfited(Double profited) {
		this.profited = profited;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getFundname() {
	return fundname;
}

public void setFundname(String fundname) {
	this.fundname = fundname;
}

public String getCompany() {
	return company;
}

public void setCompany(String company) {
	this.company = company;
}

public String getHbzl() {
	return hbzl;
}

public void setHbzl(String hbzl) {
	this.hbzl = hbzl;
}

public String getrCustName() {
	return rCustName;
}

public void setrCustName(String rCustName) {
	this.rCustName = rCustName;
}

public Double getSz() {
	return sz;
}

public void setSz(Double sz) {
	this.sz = sz;
}

public String getPj() {
	return pj;
}

public void setPj(String pj) {
	this.pj = pj;
}

public String getPjbd() {
	return pjbd;
}

public void setPjbd(String pjbd) {
	this.pjbd = pjbd;
}

public Double getPerCast() {
	return perCast;
}

public void setPerCast(Double perCast) {
	this.perCast = perCast;
}

public Double getDivamt() {
	return divamt;
}

public void setDivamt(Double divamt) {
	this.divamt = divamt;
}
}
