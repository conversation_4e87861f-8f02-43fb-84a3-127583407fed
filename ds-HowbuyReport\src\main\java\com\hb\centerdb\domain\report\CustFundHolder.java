package com.hb.centerdb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SvcFee.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CustFundHolder implements Serializable {


    private static final long serialVersionUID = -4822500294127639061L;

    private String custNo; // 客户号

	private String custName; // 客户名称

	private String idNo; // 证件号码

    private String idType; // 证件类型

    private String riskToleranceLevel; //客户风险级别

    private String riskToleranceTerm; // 客户风险级别有效期

    private String txAcctNo; // 基金交易账号

    private String disCode; // 分销机构code

    private String disCodeName; // 分销机构名字

    private String fundCode; // 基金代码

	private String fundType; // 基金类型
	
	private String fundName; // 基金名称

    private String taAbbr; //基金公司简称

    private String fundAcctNo; //基金账号

	private Double balanceVol = 0d; // 总持有份额

    private Double availVol = 0d; // 可用份额

	private Double marketAmt = 0d; // 基金市值

    private String invstType; // 投资者类别

    private String riskName; // 客户反洗钱风险等级

	private Double smamt = 0d; // 申购净金额

    private String fundRiskLevel; // 产品风险等级

    private String regDt; // 份额登记日期

    private String lockEndDt; // 份额锁定结束日期

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getRiskToleranceLevel() {
        return riskToleranceLevel;
    }

    public void setRiskToleranceLevel(String riskToleranceLevel) {
        this.riskToleranceLevel = riskToleranceLevel;
    }

    public String getRiskToleranceTerm() {
        return riskToleranceTerm;
    }

    public void setRiskToleranceTerm(String riskToleranceTerm) {
        this.riskToleranceTerm = riskToleranceTerm;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getDisCodeName() {
        return disCodeName;
    }

    public void setDisCodeName(String disCodeName) {
        this.disCodeName = disCodeName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getTaAbbr() {
        return taAbbr;
    }

    public void setTaAbbr(String taAbbr) {
        this.taAbbr = taAbbr;
    }

    public String getFundAcctNo() {
        return fundAcctNo;
    }

    public void setFundAcctNo(String fundAcctNo) {
        this.fundAcctNo = fundAcctNo;
    }

    public Double getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(Double balanceVol) {
        this.balanceVol = balanceVol;
    }

    public Double getAvailVol() {
        return availVol;
    }

    public void setAvailVol(Double availVol) {
        this.availVol = availVol;
    }

    public Double getMarketAmt() {
        return marketAmt;
    }

    public void setMarketAmt(Double marketAmt) {
        this.marketAmt = marketAmt;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    public Double getSmamt() {
        return smamt;
    }

    public void setSmamt(Double smamt) {
        this.smamt = smamt;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }

    public String getLockEndDt() {
        return lockEndDt;
    }

    public void setLockEndDt(String lockEndDt) {
        this.lockEndDt = lockEndDt;
    }

    public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
