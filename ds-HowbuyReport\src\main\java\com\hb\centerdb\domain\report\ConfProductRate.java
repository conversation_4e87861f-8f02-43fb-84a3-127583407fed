package com.hb.centerdb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 针对财务固收二级产品费用计算费率用
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class ConfProductRate implements Serializable {


    private static final long serialVersionUID = 626911784190282028L;

    /**
     * @Fields taskId : taskid
     */
    private String taskId;//主键

    private String fundCode;//基金code

    private String fundName;//基金名称

    private String valueStartDate;//产品起息日

    private String valueEndDate;//产品到期日

    private String countDay;//计费天数

    private String yearDay;//年化天数

    private BigDecimal consultRate = new BigDecimal(0);// 咨询费率

    private BigDecimal manageRate = new BigDecimal(0);// 管理费率

    private BigDecimal redemRate = new BigDecimal(0);// 赎回费率

    private String redemDay;//赎回天数

    private String consultFormula;//咨询费公式

    private String manageFormula;//管理费公式

    private String redemFormula;//赎回费公式

    private BigDecimal adjustAmount = new BigDecimal(0);// 调整金额

    private String selfCust;//自有客户

    private String agarement;//协议主体

    private String settleStartDate;//结算起始日期

    private String settleEndDate;//结算结束日期

    private String remark;//备注

    private String reviewState;//审核状态

    private Date reviewtime;//审核时间

    private String reviewer;//审核人

    private String creator;//创建者

    private Date createtime;//创建时间

    private String updater;//更新者

    private Date updattime;//更新时间


    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getValueStartDate() {
        return valueStartDate;
    }

    public void setValueStartDate(String valueStartDate) {
        this.valueStartDate = valueStartDate;
    }

    public String getValueEndDate() {
        return valueEndDate;
    }

    public void setValueEndDate(String valueEndDate) {
        this.valueEndDate = valueEndDate;
    }

    public String getCountDay() {
        return countDay;
    }

    public void setCountDay(String countDay) {
        this.countDay = countDay;
    }

    public String getYearDay() {
        return yearDay;
    }

    public void setYearDay(String yearDay) {
        this.yearDay = yearDay;
    }

    public BigDecimal getConsultRate() {
        return consultRate;
    }

    public void setConsultRate(BigDecimal consultRate) {
        this.consultRate = consultRate;
    }

    public BigDecimal getManageRate() {
        return manageRate;
    }

    public void setManageRate(BigDecimal manageRate) {
        this.manageRate = manageRate;
    }

    public BigDecimal getRedemRate() {
        return redemRate;
    }

    public void setRedemRate(BigDecimal redemRate) {
        this.redemRate = redemRate;
    }

    public String getRedemDay() {
        return redemDay;
    }

    public void setRedemDay(String redemDay) {
        this.redemDay = redemDay;
    }

    public String getConsultFormula() {
        return consultFormula;
    }

    public void setConsultFormula(String consultFormula) {
        this.consultFormula = consultFormula;
    }

    public String getManageFormula() {
        return manageFormula;
    }

    public void setManageFormula(String manageFormula) {
        this.manageFormula = manageFormula;
    }

    public String getRedemFormula() {
        return redemFormula;
    }

    public void setRedemFormula(String redemFormula) {
        this.redemFormula = redemFormula;
    }

    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    public String getSelfCust() {
        return selfCust;
    }

    public void setSelfCust(String selfCust) {
        this.selfCust = selfCust;
    }

    public String getAgarement() {
        return agarement;
    }

    public void setAgarement(String agarement) {
        this.agarement = agarement;
    }

    public String getSettleStartDate() {
        return settleStartDate;
    }

    public void setSettleStartDate(String settleStartDate) {
        this.settleStartDate = settleStartDate;
    }

    public String getSettleEndDate() {
        return settleEndDate;
    }

    public void setSettleEndDate(String settleEndDate) {
        this.settleEndDate = settleEndDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReviewState() {
        return reviewState;
    }

    public void setReviewState(String reviewState) {
        this.reviewState = reviewState;
    }

    public Date getReviewtime() {
        return reviewtime;
    }

    public void setReviewtime(Date reviewtime) {
        this.reviewtime = reviewtime;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getUpdattime() {
        return updattime;
    }

    public void setUpdattime(Date updattime) {
        this.updattime = updattime;
    }
}
